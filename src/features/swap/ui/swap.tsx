/* eslint-disable max-lines-per-function */
'use client';
import { Icons } from '@assets';
import { ETHEREUM_NATIVE_ADDRESS } from '@shared/constants/ethereum';
import { useBottomSheetStore } from '@entities/bottom-sheet';
import { SelectWallet } from '@features/select-wallet';
import { App, Token } from '@types';
import { Button, SwapForm } from '@ui';
import { useAccount } from 'wagmi';
import { Address } from 'viem';
import React, { FC, useCallback, useMemo, useState } from 'react';

import {
    useEthereumBalances,
    useEthereumAmountOut,
    useEvmSwap,
} from '../hooks';
import styles from './swap.module.scss';

interface ISwapProps {
    tokenFrom?: Token;
    setTokenFrom: (token: Token) => void;
    tokenTo?: Token;
    setTokenTo: (token: Token) => void;
    app: App;
    isLoading?: boolean;
    assets?: Token[] | undefined;
    customColor?: string;
}

export const Swap: FC<ISwapProps> = ({
    tokenFrom,
    setTokenFrom,
    tokenTo,
    setTokenTo,
    isLoading,
    customColor,
    app,
}) => {
    const { address, isConnected } = useAccount();
    const [amount, setAmount] = useState('');

    // Convert Token addresses to Address type, treating native as ETH
    const tokenFromAddress: Address | undefined = useMemo(() => {
        if (!tokenFrom) return undefined;
        if (tokenFrom.type === 'native') return ETHEREUM_NATIVE_ADDRESS;
        return (tokenFrom.mintAddress || app.token?.contractAddress) as Address;
    }, [tokenFrom, app.token]);

    const tokenToAddress: Address | undefined = useMemo(() => {
        if (!tokenTo) return undefined;
        if (tokenTo.type === 'native') return ETHEREUM_NATIVE_ADDRESS;
        return (tokenTo.mintAddress || app.token?.contractAddress) as Address;
    }, [tokenTo, app.token]);

    // Use Ethereum hooks for balances
    const { tokenFromBalance, tokenToBalance } = useEthereumBalances({
        tokenFromAddress,
        tokenToAddress,
    });

    const tradeType = useMemo(
        () => (tokenFrom?.type === 'native' ? 'buy' : 'sell'),
        [tokenFrom],
    );

    // Use Ethereum amount estimation
    const { estimatedAmountOut, isLoading: isEstimateLoading } =
        useEthereumAmountOut({
            tokenFromAddress,
            tokenToAddress,
            amount,
            isEnabled: !!amount && parseFloat(amount) > 0,
        });

    // Use EVM swap functionality
    const {
        executeSwap,
        needsApproval,
        executeApproval,
        isLoading: isSwapLoading,
        isApproving,
    } = useEvmSwap({
        tokenAddress:
            (tradeType === 'sell' ? tokenFromAddress : tokenToAddress) || null,
        amount,
        tradeType,
        enabled: !!amount && parseFloat(amount) > 0,
    });

    // Check if user has sufficient balance
    const hasSufficientBalance = useMemo(() => {
        if (!amount) return true;
        return parseFloat(tokenFromBalance.toString()) >= parseFloat(amount);
    }, [amount, tokenFromBalance]);

    // Use the estimated amount out from Ethereum hooks
    const finalAmountOut = estimatedAmountOut || '0';
    const finalIsLoading = isEstimateLoading;

    const handleSwitchTokens = useCallback(() => {
        if (tokenFrom && tokenTo) {
            setTokenFrom(tokenTo);
            setTokenTo(tokenFrom);
        }
    }, [tokenFrom, tokenTo, setTokenFrom, setTokenTo]);

    const { show, close } = useBottomSheetStore();

    const connectClickHandler = useCallback(() => {
        show({
            title: 'Wallet',
            customContent: <SelectWallet onSelect={close} />,
        });
    }, [show, close]);

    const swapClickHandler = useCallback(async () => {
        try {
            if (needsApproval && tradeType === 'sell') {
                await executeApproval();
            } else {
                await executeSwap();
            }
        } catch (error) {
            console.error('Swap failed:', error);
        }
    }, [executeSwap, executeApproval, needsApproval, tradeType]);

    const isError = useMemo(
        () => !hasSufficientBalance,
        [hasSufficientBalance],
    );

    const buttonContent = useMemo(() => {
        if (isSwapLoading || isApproving) {
            return (
                <Button
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                    disabled
                >
                    {isApproving ? 'Approving' : 'Processing'}{' '}
                    <Icons.Loader style={{ marginLeft: 8 }} />
                </Button>
            );
        }

        if (!isConnected || !address) {
            return (
                <Button
                    onClick={connectClickHandler}
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Connect Wallet
                </Button>
            );
        }

        if (needsApproval && tradeType === 'sell') {
            return (
                <Button
                    onClick={swapClickHandler}
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                >
                    Approve Token
                </Button>
            );
        }

        if (isError) {
            return (
                <Button
                    disabled
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Insufficient balance
                </Button>
            );
        }

        if (Number(amount) < 0.0001) {
            return (
                <Button
                    view="app-action"
                    size="xl"
                    borderRadius="xxl"
                    full
                    disabled
                >
                    Minimum amount is 0.01
                </Button>
            );
        }

        if (amount === '0') {
            return (
                <Button
                    disabled
                    view="action"
                    size="xl"
                    borderRadius="xxl"
                    style={{
                        background: customColor,
                        color: 'var(--color-app-accent-text)',
                    }}
                    full
                >
                    Enter the amount
                </Button>
            );
        }

        return (
            <Button
                onClick={swapClickHandler}
                view={tradeType === 'buy' ? 'green' : 'danger'}
                size="xl"
                borderRadius="xxl"
                full
            >
                {tradeType === 'buy' ? 'Buy' : 'Sell'}
            </Button>
        );
    }, [
        swapClickHandler,
        connectClickHandler,
        tradeType,
        customColor,
        isError,
        amount,
        isConnected,
        address,
        isSwapLoading,
        isApproving,
        needsApproval,
    ]);

    return (
        <div className={styles.container}>
            <SwapForm
                fromAmount={amount}
                setFromAmount={setAmount}
                isEstimateLoading={finalIsLoading}
                toAmount={finalAmountOut || ''}
                tokenTo={tokenTo}
                tokenFrom={tokenFrom}
                onSwitchTokens={handleSwitchTokens}
                balanceFrom={tokenFromBalance.toString()}
                balanceTo={tokenToBalance.toString()}
                isLoading={isLoading}
                customColor={customColor}
            />
            {buttonContent}
        </div>
    );
};
