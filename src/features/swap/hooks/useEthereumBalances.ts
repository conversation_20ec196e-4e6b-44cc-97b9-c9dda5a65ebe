import { useAccount, useBalance, useReadContract } from 'wagmi';
import { formatUnits, Address } from 'viem';
import { ERC20_ABI, ETHEREUM_NATIVE_ADDRESS } from '@shared/constants';

interface UseEthereumBalancesProps {
    tokenFromAddress?: Address;
    tokenToAddress?: Address;
}

export const useEthereumBalances = ({ 
    tokenFromAddress, 
    tokenToAddress 
}: UseEthereumBalancesProps) => {
    const { address } = useAccount();

    // ETH balance
    const { data: ethBalance } = useBalance({
        address,
    });

    // From token balance
    const { data: fromTokenBalance } = useReadContract({
        address: tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenFromAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: address ? [address] : undefined,
        query: {
            enabled: !!address && !!tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // From token decimals
    const { data: fromTokenDecimals } = useReadContract({
        address: tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenFromAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'decimals',
        query: {
            enabled: !!tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // To token balance
    const { data: toTokenBalance } = useReadContract({
        address: tokenToAddress && tokenToAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenToAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: address ? [address] : undefined,
        query: {
            enabled: !!address && !!tokenToAddress && tokenToAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // To token decimals
    const { data: toTokenDecimals } = useReadContract({
        address: tokenToAddress && tokenToAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenToAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'decimals',
        query: {
            enabled: !!tokenToAddress && tokenToAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // Format from token balance
    const tokenFromBalance = (() => {
        if (tokenFromAddress === ETHEREUM_NATIVE_ADDRESS || !tokenFromAddress) {
            return ethBalance ? formatUnits(ethBalance.value, 18) : '0';
        }
        if (fromTokenBalance) {
            const decimals = fromTokenDecimals || 18;
            return formatUnits(BigInt(fromTokenBalance.toString()), decimals);
        }
        return '0';
    })();

    // Format to token balance
    const tokenToBalance = (() => {
        if (tokenToAddress === ETHEREUM_NATIVE_ADDRESS || !tokenToAddress) {
            return ethBalance ? formatUnits(ethBalance.value, 18) : '0';
        }
        if (toTokenBalance) {
            const decimals = toTokenDecimals || 18;
            return formatUnits(BigInt(toTokenBalance.toString()), decimals);
        }
        return '0';
    })();

    return {
        tokenFromBalance,
        tokenToBalance,
        isLoading: false, // All queries handle their own loading states
    };
};
