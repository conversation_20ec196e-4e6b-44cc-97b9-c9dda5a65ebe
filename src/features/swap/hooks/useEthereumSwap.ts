import { useCallback, useState } from 'react';
import { 
    useAccount, 
    usePublicClient, 
    useWalletClient,
    useBalance,
    useReadContract,
    useWriteContract,
    useWaitForTransactionReceipt 
} from 'wagmi';
import { parseEther, formatUnits, Address, erc20Abi } from 'viem';
import { SODA_FACTORY_ADDRESS, ERC20_ABI, MAX_UINT256, ETHEREUM_NATIVE_ADDRESS } from '@shared/constants';
import SodaFactoryABI from '@shared/abi/SodaFactoryABI.json';

interface UseEthereumSwapProps {
    tokenFromAddress?: Address;
    tokenToAddress?: Address;
    amount: string;
}

export const useEthereumSwap = ({ 
    tokenFromAddress, 
    tokenToAddress, 
    amount 
}: UseEthereumSwapProps) => {
    const { address } = useAccount();
    const { data: walletClient } = useWalletClient();
    const publicClient = usePublicClient();
    const { writeContractAsync } = useWriteContract();
    
    const [isLoading, setIsLoading] = useState(false);
    const [txHash, setTxHash] = useState<`0x${string}` | undefined>();

    // Check if swapping from ETH to token (buy) or token to ETH (sell)
    const isBuyOperation = tokenFromAddress === ETHEREUM_NATIVE_ADDRESS || !tokenFromAddress;

    // Get ETH balance
    const { data: ethBalance } = useBalance({
        address,
    });

    // Get token balance
    const { data: tokenBalance } = useReadContract({
        address: tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenFromAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: address ? [address] : undefined,
        query: {
            enabled: !!address && !!tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // Get token allowance
    const { data: allowance, refetch: refetchAllowance } = useReadContract({
        address: tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenFromAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'allowance',
        args: address ? [address, SODA_FACTORY_ADDRESS] : undefined,
        query: {
            enabled: !!address && !!tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // Get token decimals
    const { data: tokenDecimals } = useReadContract({
        address: tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS ? tokenFromAddress : undefined,
        abi: ERC20_ABI,
        functionName: 'decimals',
        query: {
            enabled: !!tokenFromAddress && tokenFromAddress !== ETHEREUM_NATIVE_ADDRESS,
        },
    });

    // Wait for transaction receipt
    const { data: receipt, isLoading: isWaitingForReceipt } = useWaitForTransactionReceipt({
        hash: txHash,
    });

    // Check if approval is needed
    const needsApproval = useCallback(() => {
        if (isBuyOperation || !tokenFromAddress || tokenFromAddress === ETHEREUM_NATIVE_ADDRESS) {
            return false;
        }
        
        if (!allowance || !amount) return true;
        
        const decimals = tokenDecimals || 18;
        const amountBigInt = parseEther(amount) / BigInt(10 ** (18 - decimals));
        
        return BigInt(allowance.toString()) < amountBigInt;
    }, [isBuyOperation, tokenFromAddress, allowance, amount, tokenDecimals]);

    // Approve token spending
    const approveToken = useCallback(async () => {
        if (!tokenFromAddress || tokenFromAddress === ETHEREUM_NATIVE_ADDRESS || !address) {
            throw new Error('Invalid token or address');
        }

        setIsLoading(true);
        try {
            const hash = await writeContractAsync({
                address: tokenFromAddress,
                abi: ERC20_ABI,
                functionName: 'approve',
                args: [SODA_FACTORY_ADDRESS, BigInt(MAX_UINT256)],
            });
            
            setTxHash(hash);
            
            // Wait for confirmation and refetch allowance
            return hash;
        } catch (error) {
            console.error('Approval failed:', error);
            throw error;
        } finally {
            setIsLoading(false);
        }
    }, [tokenFromAddress, address, writeContractAsync]);

    // Execute buy operation (ETH -> Token)
    const executeBuy = useCallback(async () => {
        if (!tokenToAddress || !amount || !address) {
            throw new Error('Missing required parameters for buy');
        }

        setIsLoading(true);
        try {
            const ethAmount = parseEther(amount);
            
            const hash = await writeContractAsync({
                address: SODA_FACTORY_ADDRESS,
                abi: SodaFactoryABI.abi,
                functionName: 'buyExactIn',
                args: [tokenToAddress, BigInt(0)], // 0 for minimum amount out (can be improved with slippage calculation)
                value: ethAmount,
            });
            
            setTxHash(hash);
            return hash;
        } catch (error) {
            console.error('Buy failed:', error);
            throw error;
        } finally {
            setIsLoading(false);
        }
    }, [tokenToAddress, amount, address, writeContractAsync]);

    // Execute sell operation (Token -> ETH)
    const executeSell = useCallback(async () => {
        if (!tokenFromAddress || tokenFromAddress === ETHEREUM_NATIVE_ADDRESS || !amount || !address) {
            throw new Error('Missing required parameters for sell');
        }

        setIsLoading(true);
        try {
            const decimals = tokenDecimals || 18;
            const tokenAmount = parseEther(amount) / BigInt(10 ** (18 - decimals));
            
            const hash = await writeContractAsync({
                address: SODA_FACTORY_ADDRESS,
                abi: SodaFactoryABI.abi,
                functionName: 'sellExactIn',
                args: [tokenFromAddress, tokenAmount, BigInt(0)], // 0 for minimum collateral out
            });
            
            setTxHash(hash);
            return hash;
        } catch (error) {
            console.error('Sell failed:', error);
            throw error;
        } finally {
            setIsLoading(false);
        }
    }, [tokenFromAddress, amount, address, tokenDecimals, writeContractAsync]);

    // Main swap function
    const executeSwap = useCallback(async () => {
        if (isBuyOperation) {
            return await executeBuy();
        } else {
            // Check if approval is needed first
            if (needsApproval()) {
                const approvalHash = await approveToken();
                // Wait for approval before executing sell
                // In a real app, you might want to handle this differently
                await new Promise(resolve => setTimeout(resolve, 2000)); // Basic delay
                await refetchAllowance();
            }
            return await executeSell();
        }
    }, [isBuyOperation, executeBuy, executeSell, needsApproval, approveToken, refetchAllowance]);

    // Get balance for the "from" token
    const getFromTokenBalance = useCallback(() => {
        if (isBuyOperation) {
            return ethBalance ? formatUnits(ethBalance.value, 18) : '0';
        } else if (tokenBalance) {
            const decimals = tokenDecimals || 18;
            return formatUnits(BigInt(tokenBalance.toString()), decimals);
        }
        return '0';
    }, [isBuyOperation, ethBalance, tokenBalance, tokenDecimals]);

    // Check if user has sufficient balance
    const hasSufficientBalance = useCallback(() => {
        if (!amount) return true;
        
        const balance = getFromTokenBalance();
        return parseFloat(balance) >= parseFloat(amount);
    }, [amount, getFromTokenBalance]);

    return {
        executeSwap,
        approveToken,
        needsApproval: needsApproval(),
        hasSufficientBalance: hasSufficientBalance(),
        fromTokenBalance: getFromTokenBalance(),
        isLoading: isLoading || isWaitingForReceipt,
        txHash,
        receipt,
        isBuyOperation,
    };
};
