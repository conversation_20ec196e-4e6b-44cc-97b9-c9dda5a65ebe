<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_11250_3738)">
<path d="M2.59794 16.4354C-1.01088 12.8265 -0.838789 6.80341 2.98231 2.98231C6.80341 -0.838789 12.8265 -1.01088 16.4354 2.59794C20.0442 6.20676 19.8721 12.2299 16.051 16.051C12.2299 19.8721 6.20676 20.0442 2.59794 16.4354Z" fill="#FFF728"/>
<path d="M2.59794 16.4354C-1.01088 12.8265 -0.838789 6.80341 2.98231 2.98231C6.80341 -0.838789 12.8265 -1.01088 16.4354 2.59794C20.0442 6.20676 19.8721 12.2299 16.051 16.051C12.2299 19.8721 6.20676 20.0442 2.59794 16.4354Z" fill="url(#paint0_radial_11250_3738)"/>
</g>
<g filter="url(#filter1_i_11250_3738)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.59794 16.4354L3.56463 17.4021C7.17345 21.0109 13.1966 20.8388 17.0177 17.0177C20.8388 13.1966 21.0109 7.17345 17.4021 3.56463L16.4354 2.59794C20.0442 6.20675 19.8721 12.2299 16.051 16.051C12.2299 19.8721 6.20675 20.0442 2.59794 16.4354Z" fill="#EE6C00"/>
</g>
<path d="M2.6914 16.3419C-0.860837 12.7897 -0.698814 6.85036 3.07577 3.07577C6.85036 -0.698814 12.7897 -0.860837 16.3419 2.6914C19.8941 6.24364 19.7321 12.1829 15.9575 15.9575C12.1829 19.7321 6.24364 19.8941 2.6914 16.3419Z" stroke="url(#paint1_radial_11250_3738)" stroke-opacity="0.72" stroke-width="0.264351"/>
<g filter="url(#filter2_ii_11250_3738)">
<path d="M4.48424 3.95476C1.53975 6.89925 1.26126 11.7753 4.09693 14.611C6.93261 17.4466 11.8087 17.1681 14.7531 14.2237C17.6976 11.2792 17.9761 6.40313 15.1404 3.56745C12.3048 0.731775 7.42873 1.01027 4.48424 3.95476Z" fill="#FFB700"/>
</g>
<path d="M4.28885 4.07996C1.30045 7.06835 1.00686 12.0284 3.90154 14.9231C6.79623 17.8178 11.7563 17.5242 14.7447 14.5358C17.7331 11.5474 18.0267 6.58733 15.132 3.69265C12.2373 0.797968 7.27725 1.09156 4.28885 4.07996Z" stroke="url(#paint2_radial_11250_3738)" stroke-opacity="0.48" stroke-width="0.264351"/>
<g filter="url(#filter3_i_11250_3738)">
<path d="M10.2958 5.49549C10.5536 5.13746 11.121 5.23538 11.2382 5.65811L11.7806 7.61508C11.8267 7.78155 11.9546 7.91457 12.1215 7.9698L14.0496 8.60753C14.4575 8.74245 14.5384 9.27238 14.1886 9.51804L12.4589 10.7328C12.3224 10.8287 12.2406 10.9823 12.2385 11.1466L12.2113 13.2513C12.2058 13.6831 11.6941 13.9186 11.3507 13.6475L9.79375 12.4181C9.65238 12.3065 9.46235 12.2737 9.2905 12.3313L7.39781 12.9654C6.98035 13.1053 6.58182 12.7127 6.7275 12.3051L7.43758 10.3182C7.49304 10.163 7.46984 9.99132 7.3751 9.85558L6.17428 8.13513C5.93144 7.7872 6.19265 7.3163 6.62308 7.32605L8.65775 7.3721C8.83396 7.37609 9.00061 7.29389 9.10214 7.1529L10.2958 5.49549Z" fill="#EE6400"/>
</g>
<path d="M9.76621 4.97792C10.0241 4.61989 10.5915 4.7178 10.7086 5.14054L11.251 7.0975C11.2972 7.26397 11.425 7.39699 11.592 7.45222L13.5201 8.08996C13.9279 8.22487 14.0088 8.7548 13.659 9.00046L11.9293 10.2152C11.7929 10.3111 11.711 10.4647 11.7089 10.6291L11.6818 12.7338C11.6762 13.1655 11.1645 13.4011 10.8211 13.1299L9.26421 11.9005C9.12284 11.7889 8.93281 11.7561 8.76096 11.8137L6.86827 12.4478C6.4508 12.5877 6.05228 12.1951 6.19795 11.7875L6.90804 9.80064C6.96349 9.64547 6.9403 9.47374 6.84556 9.338L5.64473 7.61755C5.40189 7.26962 5.66311 6.79872 6.09354 6.80847L8.12821 6.85452C8.30442 6.85851 8.47107 6.77631 8.5726 6.63533L9.76621 4.97792Z" fill="#F9D131"/>
<path d="M9.76621 4.97792C10.0241 4.61989 10.5915 4.7178 10.7086 5.14054L11.251 7.0975C11.2972 7.26397 11.425 7.39699 11.592 7.45222L13.5201 8.08996C13.9279 8.22487 14.0088 8.7548 13.659 9.00046L11.9293 10.2152C11.7929 10.3111 11.711 10.4647 11.7089 10.6291L11.6818 12.7338C11.6762 13.1655 11.1645 13.4011 10.8211 13.1299L9.26421 11.9005C9.12284 11.7889 8.93281 11.7561 8.76096 11.8137L6.86827 12.4478C6.4508 12.5877 6.05228 12.1951 6.19795 11.7875L6.90804 9.80064C6.96349 9.64547 6.9403 9.47374 6.84556 9.338L5.64473 7.61755C5.40189 7.26962 5.66311 6.79872 6.09354 6.80847L8.12821 6.85452C8.30442 6.85851 8.47107 6.77631 8.5726 6.63533L9.76621 4.97792Z" fill="url(#paint3_radial_11250_3738)"/>
<path d="M9.87347 5.05516C10.0679 4.78515 10.4944 4.86235 10.5813 5.17584L11.1236 7.1328C11.1818 7.3427 11.3425 7.50893 11.5505 7.57771L13.4785 8.21544C13.7852 8.31686 13.8423 8.71021 13.5831 8.8923L11.8534 10.1071C11.6827 10.2269 11.5794 10.4198 11.5768 10.6274L11.5496 12.7321C11.5455 13.0489 11.1646 13.2327 10.903 13.0262L9.34612 11.7968C9.16948 11.6573 8.9328 11.6167 8.71897 11.6883L6.82627 12.3225C6.50671 12.4296 6.21624 12.1291 6.32242 11.832L7.0325 9.84512C7.10244 9.64945 7.07304 9.43298 6.95395 9.26235L5.75312 7.5419C5.57542 7.28731 5.76267 6.93319 6.09055 6.94061L8.12522 6.98667C8.34422 6.99162 8.55243 6.88951 8.67986 6.71257L9.87347 5.05516Z" stroke="url(#paint4_radial_11250_3738)" stroke-opacity="0.48" stroke-width="0.264351"/>
<defs>
<filter id="filter0_i_11250_3738" x="-0.528703" y="-0.528703" width="19.562" height="19.562" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.528703" dy="-0.528703"/>
<feGaussianBlur stdDeviation="2.11481"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_11250_3738"/>
</filter>
<filter id="filter1_i_11250_3738" x="2.33355" y="2.33358" width="17.6665" height="17.6664" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.264351" dy="-0.264351"/>
<feGaussianBlur stdDeviation="0.793054"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.988235 0 0 0 0 0.811765 0 0 0 0 0 0 0 0 0.64 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_11250_3738"/>
</filter>
<filter id="filter2_ii_11250_3738" x="2.11584" y="1.58636" width="17.1205" height="17.1205" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.11481" dy="2.11481"/>
<feGaussianBlur stdDeviation="4.22962"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.64 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_11250_3738"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.528703" dy="0.528703"/>
<feGaussianBlur stdDeviation="1.05741"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.64 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_11250_3738" result="effect2_innerShadow_11250_3738"/>
</filter>
<filter id="filter3_i_11250_3738" x="5.81683" y="5.01159" width="8.59235" height="8.75156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.264351" dy="-0.264351"/>
<feGaussianBlur stdDeviation="0.793054"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.690196 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_11250_3738"/>
</filter>
<radialGradient id="paint0_radial_11250_3738" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0.218995 0.218995) rotate(45) scale(25.7956)">
<stop offset="0.06" stop-color="#FBFFA0"/>
<stop offset="0.835" stop-color="#FBFFA0" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_11250_3738" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(0.218995 0.218998) rotate(45) scale(26.9172)">
<stop stop-color="white"/>
<stop offset="0.886255" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_11250_3738" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.1376 16.9287) rotate(-135) scale(21.6833)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_11250_3738" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8.95291 0.934694) rotate(82.9223) scale(16.8531 15.307)">
<stop offset="0.06" stop-color="#FBFFA0"/>
<stop offset="0.835" stop-color="#FBFFA0" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint4_radial_11250_3738" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9.88295 14.494) rotate(-100.83) scale(11.0632 10.4576)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
