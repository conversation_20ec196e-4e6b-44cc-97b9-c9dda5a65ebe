import { ReactNode } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface BottomSheetState {
    icon?: ReactNode;
    title?: string;
    description?: string;
    actions?: ReactNode[];
    customContent?: ReactNode;
    customHeader?: ReactNode;
    isShowCloseIcon?: boolean;
    onClose?: () => void;
}

const INITIAL_STATE: BottomSheetState = {
    actions: [],
    description: '',
    title: '',
    icon: undefined,
    customContent: undefined,
    customHeader: undefined,
    isShowCloseIcon: true,
};

interface BottomSheet {
    isShow: boolean;
    state: BottomSheetState;
    show: (payload: BottomSheetState) => void;
    close: () => void;
    updateState: (payload: BottomSheetState) => void;
}

export const useBottomSheetStore = create<BottomSheet>()(
    devtools(
        immer((set) => ({
            isShow: false,
            state: INITIAL_STATE,
            show: (payload) =>
                set({ state: { ...INITIAL_STATE, ...payload }, isShow: true }),
            close: () =>
                set({
                    isShow: false,
                    state: INITIAL_STATE,
                }),
            updateState: (payload) =>
                set((state) => {
                    state.state = {
                        ...state.state,
                        ...payload,
                    };
                }),
        })),
    ),
);
