'use client';
import { Icons } from '@assets';
import { useKeyboardInfo } from '@hooks';
import { Title } from '@ui';
import classNames from 'classnames';
import { FC, useCallback } from 'react';

import { useBottomSheetStore } from '../model';
import styles from './bottom-sheet.module.scss';

export const BottomSheet: FC = () => {
    const { state, isShow, close } = useBottomSheetStore();
    const { isKeyboardVisible } = useKeyboardInfo();

    const closeHandler = useCallback(() => {
        if (state.onClose) {
            state.onClose();
        }

        close();
    }, [close, state]);

    return (
        <div
            className={classNames(styles.container, {
                [styles.show]: isShow,
                [styles.keyboardShow]: isKeyboardVisible,
            })}
        >
            <div className={styles.content}>
                {state.customHeader && (
                    <div className={styles.customHeader}>
                        {state.customHeader}
                    </div>
                )}
                {state.isShowCloseIcon && (
                    <div onClick={closeHandler} className={styles.close}>
                        <Icons.Close />
                    </div>
                )}

                {state.icon ? (
                    <div className={styles.icon}>{state.icon}</div>
                ) : null}

                <div className={styles.title}>
                    <Title size="s">{state.title}</Title>
                </div>

                <div className={styles.description}>{state.description}</div>

                {state.customContent && (
                    <div className={styles.customContent}>
                        {state.customContent}
                    </div>
                )}

                {state.actions && (
                    <div className={styles.actions}>
                        {state.actions.map((action) => action)}
                    </div>
                )}
            </div>
        </div>
    );
};
