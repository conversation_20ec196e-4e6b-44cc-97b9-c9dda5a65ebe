.container {
    z-index: var(--bottom-sheet-z-index);
    position: fixed;

    bottom: -100vh;
    left: 0;

    height: 100vh;
    width: 100vw;

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    opacity: 0;
    transition: all 0.2s ease-in-out;

    @media screen and (min-width: 1024px) {
        align-items: center;
        justify-content: center;
    }
}

.keyboardShow {
    padding-bottom: 200px;
}

.show {
    bottom: 0;
    background: var(--color-bg-scrim);
    opacity: 1;
}

.content {
    @media screen and (min-width: 1024px) {
        max-width: 500px;
        width: 500px;

        border-radius: 24px;
    }

    position: relative;

    padding: 20px;
    border-radius: 24px 24px 0px 0px;
    background: var(--color-bg-card);
}

.close {
    cursor: pointer;

    position: absolute;
    z-index: 1;

    width: 28px;
    height: 28px;

    display: flex;
    justify-content: center;
    align-items: center;

    right: 20px;
    top: 20px;

    border-radius: 50px;
    background: var(--color-neutral);
    color: var(--color-fg-tertiary);
}

.icon {
    margin-bottom: 21px;

    & svg {
        width: 32px;
        height: 32px;
    }
}

.title {
    margin-bottom: 12px;
}

.description {
    color: var(--color-fg-secondary);

    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.13px;

    margin-bottom: 20px;
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.customContent {
    margin-bottom: 12px;
}
