'use client';

import { App } from '@types';
import { AppCard, Slider, SliderMandatory } from '@ui';
import { FC, useMemo } from 'react';

import styles from './apps-slider.module.scss';

interface IAppsSliderProps {
    items: App[];
    groupSize?: number;
}

export const AppsSlider: FC<IAppsSliderProps> = ({ items, groupSize = 3 }) => {
    const groupedApps = useMemo(
        () =>
            items.reduce<App[][]>(
                (acc, game) => {
                    if (acc[acc.length - 1].length < groupSize) {
                        acc[acc.length - 1].push(game);
                    } else {
                        acc.push([game]);
                    }

                    return acc;
                },
                [[]],
            ),
        [items, groupSize],
    );

    const sliderItems = useMemo(
        () =>
            groupedApps.map((group, groupIndex) => (
                <div
                    key={`slider-group-${groupIndex}`}
                    className={styles.group}
                >
                    {group.map((app, appIndex) => (
                        <AppCard.Big
                            key={`slider-group-item-${app.name}-${appIndex}`}
                            name={app.name}
                            imageSrc={app.avatar}
                            place={groupIndex * (appIndex + 1)}
                            color={app.color}
                            id={app.id}
                            usersCount={app.usersCount}
                            marketCapUsd={app.token?.marketCapUsd}
                            collectedSol={app.token?.collectedSol}
                        />
                    ))}
                </div>
            )),
        [groupedApps],
    );

    return (
        <div className={styles.container}>
            {sliderItems.length ? (
                <SliderMandatory items={sliderItems} />
            ) : null}
        </div>
    );
};
