/* eslint-disable no-restricted-syntax */
'use client';
import { widget } from '@static/charting_library';
import type {
    IChartingLibraryWidget,
    ResolutionString,
} from '@static/charting_library/charting_library';
import { Loader, Typography } from '@ui';
import classNames from 'classnames';
import { FC, useCallback, useEffect, useRef, useState } from 'react';

import { useChartOptions } from '../../hooks/useChartOptions';
import styles from './trading-view.module.scss';

export interface ITradingViewProps {
    className?: string;
    containerId?: string;
}

export const TradingView: FC<
    ITradingViewProps & { symbol: string; address?: string }
> = ({ className, containerId = 'tv_chart_container', symbol, address }) => {
    const [isReady, setIsReady] = useState(false);

    const { getOptions, getOverridesDark } = useChartOptions(containerId);

    const ref = useRef<IChartingLibraryWidget>();

    const onReadyHandler = useCallback(
        (val = '') => {
            if (!ref.current) {
                const myNode = document.getElementById('containerId');

                if (myNode) {
                    myNode.childNodes.forEach(
                        (_item, index) => delete myNode.childNodes[index],
                    );
                }

                // @ts-expect-error tvwidget
                ref.current = window.tvWidget = new widget(getOptions(val));

                window.frames[0].focus();

                // ref.current?.chartRea().then(() => {
                //     ref.current?.applyOverrides(getOverridesDark());

                //     ref.current?.setCSSCustomProperty(
                //         '--tv-color-pane-background',
                //         '#131314',
                //     );

                //     ref.current?.setCSSCustomProperty(
                //         '--tv-color-toolbar-button-text-active',
                //         '#0593ff',
                //     );
                // });

                ref.current?.onChartReady(() => {
                    ref.current?.applyOverrides(getOverridesDark());

                    ref.current?.setCSSCustomProperty(
                        '--tv-color-pane-background',
                        '#131314',
                    );

                    ref.current?.setCSSCustomProperty(
                        '--tv-color-toolbar-button-text-active',
                        '#0593ff',
                    );

                    ref.current?.setSymbol(
                        val,
                        ref.current?.chart().resolution(),
                        () => {
                            setIsReady(true);
                        },
                    );

                    ref.current
                        ?.chart()
                        .getPanes()
                        .forEach((pane) =>
                            pane.getMainSourcePriceScale()?.setMode(1),
                        );
                });
            }
        },
        [getOptions, getOverridesDark, ref],
    );

    useEffect(() => {
        setTimeout(() => {
            onReadyHandler(`${symbol} (${address})`);
        }, 150);
    }, [onReadyHandler, symbol, address]);

    const [selectedResolution, setSelectedResolution] = useState('1');

    const handleResolutionClick = (resolution: string) => {
        ref.current?.chart()?.setResolution(resolution as ResolutionString);
        setSelectedResolution(resolution);
    };

    return (
        <div className={classNames(styles.container)}>
            <div className={styles.resolutions}>
                <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '1',
                    })}
                    onClick={() => handleResolutionClick('1')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        1m
                    </Typography>
                </div>
                <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '5',
                    })}
                    onClick={() => handleResolutionClick('5')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        5m
                    </Typography>
                </div>
                <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '15',
                    })}
                    onClick={() => handleResolutionClick('15')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        15m
                    </Typography>
                </div>
                <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '60',
                    })}
                    onClick={() => handleResolutionClick('60')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        1h
                    </Typography>
                </div>
                {/* <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '4h',
                    })}
                    onClick={() => handleResolutionClick('4h')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        4h
                    </Typography>
                </div> */}
                <div
                    className={classNames(styles.resolution, {
                        [styles.selected]: selectedResolution === '1d',
                    })}
                    onClick={() => handleResolutionClick('1d')}
                >
                    <Typography size="s" variant="primary" weight="600">
                        1d
                    </Typography>
                </div>
            </div>
            <div className={classNames(styles.chartWrapper, className)}>
                <div
                    className={classNames(styles.chart, {
                        [styles.isVisible]: isReady,
                    })}
                    id={containerId}
                />
                {!isReady && (
                    <div className={styles.loading}>
                        <Loader.TwelveFade />
                    </div>
                )}
            </div>
        </div>
    );
};
