.container {
    margin-bottom: 24px;
    width: 100%;
}

.chartWrapper {
    position: relative;
    z-index: 9;
    overflow: hidden;
    width: 100%;
    border-radius: 0.5rem;
    scroll-margin-top: 1000px;
    border: 1px solid var(--color-border-base);
}

.chart {
    position: relative;
    width: 100%;
    padding-bottom: 65%;
    min-height: 200px;
    opacity: 0;
    transition: all 0.1s ease-in;

    iframe {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
    }
}

.isVisible {
    opacity: 1;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
}

.resolutions {
    display: flex;
    align-items: center;

    gap: 2px;

    margin-bottom: 12px;
}

.resolution {
    cursor: pointer;

    display: flex;
    justify-content: center;
    align-items: center;

    width: 38px;
    height: 32px;

    border-radius: 10px;

    &.selected {
        background-color: var(--color-bg-card-secondary);
    }
}
