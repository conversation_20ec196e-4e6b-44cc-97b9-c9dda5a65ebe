/* eslint-disable @typescript-eslint/no-explicit-any */
export enum Theme {
    LIGHT = 'light',
    DARK = 'dark',
}
// storage types

export enum COOKIE_KEY {
    USER = 'user',
}

export enum LOCAL_STORAGE_KEY {
    ACCESS_TOKEN = 'dyor:access-token',
    IS_CUSTODIAN_ACCOUNT = 'dyor:is-custodian-account',
    OVERRIDE_BROWSER_TIMEZONE = 'dyor:override-browser-timezone',
}

// trading view types

export const UP_COLOR = '#16c784';
export const DOWN_COLOR = '#EA3943';

export enum ANALYTICS_PERIOD {
    '14D' = 'w2',
    '15M' = 'min15',
    '180D' = 'm6',
    '1D' = 'd1',
    '1H' = 'h1',
    '1M' = '1m',
    '1Y' = 'y1',
    '30D' = 'm1',
    '30M' = 'min30',
    '3D' = 'd3',
    '4H' = 'h4',
    '5M' = 'min5',
    '7D' = 'w1',
    '90D' = 'm3',
    'all' = 'all',
    'M1' = 'min1',
}

export enum CURRENCY {
    TON = 'ton',
    USD = 'usd',
}

export enum SORT_ORDER {
    ASC = 'asc',
    DESC = 'desc',
}

export enum MARKETS_SORT_FIELD {
    TRADERS_24H = 'traders24h',
    TVL = 'tvl',
    VOLUME_24H = 'volume24h',
}

export const ANALYTICS_MARKETS_LIMIT = 30;
export const ANALYTICS_MARKETS_ORDER = SORT_ORDER.DESC;
export const ANALYTICS_MARKETS_SORT = MARKETS_SORT_FIELD.TRADERS_24H;

export type TMarketV4 = {
    createdAt: string;
    jetton1Id: number;
    jetton2Id: number;
    price: number;
    priceChange1m: number;
    priceChange1w: number;
    priceChange24h: number;
    sourceId: number;
    traders24h: number;
    tradersChange24h: number;
    tvl: number;
    updatedAt: string;
    volume24h: number;
    volumeChange24h: number;
    volumeShare24h: number;
};

export type TPairV4 = {
    createdAt: string;
    id: number;
    jetton1Id: number;
    jetton2Id?: number;
    lpAddress: string;
    sourceId: number;
};

export type TJettonLink = {
    id: number;
    name: string;
    type: JETTON_LINK_TYPE;
    url: string;
};

export enum JETTON_LINK_TYPE {
    DISCORD = 'discord',
    FACEBOOK = 'facebook',
    GETGEMS = 'getgems',
    GITHUB = 'github',
    INSTAGRAM = 'instagram',
    MINTER = 'minter',
    OTHER = 'other',
    TELEGRAM = 'telegram',
    TWITTER = 'twitter',
    WEBSITE = 'website',
    WHITEPAPER = 'whitepaper',
}

export enum TVerifStatus {
    APPROVED = 'appr',
    COMMUNITY_VERIFIED = 'cver',
    NONE = 'none',
    SCAM = 'scam',
    VERIFIED = 'ver',
}

export type TJettonV4 = {
    address: string;
    chartDark: string;
    chartLight: string;
    createdAt: string | null;
    customContract: boolean;
    data?: any[];
    decimals: number;
    dedustLPAddress?: string;
    dedustSwapAddress?: string;
    description?: string;
    id: number;
    image: string;
    links?: TJettonLink[];
    mintable: number;
    name: string;
    offchainDescription?: string | null;
    offchainImage?: string | null;
    offchainOwnerAddress?: string | null;
    owner: string;
    ownerAddress: string;
    symbol: string;
    totalSupply: number;
    URLIfCommunityVerified: string;
    verified: number;
    verifStatus: TVerifStatus;
};

export type TCachedJetton = {
    chartDark1m?: string;
    chartLight1m?: string;
    createdAt?: string;
    fdmc?: number;
    jettonId: number;
    price?: number | string;
    priceChange1m?: number | string;
    priceChange1w?: number | string;
    priceChange24h?: number | string;
    priceUsd?: number | string;
    promotingPoints: number;
    traders: number;
    traders1w: number;
    traders24h: number;
    trustScore: number;
    tvl?: number | string;
    updatedAt?: string;
    volume24h?: number | string;
};

export type TJettonDetails = {
    cachedJetton?: TCachedJetton;
    jetton: TJettonV4;
    lastHolders: number;
    links?: TJettonLink[];
};

export type TProfit = {
    period: string;
    sumTon: number;
    sumUsd: number;
};

// dyor auth types

export const TON_CONNECT_MANIFEST_URL =
    'https://dyor.io/tonconnect-manifest.json';

export type TAuthData = {
    address: string;
    status: boolean;
    token: string;
    ton_wallets: TAuthTonWallet[];
    user: TAuthUser;
};

export enum USER_ROLE {
    ADMIN = 'admin',
    USER = 'user',
}

export type TAuthUser = {
    address?: string;
    display_name: string;
    id: number;
    primary_wallet: TAuthPrimaryWallet;
    role: USER_ROLE;
};

export type TAuthPrimaryWallet = {
    address: string;
};

export type TAuthTonWallet = {
    address: string;
};
