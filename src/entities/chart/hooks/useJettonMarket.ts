/* eslint-disable @typescript-eslint/no-explicit-any */
import { QueryObserverOptions, useQuery } from '@tanstack/react-query';
// import type {
//     SORT_ORDER,
//     TJettonDetails,
//     TJettonV4,
//     TMarketV4,
//     TPairV4,
// } from '@types';
// import { dyorApiClient } from './dyor-client';
import axios from 'axios';

import {
    ANALYTICS_MARKETS_LIMIT,
    ANALYTICS_MARKETS_ORDER,
    ANALYTICS_MARKETS_SORT,
    SORT_ORDER,
    TJettonDetails,
    TJettonV4,
    TMarketV4,
    TPairV4,
} from '../types';

export type TGetJettonDetailsByAddressResp = TJettonDetails;

const jettonDetailsGetByAddress = async (
    address: string,
    signal?: AbortSignal,
) => {
    const res = await axios.get<TGetJettonDetailsByAddressResp>(
        `/api/proxy/dyor.io/api/v4/jettons/addr/${address}/details`,
        {
            signal,
        },
    );

    return res.data;
};

export type TGetJettonMarketsResp = {
    count: number;
    jettons: TJettonV4[];
    markets: TMarketV4[];
    pairs: TPairV4[];
};

export enum MARKETS_SORT_FIELD {
    TRADERS_24H = 'traders24h',
    TVL = 'tvl',
    VOLUME_24H = 'volume24h',
}

export type TGetJettonMarketsParams = {
    limit: number;
    order?: SORT_ORDER;
    page?: number;
    sort?: MARKETS_SORT_FIELD;
};

const jettonMarkets = async (
    jettonId: number,
    params: TGetJettonMarketsParams,
    signal?: AbortSignal,
) => {
    const enhancedParams = {
        limit: params.limit,
        offset: params.page ? (params.page - 1) * params.limit : 0,
        sort: params.sort,
        order: params.order,
    };
    const res = await axios.get<TGetJettonMarketsResp>(
        `/api/proxy/dyor.io/api/v4/jettons/${jettonId}/markets`,
        {
            params: enhancedParams,
            signal,
        },
    );

    return res.data;
};

export const v4 = {
    jettonMarkets,
    jettonDetailsGetByAddress,
};

const dyorApi = {
    v4,
};

const getUseJettonV4MarketsQueryKey = (
    jettonId: number | undefined,
    params: TGetJettonMarketsParams,
) => {
    const { limit, order, page, sort } = params;

    return ['dyorApi.v4.jettonMarkets', jettonId, page, limit, sort, order];
};

const useJettonV4Markets = (
    jettonId: number | undefined,
    params: TGetJettonMarketsParams,
    options: Omit<QueryObserverOptions<any>, 'queryKey' | 'queryFn'> = {},
) => {
    const queryFn = (props: { signal?: AbortSignal }) => {
        const { signal } = props;

        if (typeof jettonId === 'undefined') {
            return Promise.reject(new Error('jettonId is empty'));
        }

        return dyorApi.v4.jettonMarkets(jettonId, params, signal);
    };

    return useQuery<Awaited<ReturnType<typeof queryFn>>>({
        queryKey: getUseJettonV4MarketsQueryKey(jettonId, params),
        queryFn,
        enabled: !!jettonId,
        refetchOnReconnect: false,
        ...options,
    });
};

export { useJettonV4Markets };

const useJettonMarket = () => {
    const marketsQuery = useJettonV4Markets(128, {
        sort: ANALYTICS_MARKETS_SORT,
        limit: ANALYTICS_MARKETS_LIMIT,
        order: ANALYTICS_MARKETS_ORDER,
    });

    const getMarket = (pairId?: number) => {
        if (!pairId) {
            return {};
        }

        const marketIndex = marketsQuery.data?.pairs?.findIndex(
            (item: { id: number }) => item.id === pairId,
        );
        const market = (marketsQuery.data?.markets || []).find(
            (_item: any, index: any) => index === marketIndex,
        );

        return {
            market,
            jetton1:
                (market &&
                    (marketsQuery.data?.jettons?.find(
                        (item: { id: any }) => item.id === market?.jetton1Id,
                    ) as unknown as TJettonV4)) ||
                ({
                    name: 'Toncoin',
                    image: '/img/currency/ton.svg',
                    symbol: 'TON',
                    decimals: 9,
                } as TJettonV4),
            jetton2:
                (market &&
                    (marketsQuery.data?.jettons?.find(
                        (item: { id: any }) => item.id === market?.jetton2Id,
                    ) as unknown as TJettonV4)) ||
                ({
                    name: 'Toncoin',
                    image: '/img/currency/ton.svg',
                    symbol: 'TON',
                    decimals: 9,
                } as TJettonV4),
        };
    };

    return { getMarket };
};

export { useJettonMarket };
