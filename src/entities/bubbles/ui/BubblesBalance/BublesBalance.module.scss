.container {
    position: relative;
}

.popup {
    position: absolute;

    left: 0;
    top: calc(100% + 8px);

    &.top {
        bottom: calc(100% + 8px);
        top: unset;
    }
}

.content {
    cursor: pointer;
    user-select: none;

    width: fit-content;

    display: flex;
    align-items: center;

    padding: 7px 10px 7px 8px;

    gap: 2px;

    background-color: var(--color-bg-secondary);
    border-radius: 999px;
}
