'use client';
import { useBubblesBalance } from '@entities/bubbles/lib';
import { ClaimBubbles } from '@features/claim-bubbles';
import { BubblesIcon } from '@shared/assets/icons/bublbes';
import { Typography } from '@shared/ui/typography';
import classNames from 'classnames';
import { motion } from 'framer-motion';
import { FC, useLayoutEffect, useRef, useState } from 'react';

import styles from './BublesBalance.module.scss';

export const BubblesBalance: FC = () => {
    const [isPopupOpen, setIsPopupOpen] = useState(false);
    const { bubblesBalance } = useBubblesBalance();
    const [popupPosition, setPopupPosition] = useState<'top' | 'bottom'>('top');
    const containerRef = useRef<HTMLDivElement>(null);

    const togglePopup = () => {
        setIsPopupOpen((prev) => !prev);
    };

    useLayoutEffect(() => {
        if (isPopupOpen && containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            const spaceAbove = rect.top;
            const MIN_SPACE_ABOVE = 380;

            // По умолчанию показываем сверху, если сверху достаточно места
            setPopupPosition(spaceAbove >= MIN_SPACE_ABOVE ? 'top' : 'bottom');
        }
    }, [isPopupOpen]);

    return (
        <div className={styles.container} ref={containerRef}>
            {isPopupOpen && (
                <div
                    className={classNames(styles.popup, {
                        [styles.top]: popupPosition === 'top',
                    })}
                >
                    <ClaimBubbles onClose={togglePopup} />
                </div>
            )}

            <motion.div
                onClick={togglePopup}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={styles.content}
            >
                <BubblesIcon width={22} height={22} />

                <Typography variant="primary" size="l" weight="500">
                    {bubblesBalance}
                </Typography>
            </motion.div>
        </div>
    );
};
