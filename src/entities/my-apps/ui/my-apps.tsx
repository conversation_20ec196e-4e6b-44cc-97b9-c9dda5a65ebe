/* eslint-disable indent */
'use client';
import { Icons } from '@assets';
import { useMyApps, useTelegramRouterBack } from '@hooks';
import { AppCard, Title } from '@ui';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FC, useCallback } from 'react';

import styles from './my-apps.module.scss';

export const MyApps: FC = () => {
    useTelegramRouterBack('/');
    const { push } = useRouter();
    const { apps: myApps } = useMyApps();

    const addClickHandler = useCallback(() => {
        push('/launch-app');
    }, [push]);

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <Title size="l" weight="600">
                    My games & tokens
                </Title>
                <motion.div
                    whileTap={{ scale: 0.9 }}
                    className={styles.addButton}
                    onClick={addClickHandler}
                >
                    <Icons.Plus />
                </motion.div>
            </div>
            <div>
                {myApps?.map((app, index) => (
                    <AppCard.Big
                        key={app.id}
                        name={app.name}
                        imageSrc={app.avatar}
                        place={index}
                        color={app.color}
                        id={app.id}
                        usersCount={app.usersCount}
                        marketCapUsd={app.token?.marketCapUsd}
                        collectedSol={app.token?.collectedSol}
                    />
                ))}
            </div>
        </div>
    );
};
