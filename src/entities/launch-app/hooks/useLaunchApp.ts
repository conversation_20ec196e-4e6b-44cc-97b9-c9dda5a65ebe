/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-restricted-syntax */
'use client';

import * as amplitude from '@amplitude/analytics-browser';
import { useCoreStore } from '@entities/core';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { sleep } from '@utils';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';

import { fetchLaunchApp, fetchUploadTokenImage } from '../api';
import { useLaunchAppStore } from '../model';
import { gainFromIndex, upgradePriceFromIndex } from '../ui/step2/step2';

const progressSteps = [
    { progress: 10, delay: 1000 },
    { progress: 35, text: 'Devs drinking latte', delay: 1500 },
    { progress: 60, delay: 1500 },
    { progress: 85, text: 'Cleaning stuff', delay: 1500 },
    { progress: 100, delay: 100 },
];

export const useLaunchApp = () => {
    const router = useRouter();

    const showModal = useModalStore((state) => state.showModal);
    const closeModal = useModalStore((state) => state.closeModal);
    const updateModalState = useModalStore((state) => state.updateModalState);
    const clearAppLaunchState = useLaunchAppStore((state) => state.clearStates);
    const addNotification = useNotificationsAdd();
    const { userId } = useCoreStore();

    const simulateLoadingProgress = useCallback(
        async (cb: () => void) => {
            for (const step of progressSteps) {
                if (step.text) {
                    updateModalState({
                        progress: step.progress,
                        text: step.text,
                    });
                } else {
                    updateModalState({
                        progress: step.progress,
                    });
                }
                await sleep(step.delay);
            }
            cb();
        },
        [updateModalState],
    );

    const launchApp = useCallback(async () => {
        try {
            showModal({
                modalType: 'launchAppLoader',
                modalState: {
                    text: 'Creating app',
                    progress: 0,
                },
            });

            const {
                logo,
                gameImage,
                projectName,
                tokenName,
                tokenSymbol,
                description,
                color,
                vibration,
                sounds,
                upgrades,
                tasks,
                soundValue,
                launchNewToken,
            } = useLaunchAppStore.getState();

            const avatar = await fetchUploadTokenImage(logo);
            const tapImageUrl = await fetchUploadTokenImage(gameImage);

            // const [avatar, tapImageUrl] = await Promise.all([
            //     fetchUploadTokenImage(logo),
            //     fetchUploadTokenImage(gameImage),
            // ]);

            const payload: any = {
                gameName: projectName,
                tapImageUrl,
                avatar,
                color,
                type: 'TAP',
                gameFile: '',
                vibration,
                sound: sounds ? `${soundValue}` : undefined,
                upgrades: upgrades.map(({ name, emoji }, index) => ({
                    upgradeName: name,
                    icon: emoji,
                    priceXp: upgradePriceFromIndex[index].tokens,
                    priceSolana:
                        upgradePriceFromIndex[index].sol * LAMPORTS_PER_SOL,
                    profitPerHour: gainFromIndex[index],
                })),
                quests: tasks.map(({ name, link, type }, index) => ({
                    questName: name,
                    description: type.text,
                    reward: 100,
                    expiredAt: moment().add(index, 'month').unix(),
                    actionLink: link,
                    type: type.type,
                })),
            };

            if (launchNewToken) {
                payload.token = {
                    tokenName,
                    tokenSymbol,
                    tokenDescription: description,
                    tokenImage: avatar,
                };
            }

            const app = await fetchLaunchApp(payload);

            amplitude.logEvent('Creation_Builder_Success', {
                userId,
                timestamp: moment().unix(),
                appId: app?.id,
            });

            if (app?.id) {
                router.prefetch(`/app/${app.id}`);

                await simulateLoadingProgress(() => {
                    router.push(`/app/${app.id}`);
                    clearAppLaunchState();
                });
            }
        } catch (error) {
            amplitude.logEvent('Creation_Builder_Error', {
                userId,
                timestamp: moment().unix(),
            });

            await simulateLoadingProgress(() => {
                closeModal();
                addNotification(
                    'error',
                    'Something went wrong. Please, try again later!',
                );
            });
        }
    }, [
        userId,
        router,
        showModal,
        closeModal,
        simulateLoadingProgress,
        clearAppLaunchState,
        addNotification,
    ]);

    return useMemo(() => ({ launchApp }), [launchApp]);
};
