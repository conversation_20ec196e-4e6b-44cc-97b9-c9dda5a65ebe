// import { useModalStore } from '@entities/modal';
// import { useNotificationsAdd } from '@entities/notifications';
import { useNotificationsAdd } from '@entities/notifications';
import { useMutation } from '@tanstack/react-query';
import { dataURLtoFile } from '@utils';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { v4 as randomUUID } from 'uuid';

import { fetchGenerateAppPreset } from '../api';
import { useLaunchAppStore } from '../model';

export const useGenerateAppPreset = () => {
    const addNotification = useNotificationsAdd();
    const { push } = useRouter();

    const { mutateAsync, isPending } = useMutation({
        mutationFn: fetchGenerateAppPreset,
        mutationKey: ['generate-app-preset'],
        onError: () => {
            addNotification(
                'error',
                'Something went wrong. Please, try again later!',
            );
        },
        onSuccess: (data) => {
            // const fileLogo = null;
            let fileMainImage = null;
            if (data.images) {
                fileMainImage = dataURLtoFile(
                    data.images.mainImage.image,
                    'image.webp',
                );

                Object.assign(fileMainImage, {
                    width: data.images.mainImage.width,
                    height: data.images.mainImage.height,
                    aspectRatio: data.images.mainImage.aspectRatio,
                });
            }

            // if (data.images?.logo) {
            //     fileLogo = dataURLtoFile(data.images.logo, 'image.webp');
            // }

            useLaunchAppStore.setState({
                projectName: data.project_name,
                tokenName: data.token_name,
                tokenSymbol: data.token_symbol,
                description: data.description,
                logo: fileMainImage,
                gameImage: fileMainImage,
                color: data.accent_color,
                upgrades: data.upgrades.map((upgrade) => ({
                    ...upgrade,
                    id: randomUUID(),
                })),
            });

            push('/launch-app/1');
        },
    });

    return useMemo(
        () => ({ generate: mutateAsync, isPending }),
        [mutateAsync, isPending],
    );
};
