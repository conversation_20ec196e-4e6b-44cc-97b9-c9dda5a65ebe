import { Toggle, Typography } from '@ui';
import { FC, ReactNode } from 'react';

import styles from './settings-item.module.scss';

interface ISettingsItemProps {
    icon: ReactNode;
    title: string;
    value?: boolean;
    onChange?: (value: boolean) => void;
}

export const SettingsItem: FC<ISettingsItemProps> = ({
    icon,
    title,
    value = false,
    onChange,
}) => (
    <div className={styles.container}>
        <div className={styles.icon}>{icon}</div>

        <Typography
            className={styles.title}
            weight="500"
            variant="primary"
            size="m"
        >
            {title}
        </Typography>

        <Toggle size="l" value={value} onChange={onChange} />
    </div>
);
