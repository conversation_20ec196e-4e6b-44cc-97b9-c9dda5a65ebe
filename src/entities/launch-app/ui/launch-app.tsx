/* eslint-disable indent */
'use client';
import { FC, useMemo } from 'react';

import { useCurrentStep } from '../hooks';
import styles from './launch-app.module.scss';
import { LaunchAppStep0 } from './step0';
import { LaunchAppStep1 } from './step1';
import { LaunchAppStep2 } from './step2';
import { LaunchAppStep3 } from './step3';

export const LaunchApp: FC = () => {
    const step = useCurrentStep();

    const content = useMemo(() => {
        switch (step) {
            case 0:
                return <LaunchAppStep0 />;
            case 1:
                return <LaunchAppStep1 />;
            case 2:
                return <LaunchAppStep2 />;
            case 3:
                return <LaunchAppStep3 />;
            default:
                return <LaunchAppStep0 />;
        }
    }, [step]);

    return <div className={styles.container}>{content}</div>;
};
