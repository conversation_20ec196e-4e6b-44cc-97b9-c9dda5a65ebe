.container {
    position: relative;
    display: flex;
    align-items: center;
}

.input {
    padding: 12px 36px 12px 16px;
    background: var(--color-neutral);
    outline: none;
    border: 1px solid transparent;
    width: 100%;

    color: var(--color-fg-primary);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.09px;

    &::placeholder {
        color: var(--color-fg-tertiary);
    }
}

.invalid {
    border: 1px solid var(--color-negative);
}

.validIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--color-positive);
    color: var(--color-fg-primary);
    position: absolute;
    right: 12px;

    & img,
    svg {
        width: 16px;
        height: 16px;
    }
}
