import { Icons } from '@assets';
import classNames from 'classnames';
import React, { ChangeEvent, FC, useCallback } from 'react';

import styles from './task-input-field.module.scss';

interface TaskInputFieldProps
    extends Omit<
        React.InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'value'
    > {
    value: string;
    isValid?: boolean;
    error?: string;
    placeholder: string;
    onChange: (newValue: string) => void;
}

export const TaskInputField: FC<TaskInputFieldProps> = ({
    value,
    isValid = false,
    error,
    placeholder,
    onChange,
    className,
    ...props
}) => {
    const handleInputChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.value);
        },
        [onChange],
    );

    return (
        <div className={styles.container}>
            <input
                type="text"
                className={classNames(styles.input, className, {
                    [styles.invalid]: <PERSON><PERSON><PERSON>(error),
                })}
                value={value}
                onChange={handleInputChange}
                placeholder={placeholder}
                aria-invalid={!isValid}
                {...props}
            />
            {isValid && (
                <div className={styles.validIcon}>
                    <Icons.CheckMark />
                </div>
            )}
        </div>
    );
};
