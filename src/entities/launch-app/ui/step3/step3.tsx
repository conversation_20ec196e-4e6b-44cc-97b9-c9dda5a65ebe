/* eslint-disable max-lines-per-function */
'use client';
import { useLaunchApp } from '@entities/launch-app/hooks';
import { useLaunchAppStore } from '@entities/launch-app/model';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { TaskTypeItem } from '@types';
import { Title, Typography } from '@ui';
import { isValidLink } from '@utils';
import { useRouter } from 'next/navigation';
import { FC, useCallback, useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';

import { StepBadge } from '../step-badge';
import { AddTask } from './add-task';
import styles from './step3.module.scss';
import { TaskInput } from './task-input';

export const LaunchAppStep3: FC = () => {
    const showModal = useModalStore((store) => store.showModal);

    const addTaskHandler = useCallback(() => {
        showModal({
            modalType: 'chooseTaskType',
            modalState: {
                onSave: (taskType: TaskTypeItem) => {
                    useLaunchAppStore.setState({
                        tasks: [
                            ...useLaunchAppStore.getState().tasks,
                            {
                                id: uuid(),
                                name: '',
                                link: '',
                                type: taskType,
                            },
                        ],
                    });
                },
            },
        });
    }, [showModal]);

    const tasks = useLaunchAppStore((store) => store.tasks);

    const taskChangeHandler = useCallback(
        (id: string, key: 'link' | 'name') => (value: string) => {
            useLaunchAppStore.setState({
                tasks: useLaunchAppStore
                    .getState()
                    .tasks.map((task) =>
                        task.id === id ? { ...task, [key]: value } : task,
                    ),
            });
        },
        [],
    );

    const taskDeleteHandler = useCallback(
        (id: string) => () => {
            useLaunchAppStore.setState({
                tasks: useLaunchAppStore
                    .getState()
                    .tasks.filter((task) => task.id !== id),
            });
        },
        [],
    );

    const [errors, setErrors] = useState<Record<string, string>>({});
    const { push } = useRouter();
    const validateTasks = useCallback(() => {
        const errors: Record<string, string> = {};
        const tasks = useLaunchAppStore.getState().tasks;

        for (const task of tasks) {
            if (task.link.length === 0) {
                errors[`${task.id}-link`] = 'Link is required';
            } else if (!isValidLink(task.link)) {
                errors[`${task.id}-link`] = 'Link is invalid';
            }

            if (task.name.length === 0) {
                errors[`${task.id}-name`] = 'Name is required';
            }
        }

        return errors;
    }, []);

    const addNotification = useNotificationsAdd();

    const handleBackButtonClick = useCallback(() => {
        push('/launch-app/2');
    }, [push]);

    const { launchApp } = useLaunchApp();

    const actionClickHandler = useCallback(() => {
        const errors = validateTasks();
        setErrors(errors);

        if (Object.keys(errors).length > 0) {
            addNotification('error', 'All fields required to be completed');

            return;
        } else {
            launchApp();
        }
    }, [validateTasks, addNotification, launchApp]);

    return (
        <div className={styles.container}>
            <Title size="m" className={styles.title}>
                Create tasks
            </Title>
            <Typography weight="400" variant="secondary" size="m">
                By completing tasks with social media users will receive XP
                rewards
            </Typography>

            {tasks.length ? (
                <div className={styles.column}>
                    {tasks.map((task) => (
                        <TaskInput
                            errorName={errors[`${task.id}-name`]}
                            errorLink={errors[`${task.id}-link`]}
                            nameValue={task.name}
                            linkValue={task.link}
                            onChangeLink={taskChangeHandler(task.id, 'link')}
                            onChangeName={taskChangeHandler(task.id, 'name')}
                            onDelete={taskDeleteHandler(task.id)}
                            taskType={task.type}
                            key={task.id}
                        />
                    ))}
                </div>
            ) : null}

            <AddTask onClick={addTaskHandler} />

            <StepBadge
                onPrevClick={handleBackButtonClick}
                onActionClick={actionClickHandler}
            />
        </div>
    );
};
