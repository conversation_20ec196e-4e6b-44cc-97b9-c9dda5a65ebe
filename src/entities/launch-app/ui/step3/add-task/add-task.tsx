import { Icons } from '@assets';
import { Typography } from '@ui';
import { FC, useCallback } from 'react';

import styles from './add-task.module.scss';

interface AddTaskProps {
    onClick: () => void;
}

export const AddTask: FC<AddTaskProps> = ({ onClick }) => {
    // const [arrowTurn, setArrowTurn] = useState<boolean>(false);

    const clickHandler = useCallback(() => {
        // setArrowTurn((prev) => !prev);
        onClick();
    }, [onClick]);

    return (
        <div className={styles.container} onClick={clickHandler}>
            <Typography weight="400" variant="primary" size="m">
                Select type of task
            </Typography>
            {/* <AnimatedArrow arrowTurn={arrowTurn} /> */}
            <Icons.Arrow
                width={20}
                height={20}
                color="var(--color-fg-tertiary)"
            />
        </div>
    );
};
