.wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.label {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    color: var(--color-accent);
    & img,
    svg {
        width: 16px;
        height: 16px;
    }
}

.container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.inputs {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5px;
    overflow: hidden;
    border-radius: 12px;
}

.firstInput {
    border-radius: 12px 12px 0 0;
}

.lastInput {
    border-radius: 0 0 12px 12px;
}

.deleteButton {
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    align-self: stretch;
    background: var(--color-neutral);
    outline: none;
    border: none;

    color: var(--color-fg-secondary);
    & img,
    svg {
        width: 20px;
        height: 20px;
    }

    cursor: pointer;
}
