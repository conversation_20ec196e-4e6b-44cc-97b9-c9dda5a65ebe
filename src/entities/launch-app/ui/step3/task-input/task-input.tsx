/* eslint-disable @typescript-eslint/no-unused-expressions */
import { Icons } from '@assets';
import { TaskTypeItem } from '@types';
import { Typography } from '@ui';
import { isValidLink } from '@utils';
import { FC, useCallback, useMemo } from 'react';

import { TaskInputField } from '../task-input-field';
import styles from './task-input.module.scss';

interface TaskInputProps {
    linkValue: string;
    nameValue: string;
    onChangeLink: (newValue: string) => void;
    onChangeName: (newValue: string) => void;
    onDelete: () => void;
    taskType: TaskTypeItem;
    errorName?: string;
    errorLink?: string;
}

export const TaskInput: FC<TaskInputProps> = ({
    linkValue,
    nameValue,
    onChangeLink,
    onChangeName,
    onDelete,
    taskType,
    errorName,
    errorLink,
}) => {
    const handleInputChange = useCallback(
        (type: 'link' | 'name') => (newValue: string) => {
            type === 'link' ? onChangeLink(newValue) : onChangeName(newValue);
        },
        [onChangeLink, onChangeName],
    );

    const handleDeleteClick = useCallback(() => {
        onDelete();
    }, [onDelete]);

    const isLinkValid = useMemo(() => isValidLink(linkValue), [linkValue]);

    return (
        <div className={styles.wrapper}>
            <div className={styles.label}>
                {taskType.icon}{' '}
                <Typography weight="600" variant="primary" size="sm">
                    {taskType.text}
                </Typography>
            </div>
            <div className={styles.container}>
                <div className={styles.inputs}>
                    <TaskInputField
                        value={nameValue}
                        error={errorName}
                        placeholder="Task name"
                        onChange={handleInputChange('name')}
                        className={styles.firstInput}
                    />
                    <TaskInputField
                        value={linkValue}
                        isValid={isLinkValid}
                        error={errorLink}
                        placeholder="Link to post"
                        onChange={handleInputChange('link')}
                        className={styles.lastInput}
                    />
                </div>

                <button
                    className={styles.deleteButton}
                    onClick={handleDeleteClick}
                >
                    <Icons.Cross />
                </button>
            </div>
        </div>
    );
};
