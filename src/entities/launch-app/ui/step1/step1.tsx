/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import * as amplitude from '@amplitude/analytics-browser';
import { Icons } from '@assets';
import { useCoreStore } from '@entities/core';
import { useLaunchAppStore } from '@entities/launch-app/model';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { GenerateGameImage } from '@features/generate-game-image';
import { UploadGameImage } from '@features/upload-game-image';
import { useScrollToElement } from '@hooks';
import {
    AppBanner,
    Checkbox,
    ColorPicker,
    Field,
    Input,
    Title,
    Typography,
} from '@ui';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FC, useCallback, useEffect, useRef, useState } from 'react';

// import { default as AppPreview } from '../../assets/preview.svg';
import { AppLogo } from '../app-logo';
import { AppPreview } from '../app-preview';
import { SettingsItem } from '../settings-item';
import { SoundSelector } from '../sound-selector';
import { StepBadge } from '../step-badge';
import styles from './step1.module.scss';

export const LaunchAppStep1: FC = () => {
    const {
        projectName,
        tokenName,
        tokenSymbol,
        description,
        color,
        gameImage,
        vibration,
        sounds,
        soundValue,
        logo,
        launchNewToken,
        clearStates,
    } = useLaunchAppStore();
    const closeModal = useModalStore((state) => state.closeModal);
    const scrollToRef = useRef<HTMLDivElement | null>(null);
    useScrollToElement(scrollToRef);
    const { push } = useRouter();

    const handleBackButtonClick = useCallback(() => {
        clearStates();
        push('/launch-app/0');
    }, [push, clearStates]);

    useEffect(() => {
        closeModal();
    }, [closeModal]);

    const fieldChangeHandler = useCallback(
        (key: string) => (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            useLaunchAppStore.setState({ [key]: event.target.value });
        },
        [],
    );

    const valueChangeHandler = useCallback(
        (key: string) => (value: any) => {
            useLaunchAppStore.setState({ [key]: value });
        },
        [],
    );

    const deleteGameImage = useCallback(() => {
        useLaunchAppStore.setState({ gameImage: null });
    }, []);

    const [errors, setErrors] = useState<Record<string, string>>({});

    const validateFields = useCallback(() => {
        const errors: Record<string, string> = {};

        if (!logo) {
            errors.logo = 'Logo is required';
        }

        if (projectName.length === 0) {
            errors.projectName = 'Project name is required';
        }

        if (launchNewToken) {
            if (tokenName.length === 0) {
                errors.tokenName = 'Token name is required';
            }

            if (tokenSymbol.length === 0) {
                errors.tokenSymbol = 'Token symbol is required';
            }
        }

        if (!gameImage) {
            errors.gameImage = 'Upload image for your game';
        }

        return errors;
    }, [projectName, gameImage, tokenName, tokenSymbol, logo, launchNewToken]);

    const addNotification = useNotificationsAdd();

    const onActionClick = useCallback(() => {
        const errors = validateFields();
        setErrors(errors);

        if (Object.keys(errors).length > 0) {
            addNotification('error', 'All fields required to be completed');

            return;
        }
        push('/launch-app/2');
    }, [validateFields, addNotification, push]);

    const onUploadImageError = useCallback(
        (message: string) => {
            addNotification('error', message);
        },
        [addNotification],
    );

    const { userId } = useCoreStore();

    useEffect(() => {
        if (userId) {
            amplitude.logEvent('View_Builder_ConfigureTokenPage', {
                userId,
                timestamp: moment().unix(),
            });
        }
    }, [userId]);

    return (
        <div
            style={{ '--color-app-accent': color } as any}
            className={styles.container}
        >
            <div className={styles.banner} ref={scrollToRef}>
                <AppBanner color="var(--color-bg-card)" />
            </div>

            <div className={styles.content}>
                <div className={styles.logo}>
                    <AppLogo error={errors['logo']} shape="square" />
                </div>

                <div className={styles.projectInfo}>
                    <Title className={styles.projectInfoName} size="s">
                        {projectName ? projectName : 'Project name'}
                    </Title>

                    <Typography
                        className={styles.projectInfoSymbol}
                        variant="secondary"
                        size="m"
                        weight="500"
                    >
                        {tokenName ? tokenName : 'Token Name'}
                    </Typography>
                </div>

                <div className={styles.form}>
                    <Field label="Project name">
                        <Input
                            maxLength={30}
                            value={projectName}
                            onChange={fieldChangeHandler('projectName')}
                            placeholder="Symbol"
                            error={errors['projectName']}
                        />
                    </Field>

                    <Field label="Description">
                        <Input
                            value={description}
                            onChange={fieldChangeHandler('description')}
                            element="textarea"
                            placeholder="Ex: Make your way from the shaved hamster to the grandmaster CEO of the tier-1 crypto exchange."
                            rows={4}
                            style={{ maxHeight: 200, minWidth: '100%' }}
                        />
                    </Field>

                    <div
                        onClick={() =>
                            valueChangeHandler('launchNewToken')(
                                !launchNewToken,
                            )
                        }
                        className={styles.checkbox}
                    >
                        <Checkbox checked={launchNewToken} />
                        <Typography variant="secondary" size="m" weight="500">
                            Launch new token with the game
                        </Typography>
                    </div>

                    {launchNewToken && (
                        <>
                            <Field label="Token name">
                                <Input
                                    value={tokenName}
                                    onChange={fieldChangeHandler('tokenName')}
                                    placeholder="Symbol"
                                    error={errors['tokenName']}
                                />
                            </Field>

                            <Field label="Token Symbol">
                                <Input
                                    value={tokenSymbol}
                                    onChange={fieldChangeHandler('tokenSymbol')}
                                    placeholder="Ex: HMSTR"
                                    error={errors['tokenSymbol']}
                                />
                            </Field>
                        </>
                    )}

                    <div className={styles.divider}></div>

                    <Field label="Project color">
                        <ColorPicker
                            value={color}
                            onChange={valueChangeHandler('color')}
                        />
                    </Field>

                    <Field label="Game image" description="PNG, SVG, TGS">
                        <div style={{ marginTop: 16 }}>
                            <UploadGameImage
                                onDelete={deleteGameImage}
                                value={gameImage}
                                onFileUploaded={valueChangeHandler('gameImage')}
                                error={errors['gameImage']}
                                onError={onUploadImageError}
                            >
                                <GenerateGameImage
                                    onSave={valueChangeHandler('gameImage')}
                                />
                            </UploadGameImage>
                        </div>
                    </Field>

                    <div className={styles.preview}>
                        <AppPreview
                            image={gameImage}
                            isError={!!errors['gameImage']}
                        />
                    </div>

                    <Field label="Game effects">
                        <div className={styles.settings}>
                            <SettingsItem
                                value={vibration}
                                icon={
                                    <Icons.Vibration color="var(--color-app-accent)" />
                                }
                                title="Vibration"
                                onChange={valueChangeHandler('vibration')}
                            />

                            <SoundSelector
                                enabled={sounds}
                                onChange={valueChangeHandler('sounds')}
                                soundValue={soundValue}
                                onSoundChange={valueChangeHandler('soundValue')}
                            />

                            {/* <SettingsItem
                                value={sounds}
                                icon={
                                    <Icons.Music color="var(--color-accent)" />
                                }
                                title="Sounds"
                                onChange={valueChangeHandler('sounds')}
                            /> */}
                        </div>
                    </Field>
                </div>
            </div>
            <StepBadge
                onActionClick={onActionClick}
                onPrevClick={handleBackButtonClick}
            />
        </div>
    );
};
