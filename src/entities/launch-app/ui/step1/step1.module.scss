.container {
    padding: 8px;
}

.banner {
    display: flex;

    margin-bottom: 16px;
}

.content {
    padding: 0px 8px;

    display: flex;
    flex-direction: column;

    margin-bottom: 100px;
}

.logo {
    margin-bottom: 16px;
}

.projectInfo {
    margin-bottom: 24px;

    &Name {
        margin-bottom: 8px;
    }
}

.form {
    display: flex;
    flex-direction: column;

    gap: 20px;
}

.divider {
    height: 1px;
    width: 100%;
    background: var(--color-border-divider);
}

.checkbox {
    cursor: pointer;

    display: flex;
    align-items: center;

    gap: 8px;
}

.preview {
    position: relative;

    width: 100%;
    height: 100%;

    display: flex;
    justify-content: center;
}

.settings {
    display: flex;
    flex-direction: column;

    gap: 12px;
}
