/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { useLaunchAppStore } from '@entities/launch-app';
import { UploadAppLogo } from '@features/upload-app-logo';
import { memo, useCallback } from 'react';

interface IAppLogoProps {
    error?: string;
    shape?: 'circle' | 'square';
}

export const AppLogo = memo(
    function AppLogo({ error, shape = 'circle' }: IAppLogoProps) {
        const { logo, changeLogo } = useLaunchAppStore();

        const onFileUploaded = useCallback(
            (image: any) => {
                changeLogo(image);
            },
            [changeLogo],
        );

        return (
            <UploadAppLogo
                error={error}
                value={logo}
                onFileUploaded={onFileUploaded}
                shape={shape}
            />
        );
    },
    (prevProps, nextProps) =>
        prevProps.error === nextProps.error &&
        prevProps.shape === nextProps.shape,
);
