.container {
    cursor: pointer;

    width: 115px;

    @media screen and (min-width: 1024px) {
        width: 150px;
    }

    &.selected {
        & .image img {
            border: 4px solid var(--color-accent);
        }

        & .check {
            display: flex;
        }
    }
}

.check {
    display: none;

    position: absolute;

    width: 24px;
    height: 24px;

    right: -4px;
    bottom: -4px;

    justify-content: center;
    align-items: center;

    border-radius: 50%;

    background-color: var(--color-accent);
}

.image {
    position: relative;

    & img {
        border-radius: 20px;

        border: 4px solid transparent;

        width: 100% !important;
        height: 100% !important;
    }
}

.name {
    white-space: nowrap;

    text-align: center;
}
