import { Icons } from '@assets';
import { Typography } from '@ui';
import classNames from 'classnames';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { FC } from 'react';

import styles from './ai-styles.module.scss';

interface IAiStyleProps {
    name: string;
    image: string;
    value: {
        style: string | null;
        model: string | null;
        substyle: string | null;
    };
    selected?: boolean;
    onClick: () => void;
}

export const AiStyle: FC<IAiStyleProps> = ({
    name,
    value,
    image,
    selected = false,
    onClick,
}) => (
    <motion.div
        whileTap={{ scale: 0.95 }}
        className={classNames(styles.container, {
            [styles.selected]: selected,
        })}
        onClick={onClick}
    >
        <div className={styles.image}>
            <Image src={image} alt={name} width={115} height={115} />

            <div className={styles.check}>
                <Icons.CheckMark width={20} height={20} />
            </div>
        </div>

        <div className={styles.name}>
            <Typography size="m" weight="600" variant="primary">
                {name}
            </Typography>
        </div>
    </motion.div>
);
