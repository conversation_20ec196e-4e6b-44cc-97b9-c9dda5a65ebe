import { useCurrentStep } from '@entities/launch-app/hooks';
import { Button } from '@ui';
import { FC, useMemo } from 'react';

import styles from './step-badge.module.scss';

const STEPS = 2;
interface IStepBadgeProps {
    onActionClick?: () => void;
    onPrevClick?: () => void;
    actionText?: string;
}

export const StepBadge: FC<IStepBadgeProps> = ({
    onActionClick,
    onPrevClick,
    actionText = 'Complete',
}) => {
    const currentStep = useCurrentStep();

    const progress = useMemo(() => (currentStep / STEPS) * 100, [currentStep]);

    return (
        <div className={styles.wrapper}>
            <div className={styles.container}>
                <div className={styles.progress}>
                    <div
                        style={{ width: `${progress}%` }}
                        className={styles.progressFilled}
                    ></div>
                </div>
                <div className={styles.step}>
                    {currentStep} of {STEPS}
                </div>

                <div className={styles.action}>
                    {currentStep !== 0 && (
                        <Button onClick={onPrevClick} view="outline" size="s">
                            Back
                        </Button>
                    )}

                    {currentStep !== STEPS ? (
                        <Button
                            onClick={onActionClick}
                            view={'default'}
                            size="s"
                        >
                            Next step
                        </Button>
                    ) : (
                        <Button
                            onClick={onActionClick}
                            view={'action'}
                            size="s"
                        >
                            {actionText}
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};
