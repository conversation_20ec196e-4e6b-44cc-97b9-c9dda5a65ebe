.wrapper {
    position: fixed;

    bottom: 0;
    left: 0;

    width: 100%;
    height: 64px;

    @media screen and (min-width: 1024px) {
        border-top: 1px solid var(--color-border-base);
    }
}

.container {
    background: var(--color-bg-page);

    display: flex;
    align-items: center;
    justify-content: space-between;

    padding: 12px 16px;
    padding-top: 13px;

    @media screen and (min-width: 1024px) {
        max-width: 800px;

        margin: 0 auto;
    }
}

.step {
    color: var(--color-fg-tertiary);

    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0.01px;

    padding: 6px 10px;

    border-radius: 10px;
    border: 1px solid var(--color-border-base);
}

.progress {
    position: absolute;
    top: 0px;
    left: 0px;

    width: 100%;
    height: 2px;

    background: var(--color-border-base);

    @media screen and (min-width: 1024px) {
        display: none;
    }

    &Filled {
        height: 100%;

        background: var(--color-app-accent);
    }
}

.action {
    display: flex;
    align-items: center;

    & button:not(:last-child) {
        margin-right: 8px;
    }
}
