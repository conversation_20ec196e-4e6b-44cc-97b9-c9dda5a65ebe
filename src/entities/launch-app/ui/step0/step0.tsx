/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable indent */
'use client';
import * as amplitude from '@amplitude/analytics-browser';
import { Icons } from '@assets';
import { useCoreStore } from '@entities/core';
import { aiStyles } from '@entities/launch-app/constants';
import { useGenerateAppPreset } from '@entities/launch-app/hooks';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { useIsMobile, useKeyboardInfo, useScrollToElement } from '@hooks';
import Hotjar from '@hotjar/browser';
import { Button, Title, Typography } from '@ui';
import { convertCssValueToPixels } from '@utils';
import classNames from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { default as Gradient } from '../../assets/gradient.svg';
import { AiStyle } from '../ai-style';
import styles from './step0.module.scss';

interface IStyleItem {
    name: string;
    image: string;
    value: {
        style: string | null;
        model: string | null;
        substyle: string | null;
    };
}

export const LaunchAppStep0: FC = () => {
    const inputRef = useRef<HTMLTextAreaElement | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const showModal = useModalStore((state) => state.showModal);
    const addNotification = useNotificationsAdd();
    const isMobile = useIsMobile();

    const { push } = useRouter();
    const { generate, isPending } = useGenerateAppPreset();
    const { isKeyboardVisible, keyboardHeight } = useKeyboardInfo();
    const { userId } = useCoreStore();

    const [prompt, setPrompt] = useState<string>('');
    const [isFocused, setIsFocused] = useState(false);

    const scrollToRef = useRef<HTMLDivElement | null>(null);
    useScrollToElement(scrollToRef);

    const [selectedStyle, setSelectedStyle] = useState<IStyleItem | null>(
        aiStyles[0],
    );

    const handleStyleClick = useCallback(
        (value: IStyleItem) => () => {
            setSelectedStyle(value);
        },
        [],
    );

    const buttonGenerateClickHandler = useCallback(async () => {
        inputRef.current?.blur();
        setIsFocused(false);

        if (!prompt) {
            addNotification('error', 'Write a few words');

            return;
        }

        amplitude.logEvent('Submit_Builder_AIGeneration', {
            userId,
            timestamp: moment().unix(),
            style: selectedStyle?.value.style ?? null,
            model: selectedStyle?.value.model ?? null,
            substyle: selectedStyle?.value.substyle ?? null,
            prompt,
        });

        generate({
            prompt,
            style: selectedStyle?.value.style ?? null,
            model: selectedStyle?.value.model ?? null,
            substyle: selectedStyle?.value.substyle ?? null,
        }).catch(() => {
            amplitude.logEvent('Error_Builder_AIGeneration', {
                userId,
                timestamp: moment().unix(),
            });
        });

        showModal({
            modalType: 'launchAppLoader',
            modalState: {
                text: 'Generating upgrades',
                variant: 'stars',
            },
        });
    }, [prompt, generate, showModal, selectedStyle]);

    const buttonSkipClickHandler = useCallback(() => {
        amplitude.logEvent('Clicking_Builder_ManualSettupButton', {
            userId,
            timestamp: moment().unix(),
        });

        push('/launch-app/1');
    }, [push, userId]);

    const inputHeight = useMemo(
        () =>
            isKeyboardVisible && isFocused
                ? `calc(100vh - ${keyboardHeight + 50}px)`
                : isFocused && !isKeyboardVisible
                  ? 'calc(100vh - 50px)'
                  : '40px',
        [isKeyboardVisible, keyboardHeight, isFocused],
    );

    const focusInput = useCallback(() => {
        inputRef.current?.focus();
        setIsFocused(true);
    }, []);

    const adjustTextareaHeight = useCallback(() => {
        const maxHeight = convertCssValueToPixels(inputHeight);
        if (inputRef.current) {
            inputRef.current.style.height = 'auto';
            inputRef.current.style.height = `${Math.min(
                inputRef.current.scrollHeight,
                maxHeight,
            )}px`;
        }
    }, [inputHeight]);

    useEffect(() => {
        if (inputRef.current) {
            adjustTextareaHeight();
            inputRef.current.addEventListener('input', adjustTextareaHeight);
        }

        return () => {
            inputRef.current?.removeEventListener(
                'input',
                adjustTextareaHeight,
            );
        };
    }, [adjustTextareaHeight]);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Enter') {
                buttonGenerateClickHandler();
            }
        };

        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [buttonGenerateClickHandler]);

    useEffect(() => {
        if (userId) {
            amplitude.logEvent('View_Builder_GenerationPage', {
                userId,
                timestamp: moment().unix(),
            });
        }
    }, [userId]);

    return (
        <div
            className={classNames(styles.container, {
                [styles.keyboardShow]: isKeyboardVisible,
            })}
            ref={containerRef}
            style={
                {
                    '--input-height': inputHeight,
                    '--keyboard-height': keyboardHeight,
                } as React.CSSProperties
            }
        >
            {/* <div className={styles.soft}>
                <Gradient width="100%" height="100%" />
            </div>
            <div className={styles.circle} />
            <div className={styles.light} />
            <div className={styles.hightlight} /> */}

            <AnimatePresence>
                {/* <motion.div
                    className={styles.description}
                    ref={scrollToRef}
                    initial="initial"
                    animate={
                        isMobile && (isFocused || isKeyboardVisible)
                            ? 'exit'
                            : 'enter'
                    }
                    variants={{
                        initial: {
                            opacity: 1,
                        },
                        enter: {
                            opacity: 1,
                        },
                        exit: {
                            opacity: 0,
                            height: 0,
                            padding: 0,
                        },
                    }}
                    transition={{ duration: 0.5, ease: 'easeOut' }}
                ></motion.div> */}
                <div className={styles.description}>
                    {/* <div className={styles.icon}>
                        <Icons.Stars2 />
                    </div> */}

                    <Title
                        className={styles.title}
                        variant="primary"
                        weight="600"
                        size="l"
                    >
                        Generate game
                        <br /> assets using AI
                    </Title>

                    <Typography
                        className={styles.descriptionText}
                        variant="secondary"
                        weight="400"
                        size="m"
                    >
                        The AI will generate all the game graphics
                        <br /> and narrative for your game
                    </Typography>
                </div>
            </AnimatePresence>

            <div className={styles.inputWrapper} onClick={focusInput}>
                <Title
                    className={styles.descriptionText}
                    variant="primary"
                    weight="700"
                    size="xs"
                >
                    Enter prompt
                </Title>

                <textarea
                    className={styles.input}
                    ref={inputRef}
                    onBlur={() => {
                        setTimeout(() => {
                            setIsFocused(false);
                        }, 100);
                    }}
                    placeholder="Describe the game character here, e.g. 'demon girl'"
                    value={prompt}
                    onChange={(event) => {
                        if (isPending) {
                            return;
                        }
                        setPrompt(event.target.value);
                    }}
                />
            </div>

            <div className={styles.styles}>
                <Title
                    className={styles.descriptionText}
                    variant="primary"
                    weight="700"
                    size="xs"
                >
                    Choose style
                </Title>

                <div className={styles.stylesList}>
                    {aiStyles.map((style) => (
                        <div key={style.name} className={styles.style}>
                            <AiStyle
                                selected={selectedStyle?.name === style.name}
                                onClick={handleStyleClick(style)}
                                {...style}
                            />
                        </div>
                    ))}
                </div>
            </div>

            <div className={styles.footer}>
                <motion.div
                    initial={{
                        padding: '5px 16px 42px 16px',
                    }}
                    animate={{
                        padding:
                            isMobile && (isFocused || isKeyboardVisible)
                                ? '5px 16px 16px 16px'
                                : '5px 16px 42px 16px',
                    }}
                    transition={{ duration: 0.3 }}
                    className={styles.action}
                >
                    <Typography
                        className={styles.info}
                        variant="tertiary"
                        weight="400"
                        size="m"
                    >
                        <Icons.Info />
                        You can change anything you want
                    </Typography>
                    <Button
                        onClick={buttonGenerateClickHandler}
                        view="action"
                        full
                        size="l"
                        borderRadius="ml"
                        isLoading={isPending}
                    >
                        <Icons.Stars2 width={20} height={20} />
                        Generate
                    </Button>

                    <Button
                        onClick={buttonSkipClickHandler}
                        full
                        size="l"
                        borderRadius="ml"
                    >
                        Skip and setup manual
                    </Button>
                </motion.div>
            </div>
        </div>
    );
};
