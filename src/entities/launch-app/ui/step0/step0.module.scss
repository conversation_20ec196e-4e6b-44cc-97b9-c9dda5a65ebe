.container {
    position: relative;

    padding: 16px;
    margin-bottom: 200px;

    @media screen and (min-width: 1024px) {
        padding: 0;
    }
}

.title {
}

.soft {
    pointer-events: none;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 100%;
    aspect-ratio: 4 / 3;
}

.circle {
    pointer-events: none;
    position: fixed;
    top: 23px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 100px;
    height: 100px;
    border-radius: 512px;
    background: #8863ff;
    filter: blur(125px);
}

.hightlight {
    pointer-events: none;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 100%;
    width: 24.49%;
    opacity: 0.6;
    height: 2px;
    background: radial-gradient(
        13250% 50% at 50% 0%,
        #fff 0%,
        rgba(255, 255, 255, 0) 100%
    );
    filter: blur(4px);
}

.light {
    pointer-events: none;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 100%;
    aspect-ratio: 196 / 57;
    opacity: 0.2;
    background: radial-gradient(
        50% 50% at 50% 0%,
        #8863ff 0%,
        rgba(99, 51, 255, 0) 100%
    );
}

.keyboardShow {
    margin-bottom: 0 !important;
}

.styles {
    display: flex;
    flex-direction: column;
    gap: 24px;

    &List {
        display: flex;
        justify-content: space-between;

        flex-wrap: wrap;

        gap: 16px 8px;

        @media screen and (min-width: 1024px) {
            justify-content: flex-start;

            gap: 16px 8px;
        }
    }
}

.style {
    width: 30%;

    @media screen and (min-width: 1024px) {
        width: fit-content;
    }
}

.footer {
    @media screen and (min-width: 1024px) {
        width: 100%;

        position: fixed;

        bottom: 0;
        left: 0;

        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.action {
    position: fixed;

    left: 0;
    bottom: 0;

    width: 100%;
    background: var(--color-bg-page);

    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    -webkit-box-shadow: 0px 1px 20px 20px var(--color-bg-page);
    -moz-box-shadow: 0px 1px 20px 20px var(--color-bg-page);
    box-shadow: 0px 1px 20px 20px var(--color-bg-page);

    @media screen and (min-width: 1024px) {
        position: relative;

        max-width: 800px;
        margin: 0 auto;
    }
}

.skipButton {
    border: none !important;
    color: var(--color-fg-secondary);
}

.info {
    line-height: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    & svg,
    img {
        width: 20px;
        height: 20px;
        margin-right: 4px;
    }
}

.description {
    width: 100%;

    display: flex;
    flex-direction: column;
    gap: 12px;

    transform-origin: bottom;

    margin-bottom: 16px;

    @media screen and (min-width: 1024px) {
        gap: 16px;

        margin-bottom: 40px;
    }

    &Text {
    }
}

.icon {
    position: relative;
    margin-bottom: 12px;
    z-index: 2;
    display: flex;
    width: 56px;
    height: 56px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 14px;
    border: 2px solid #fff;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, #000 100%),
        #ab8fff;
    background-blend-mode: soft-light, normal;
    color: #fff;
    & svg,
    img {
        z-index: 3;
        width: 36px;
        height: 36px;
    }
}

.icon::before {
    content: '';
    z-index: 1;
    position: absolute;
    top: -2px;
    left: -2px;
    width: 56px;
    height: 56px;
    border-radius: 14px;
    opacity: 0.65;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, #000 100%),
        #ab8fff;
    background-blend-mode: soft-light, normal;
}

.inputWrapper {
    z-index: -1;
    // min-height: calc(240px - 48px);
    margin: 24px 0px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    @media screen and (min-width: 1024px) {
        margin-bottom: 40px;
    }
}

.input {
    width: 100%;
    min-height: 90px;
    max-height: var(--input-height);
    overflow: hidden;
    resize: none;
    outline: none;
    border: none;
    background: none;
    color: white;
    z-index: -1;

    caret-color: #4294ff;
    color: var(--color-fg-primary);
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
    letter-spacing: -0.13px;
}

.input::placeholder {
    z-index: -1;
    color: var(--color-fg-placeholder);
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
    letter-spacing: -0.13px;
}
