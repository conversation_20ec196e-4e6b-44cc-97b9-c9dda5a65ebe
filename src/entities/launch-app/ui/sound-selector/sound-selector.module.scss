.container {
    background: var(--color-neutral);
    border-radius: 12px;
    overflow: hidden;

    max-height: 48px;

    &.enabled {
        max-height: 100%;
    }
}

.header {
    padding: 12px 16px;

    display: flex;
    align-items: center;
    gap: 12px;
}

.icon {
    display: flex;

    & svg,
    img {
        width: 20px;
        height: 20px;
    }
}

.title {
    flex: 1;
}

.content {
    margin: 1px;

    border-radius: 12px;

    padding-left: 16px;

    background-color: var(--color-bg-page);
}

.item {
    cursor: pointer;

    padding: 14px 16px;
    padding-left: 0;

    display: flex;
    align-items: center;
    justify-content: space-between;

    &:not(:last-child) {
        border-bottom: 1px solid var(--color-border-base);
    }
}
