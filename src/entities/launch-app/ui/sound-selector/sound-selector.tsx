'use client';
import { Icons } from '@assets';
import { RadioButton, Toggle, Typography } from '@ui';
import classNames from 'classnames';
import { FC, useCallback, useState } from 'react';

import styles from './sound-selector.module.scss';

const sounds = [
    {
        name: 'Bass',
        path: '/assets/sounds/bass.mp3',
        value: 'bass',
    },
    {
        name: 'Bleep',
        path: '/assets/sounds/bleep.mp3',
        value: 'bleep',
    },
    {
        name: 'Casino',
        path: '/assets/sounds/casino.mp3',
        value: 'casino',
    },
    {
        name: 'Clicking',
        path: '/assets/sounds/clicking.mp3',
        value: 'clicking',
    },
    {
        name: 'Keyboard',
        path: '/assets/sounds/keyboard.mp3',
        value: 'keyboard',
    },
    {
        name: 'Magic',
        path: '/assets/sounds/magic.mp3',
        value: 'magic',
    },
    {
        name: 'Power Up',
        path: '/assets/sounds/power-up.mp3',
        value: 'power-up',
    },
    {
        name: 'Retro',
        path: '/assets/sounds/retro.mp3',
        value: 'retro',
    },
    {
        name: 'Sword',
        path: '/assets/sounds/sword.mp3',
        value: 'sword',
    },
];

export const SoundSelector: FC<{
    enabled: boolean;
    onChange: (value: boolean) => void;
    soundValue: string;
    onSoundChange: (value: string) => void;
}> = ({ enabled, onChange, soundValue, onSoundChange }) => {
    const soundClickHandler = useCallback(
        (sound: { path: string; value: string; name: string }) => () => {
            const audio = new Audio(sound.path);

            audio.play();

            onSoundChange(sound.value);
        },
        [onSoundChange],
    );

    return (
        <div
            className={classNames(styles.container, {
                [styles.enabled]: enabled,
            })}
        >
            <div className={styles.header}>
                <div className={styles.icon}>
                    <Icons.Music color="var(--color-app-accent)" />
                </div>

                <Typography
                    className={styles.title}
                    weight="500"
                    variant="primary"
                    size="m"
                >
                    Sound on tap
                </Typography>

                <Toggle size="l" value={enabled} onChange={onChange} />
            </div>

            <div className={styles.content}>
                {sounds.map((sound) => (
                    <div
                        onClick={soundClickHandler(sound)}
                        className={classNames(styles.item, {
                            [styles.active]: soundValue === sound.value,
                        })}
                        key={sound.name}
                    >
                        <Typography size="m" variant="primary" weight="500">
                            {sound.name}
                        </Typography>

                        <RadioButton value={soundValue === sound.value} />
                    </div>
                ))}
            </div>
        </div>
    );
};
