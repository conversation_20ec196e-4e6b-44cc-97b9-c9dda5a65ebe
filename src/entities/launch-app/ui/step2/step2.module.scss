.container {
    padding: 16px;
}

.header {
    display: flex;
    flex-direction: column;

    gap: 12px;

    margin-bottom: 24px;
}

.form {
    margin-bottom: 100px;

    &Header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        margin-bottom: 16px;

        & span {
            display: flex;
            align-items: center;
        }
    }

    &Footer {
        width: 100%;
    }

    &Item {
        display: flex;
        justify-content: space-between;

        gap: 12px;

        & .formItem {
            &Right {
                margin-bottom: 32px;
            }
        }

        &:not(:last-child) {
            & .formItem {
                &Line {
                    width: 1px;
                    height: 100%;

                    background-color: var(--color-neutral);
                }
            }
        }

        &Header {
            display: flex;
            align-items: center;

            gap: 12px;
        }

        &Left {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        &Icon {
            cursor: pointer;

            width: 44px;
            min-height: 44px;
            max-height: 44px;

            border-radius: 50%;
            background: var(--color-neutral);

            display: flex;
            align-items: center;
            justify-content: center;

            font-size: 24px;

            border: 1px solid transparent;

            &.error {
                border: 1px solid var(--color-negative);
            }
        }

        &Right {
            flex: 1;

            display: flex;
            flex-direction: column;

            gap: 12px;
        }
    }
}

.stats {
    display: flex;
    align-items: center;
    justify-content: space-between;

    gap: 12px;

    &Item {
        flex: 1;

        display: flex;
        flex-direction: column;

        gap: 4px;

        padding: 8px 12px;

        border-radius: 12px;
        border: 1px solid var(--color-border-base);
    }

    &Price {
        display: flex;
        align-items: center;

        gap: 4px;
    }
}
