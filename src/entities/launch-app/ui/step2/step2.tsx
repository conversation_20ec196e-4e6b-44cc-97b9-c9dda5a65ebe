/* eslint-disable indent */
'use client';
import * as amplitude from '@amplitude/analytics-browser';
import { Icons } from '@assets';
import { useCoreStore } from '@entities/core';
import { useLaunchApp } from '@entities/launch-app/hooks';
import { useLaunchAppStore } from '@entities/launch-app/model';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { useScrollToElement } from '@hooks';
import { Button, Input, Title, Typography } from '@ui';
import { formatNumberToK } from '@utils';
import classNames from 'classnames';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import { v4 as randomUUID } from 'uuid';

import { StepBadge } from '../step-badge';
import styles from './step2.module.scss';

export const upgradePriceFromIndex: Record<
    number,
    { tokens: number; sol: number }
> = {
    0: {
        tokens: 0,
        sol: 0,
    },
    1: {
        tokens: 1_000,
        sol: 0.005,
    },
    2: {
        tokens: 5_000,
        sol: 0.02,
    },
    3: {
        tokens: 20_000,
        sol: 0.06,
    },
    4: {
        tokens: 80_000,
        sol: 0.2,
    },
    5: {
        tokens: 320_000,
        sol: 0.6,
    },
    6: {
        tokens: 1_280_000,
        sol: 1.15,
    },
    7: {
        tokens: 5_120_000,
        sol: 2.5,
    },
    8: {
        tokens: 20_480_000,
        sol: 4.7,
    },
    9: {
        tokens: 81_920_000,
        sol: 9.3,
    },
    10: {
        tokens: 327_680_000,
        sol: 19,
    },
};

export const gainFromIndex: Record<number, number> = {
    0: 5,
    1: 10,
    2: 60,
    3: 250,
    4: 1000,
    5: 4000,
    6: 16_000,
    7: 64_000,
    8: 256_000,
    9: 1_024_000,
    10: 4_096_000,
};

const MAX_UPGRADES_LEN = 10;

export const LaunchAppStep2: FC = () => {
    const { upgrades } = useLaunchAppStore();
    const { showModal } = useModalStore();
    const { push } = useRouter();
    const scrollToRef = useRef<HTMLDivElement | null>(null);
    useScrollToElement(scrollToRef);

    const handleBackButtonClick = useCallback(() => {
        push('/launch-app/1');
    }, [push]);

    const upgradesChangeHandler = useCallback(
        (key: string) => (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            const value = event.target.value;
            const newUpgrades = [...useLaunchAppStore.getState().upgrades];
            const upgrade = newUpgrades.find((upgrade) => upgrade.id === key);
            if (upgrade) {
                upgrade.name = value;
            }
            useLaunchAppStore.setState({ upgrades: newUpgrades });
        },
        [],
    );
    const removeClickHandler = useCallback(
        (id: string) => () => {
            useLaunchAppStore.setState({
                upgrades: useLaunchAppStore
                    .getState()
                    .upgrades.filter((upgrade) => upgrade.id !== id),
            });
        },
        [],
    );
    const emojiClickHandler = useCallback(
        (key: string, emoji: string) => () => {
            showModal({
                modalState: {
                    onChange: (emoji: string) => {
                        const newUpgrades = [
                            ...useLaunchAppStore.getState().upgrades,
                        ];
                        const upgrade = newUpgrades.find(
                            (upgrade) => upgrade.id === key,
                        );
                        if (upgrade) {
                            upgrade.emoji = emoji;
                        }
                        useLaunchAppStore.setState({ upgrades: newUpgrades });
                    },
                    value: emoji,
                },
                modalType: 'emojiPicker',
            });
        },
        [showModal],
    );

    const addUpgradeClickHandler = useCallback(() => {
        const newUpgrade = {
            id: randomUUID(),
            name: '',
            emoji: '',
        };

        useLaunchAppStore.setState({ upgrades: [...upgrades, newUpgrade] });
    }, [upgrades]);

    const [errors, setErrors] = useState<Record<string, string>>({});

    const addNotification = useNotificationsAdd();

    const validateFields = useCallback(() => {
        const errors: Record<string, string> = {};

        if (upgrades.length === 0) {
            errors.upgrades = 'At least one upgrade is required';
        } else {
            for (const upgrade of upgrades) {
                if (!upgrade.name) {
                    errors[`${upgrade.id}-name`] = 'Upgrade name is required';
                }

                if (!upgrade.emoji) {
                    errors[`${upgrade.id}-emoji`] = 'Upgrade emoji is required';
                }
            }
        }

        return errors;
    }, [upgrades]);

    const { launchApp } = useLaunchApp();

    const onActionClick = useCallback(() => {
        const errors = validateFields();
        setErrors(errors);

        if (Object.keys(errors).length > 0) {
            addNotification('error', 'All fields required to be completed');

            return;
        }

        launchApp();
    }, [validateFields, addNotification, launchApp]);

    const { userId } = useCoreStore();

    useEffect(() => {
        if (userId) {
            amplitude.logEvent('View_Builder_UpgradesPage', {
                userId,
                timestamp: moment().unix(),
            });
        }
    }, [userId]);

    return (
        <div className={styles.container}>
            <div className={styles.header} ref={scrollToRef}>
                <Title>Game upgrades</Title>

                <Typography size="m" variant="secondary">
                    Users buy upgrades to farm XP automatically.{' '}
                    <Typography weight="500" size="m" variant="primary">
                        SOLs spent on upgrades go to your token&apos;s liquidity
                        pool
                    </Typography>
                </Typography>
            </div>

            <div className={styles.form}>
                <div className={styles.formHeader}>
                    <Typography weight="600" size="l" variant="primary">
                        Edit name or icon
                    </Typography>
                    {/* <Typography weight="600" size="m" variant="accent">
                        <Icons.Dice
                            style={{ marginRight: 6 }}
                            width={16}
                            height={16}
                        />
                        Random all
                    </Typography> */}
                </div>

                <div className={styles.formContent}>
                    {upgrades.map((upgrade, index) => (
                        <div key={upgrade.id} className={styles.formItem}>
                            <div className={styles.formItemLeft}>
                                <div
                                    onClick={emojiClickHandler(
                                        upgrade.id,
                                        upgrade.emoji,
                                    )}
                                    className={classNames(styles.formItemIcon, {
                                        [styles.error]:
                                            errors[`${upgrade.id}-emoji`],
                                    })}
                                >
                                    {upgrade.emoji}
                                </div>

                                <div className={styles.formItemLine}></div>
                            </div>
                            <div className={styles.formItemRight}>
                                <div className={styles.formItemHeader}>
                                    <Input
                                        error={errors[`${upgrade.id}-name`]}
                                        style={{ height: 44 }}
                                        value={upgrade.name}
                                        onChange={upgradesChangeHandler(
                                            upgrade.id,
                                        )}
                                        placeholder="Enter name"
                                    />

                                    {/* {index > 6 ? (
                                        <Button
                                            onClick={removeClickHandler(
                                                upgrade.id,
                                            )}
                                            size="iconL"
                                            view="default"
                                        >
                                            <Icons.Close />
                                        </Button>
                                    ) : null} */}
                                </div>

                                <div className={styles.stats}>
                                    <div className={styles.statsItem}>
                                        <Typography
                                            size="s"
                                            variant="secondary"
                                        >
                                            User pay:
                                        </Typography>

                                        <Typography
                                            className={styles.statsPrice}
                                        >
                                            <Typography
                                                weight="600"
                                                size="m"
                                                variant="primary"
                                            >
                                                {upgradePriceFromIndex[index]
                                                    .tokens
                                                    ? formatNumberToK(
                                                          upgradePriceFromIndex[
                                                              index
                                                          ].tokens,
                                                      )
                                                    : 'Watch Ad'}
                                            </Typography>
                                            {upgradePriceFromIndex[index]
                                                .sol ? (
                                                <>
                                                    {' or '}
                                                    <Icons.Solana />
                                                    {
                                                        upgradePriceFromIndex[
                                                            index
                                                        ].sol
                                                    }
                                                </>
                                            ) : null}
                                        </Typography>
                                    </div>

                                    <div className={styles.statsItem}>
                                        <Typography
                                            size="s"
                                            variant="secondary"
                                        >
                                            User gain:
                                        </Typography>

                                        <Typography
                                            size="m"
                                            variant="secondary"
                                        >
                                            <Typography
                                                weight="600"
                                                size="m"
                                                variant="primary"
                                            >
                                                +
                                                {Intl.NumberFormat('en-US', {
                                                    notation: 'compact',
                                                }).format(gainFromIndex[index])}
                                            </Typography>{' '}
                                            XP per hour
                                        </Typography>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* <div className={styles.formFooter}>
                    <Button
                        disabled={upgrades.length >= MAX_UPGRADES_LEN}
                        onClick={addUpgradeClickHandler}
                        size="l"
                        view={
                            upgrades.length >= MAX_UPGRADES_LEN
                                ? 'default'
                                : 'action'
                        }
                        full
                    >
                        <Icons.Plus width={20} height={20} /> Add upgrade
                    </Button>
                </div> */}
            </div>

            <StepBadge
                onPrevClick={handleBackButtonClick}
                onActionClick={onActionClick}
            />
        </div>
    );
};
