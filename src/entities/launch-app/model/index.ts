/* eslint-disable @typescript-eslint/no-explicit-any */
import { v4 as randomUUID } from 'uuid';
import { create } from 'zustand';

interface ILaunchAppStore {
    clearStates: () => void;

    logo: any;
    changeLogo: (avatar: any) => void;

    projectName: string;
    tokenName: string;
    tokenSymbol: string;
    description: string;
    color: string;
    gameImage: any;
    vibration: boolean;
    sounds: boolean;
    soundValue: string;

    launchNewToken: boolean;

    upgrades: any[];

    tasks: any[];
}

const INITIAL_APP_STORE = {
    logo: null,
    projectName: '',
    tokenName: '',
    tokenSymbol: '',
    description: '',
    gameImage: null,
    color: '#0593FF',
    vibration: true,
    sounds: true,
    soundValue: 'bass',
    launchNewToken: true,
    upgrades: [
        {
            name: 'Check ads — get free upgrade',
            emoji: '💶',
            id: randomUUID(),
        },
        {
            name: 'Dog bone',
            emoji: '🐶',
            id: randomUUID(),
        },
        {
            name: 'Cringe Brain',
            emoji: '🧠',
            id: randomUUID(),
        },
        {
            name: 'Pizza',
            emoji: '🍕',
            id: randomUUID(),
        },
        {
            name: 'Blue Shit',
            emoji: '🪼',
            id: randomUUID(),
        },
        {
            name: 'Champion',
            emoji: '🥇',
            id: randomUUID(),
        },
        {
            name: 'Rich Boy',
            emoji: '💶',
            id: randomUUID(),
        },
        {
            name: 'Mega-Boost Potion',
            emoji: '💶',
            id: randomUUID(),
        },
        {
            name: 'Bank Bouncer Upgrade',
            emoji: '💶',
            id: randomUUID(),
        },
        {
            name: 'Rich Clicker Pack',
            emoji: '💶',
            id: randomUUID(),
        },
    ],
    tasks: [],
};

export const useLaunchAppStore = create<ILaunchAppStore>((set) => ({
    ...INITIAL_APP_STORE,
    changeLogo: (logo: any) => set({ logo }),
    clearStates: () => set(INITIAL_APP_STORE),
}));
