import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { fetchSearchApps } from '../api';

export const useSearchApps = (search: string) => {
    const { data, isLoading } = useQuery({
        queryKey: ['apps-search', search],
        queryFn: () => (search ? fetchSearchApps(search) : null),
    });

    return useMemo(
        () => ({ apps: data?.list ?? [], isLoading }),
        [data, isLoading],
    );
};
