import { Icons } from '@assets';
import { useDeferredValue } from '@hooks';
import { AppCard, Input, SearchNotFound, Typography } from '@ui';
import classNames from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { FC, useCallback, useMemo, useState } from 'react';

import { useSearchApps } from '../../hooks';
import styles from './apps-search-desktop.module.scss';

export const AppsSearchDesktop: FC = () => {
    const [search, setSearch] = useState<string>('');

    const searchChangeHandler = useCallback(
        (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            setSearch(event.target.value);
        },
        [],
    );

    const clearSearch = useCallback(() => {
        setSearch('');
    }, []);

    const searchValue = useDeferredValue(search, 500);

    const { apps, isLoading } = useSearchApps(searchValue);

    const content = useMemo(() => {
        if (apps.length === 0 && searchValue.length !== 0 && !isLoading) {
            return <SearchNotFound searchValue={searchValue} />;
        }

        return (
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={styles.list}
                key="apps-container"
            >
                {apps.map((app, index) => (
                    <AppCard.Big
                        key={app.id}
                        id={app.id}
                        name={app.name}
                        imageSrc={app.avatar}
                        color={app.color}
                        place={index + 1}
                        usersCount={app.usersCount}
                        marketCapUsd={app.token?.marketCapUsd}
                        collectedSol={app.token?.collectedSol}
                    />
                ))}
            </motion.div>
        );
    }, [apps, isLoading, searchValue]);

    return (
        <>
            <div className={styles.container}>
                <Input
                    value={search}
                    cnRadius="xxl"
                    onChange={searchChangeHandler}
                    placeholder="Search game, token, etc."
                    color="var(--color-fg-tertiary)"
                    iconLeft={<Icons.Search />}
                    iconRight={
                        !!search.length && (
                            <Icons.SearchCancel
                                className={styles.cancelSearchIcon}
                                onClick={clearSearch}
                            />
                        )
                    }
                />
            </div>
            <div
                className={classNames(styles.content, {
                    [styles.show]: !!search.length,
                })}
            >
                {isLoading ? <Icons.Logo width={92} height={32} /> : content}
            </div>
        </>
    );
};
