.opened {
    & .cancel {
        display: block;
    }

    & .content {
        display: block;
    }
}

.header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    gap: 8px;
}

.cancelSearchIcon {
    z-index: 2;
    cursor: pointer;
    color: #7b7b85;
    width: 21px;
    height: 21px;
}

.content {
    display: none;
    overflow-y: scroll;
    margin-top: 12px;
}

.notFoundContent {
    display: none;
}

.cancel {
    cursor: pointer;

    display: none;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--color-bg-page);
    z-index: var(--search-container-z-index);

    &.opened {
        display: flex;
        flex-direction: column;
        padding: 6px 16px;
    }
}
