import { Icons } from '@assets';
import { useDeferredValue } from '@hooks';
import { AppCard, Input, SearchNotFound, Typography } from '@ui';
import classNames from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { FC, useCallback, useMemo, useState } from 'react';

import { useSearchApps } from '../../hooks';
import styles from './apps-search-touch.module.scss';

export const AppsSearchTouch: FC = () => {
    const [isOpened, setIsOpened] = useState<boolean>(false);
    const [search, setSearch] = useState<string>('');

    const searchChangeHandler = useCallback(
        (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            setSearch(event.target.value);
        },
        [],
    );

    const clearSearch = useCallback(() => {
        setSearch('');
    }, []);

    const open = useCallback(() => {
        setIsOpened(true);
    }, []);

    const close = useCallback(() => {
        setSearch('');
        setIsOpened(false);
    }, []);

    const searchValue = useDeferredValue(search, 500);

    const { apps, isLoading } = useSearchApps(searchValue);

    const content = useMemo(() => {
        if (apps.length === 0 && searchValue.length !== 0 && !isLoading) {
            return <SearchNotFound searchValue={searchValue} />;
        }

        return (
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={styles.content}
                key="apps-container"
            >
                {apps.map((app, index) => (
                    <AppCard.Big
                        key={app.id}
                        id={app.id}
                        name={app.name}
                        imageSrc={app.avatar}
                        color={app.color}
                        place={index + 1}
                        usersCount={app.usersCount}
                        marketCapUsd={app.token?.marketCapUsd}
                        collectedSol={app.token?.collectedSol}
                    />
                ))}
            </motion.div>
        );
    }, [apps, isLoading, searchValue]);

    return (
        <>
            <div className={styles.header}>
                <Input
                    value={search}
                    onChange={searchChangeHandler}
                    onFocus={open}
                    placeholder="Search game, token, etc."
                    color="var(--color-fg-tertiary)"
                    iconLeft={<Icons.Search />}
                    iconRight={
                        !!search.length && (
                            <Icons.SearchCancel
                                className={styles.cancelSearchIcon}
                                onClick={clearSearch}
                            />
                        )
                    }
                />
                <div onClick={close} className={styles.cancel}>
                    <Typography variant="accent" weight="500">
                        Cancel
                    </Typography>
                </div>
            </div>
            <AnimatePresence>
                {isOpened && (
                    <motion.div
                        initial={{ y: '-100%' }}
                        animate={{ y: 0 }}
                        exit={{ y: '-100%' }}
                        transition={{ duration: 0.3 }}
                        className={classNames(styles.overlay, {
                            [styles.opened]: isOpened,
                        })}
                        onAnimationComplete={() => {
                            if (!isOpened) {
                                setIsOpened(false);
                            }
                        }}
                    >
                        <div className={styles.header}>
                            <Input
                                value={search}
                                onChange={searchChangeHandler}
                                placeholder="Search game, token, etc."
                                color="var(--color-fg-tertiary)"
                                iconLeft={<Icons.Search />}
                                iconRight={
                                    !!search.length && (
                                        <Icons.SearchCancel
                                            className={styles.cancelSearchIcon}
                                            onClick={clearSearch}
                                        />
                                    )
                                }
                            />
                            <div onClick={close} className={styles.cancel}>
                                <Typography variant="accent" weight="500">
                                    Cancel
                                </Typography>
                            </div>
                        </div>
                        <AnimatePresence mode="wait">
                            {content}{' '}
                        </AnimatePresence>
                    </motion.div>
                )}
            </AnimatePresence>
        </>
    );
};
