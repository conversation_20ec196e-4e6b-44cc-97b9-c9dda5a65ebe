.container {
    margin-top: 20px;

    width: 100%;

    padding: 0 16px;
}

.list {
    display: flex;
    flex-direction: column;

    gap: 1px;

    border-radius: 20px;

    overflow: hidden;
}

.leaders {
    transform: scale(1.04);

    margin-bottom: 12px;
}

.leader {
    width: 100%;

    display: flex;
    align-items: center;
    justify-content: space-between;

    background: var(--color-bg-card);

    padding: 12px 16px 12px 8px;

    &.absolute {
        position: fixed;
        width: calc(100% - 32px);

        border-radius: 20px;
        border: 1px solid var(--color-border-base);
        background-color: var(--color-bg-elevated);

        overflow: hidden;

        bottom: 100px;

        & .itemPosition {
            font-weight: 600;
        }
    }

    &.user {
        background: linear-gradient(
                0deg,
                var(--color-bg-card-secondary) 0%,
                var(--color-bg-card-secondary) 100%
            ),
            var(--color-bg-card) !important;
    }

    &Left {
        display: flex;
        align-items: center;

        gap: 12px;
    }

    &Position {
        display: flex;
        justify-content: center;
        align-items: center;

        width: 40px;
    }

    &Avatar {
        width: 36px;

        & img {
            border-radius: 50%;
        }
    }

    &Xp {
        display: flex;
        align-items: center;

        gap: 6px;
    }
}
