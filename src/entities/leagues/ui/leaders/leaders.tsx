import { Icons } from '@assets';
import { LeagueIcons } from '@entities/leagues/assets';
import { useLeaders } from '@entities/leagues/hooks/useLeaders';
import { Leader } from '@entities/leagues/types';
import { Typography } from '@ui';
import classNames from 'classnames';
import Image from 'next/image';
import { FC, useMemo, useState } from 'react';
import { useInView } from 'react-intersection-observer';

import styles from './leaders.module.scss';

const iconFromPosition: Record<number, React.ReactNode> = {
    1: <LeagueIcons.First width={24} height={24} />,
    2: <LeagueIcons.Second width={24} height={24} />,
    3: <LeagueIcons.Third width={24} height={24} />,
};

const LeadersItem: FC<{
    leader: Leader;
    isUser?: boolean;
    isInView?: boolean;
}> = ({ leader, isUser = false, isInView = true }) => {
    const [imageError, setImageError] = useState(false);

    const icon = useMemo(() => {
        const icon = iconFromPosition[leader.rank];

        if (!icon) {
            return leader.rank;
        }

        return icon;
    }, [leader.rank]);

    return (
        <div
            className={classNames(styles.leader, {
                [styles.absolute]: isUser && !isInView,
                [styles.user]: isUser && isInView,
            })}
        >
            <div className={styles.leaderLeft}>
                <div className={styles.leaderPosition}>
                    <Typography size="m" variant="secondary" weight="500">
                        {icon}
                    </Typography>
                </div>

                <div className={styles.leaderAvatar}>
                    {imageError ? (
                        <Icons.Avatar width={36} height={36} />
                    ) : (
                        <Image
                            src={`https://t.me/i/userpic/160/${leader.user.nickname}.jpg`}
                            width={36}
                            height={36}
                            alt={leader.user.nickname ?? 'avatar'}
                            onError={() => {
                                setImageError(true);
                            }}
                        />
                    )}
                </div>

                <div className={styles.leaderNickname}>
                    <Typography size="m" weight="500">
                        {isUser ? 'You' : leader.user.nickname}
                    </Typography>
                </div>
            </div>

            <div className={styles.leaderXp}>
                <Typography variant="primary" size="l" weight="500">
                    {Intl.NumberFormat('en-US', {
                        notation: 'compact',
                    }).format(Number(leader.xp))}
                </Typography>
                <Icons.XP width={18} height={18} />
            </div>
        </div>
    );
};

export const Leaders: FC<{
    users?: Leader[];
    userRank?: number;
    userLeague?: 'low' | 'mid' | 'high';
    selectedLeague?: 'low' | 'mid' | 'high';
}> = ({ users, userRank, userLeague, selectedLeague }) => {
    const { ref, inView } = useInView();

    const { leaders, others } = useMemo(() => {
        if (!users) {
            return { leaders: [], others: [] };
        }

        const leaders = users.slice(0, 3);
        const others = users.slice(3);

        return { leaders, others };
    }, [users]);

    return (
        <div className={styles.container}>
            {/* <div className={styles.switch}></div> */}

            <div className={styles.content}>
                <div className={classNames(styles.list, styles.leaders)}>
                    {leaders.map((leader) => (
                        <>
                            <LeadersItem
                                isUser={
                                    leader.rank === userRank &&
                                    userLeague === selectedLeague
                                }
                                leader={leader}
                                key={leader.user.id}
                            />
                        </>
                    ))}
                </div>

                <div className={styles.list}>
                    {others.map((leader) => (
                        <>
                            {leader.rank === userRank && <div ref={ref} />}

                            <LeadersItem
                                isInView={inView}
                                isUser={
                                    leader.rank === userRank &&
                                    userLeague === selectedLeague
                                }
                                leader={leader}
                                key={leader.user.id}
                            />
                        </>
                    ))}
                </div>
            </div>
        </div>
    );
};
