import { Icons } from '@assets';
import { useAppStatistic } from '@entities/earn';
import { Title, Typography } from '@ui';
import classNames from 'classnames';
import { FC, useMemo } from 'react';

import styles from './league.module.scss';

const titleFromLeague = {
    low: 'Wooden',
    mid: 'Silver',
    high: 'Diamond',
};

const emojiFromLeague = {
    low: '🪵',
    mid: '🪙',
    high: '💎',
};

export const League: FC<{
    league: 'low' | 'mid' | 'high';
    setLeague: (league: 'low' | 'mid' | 'high') => void;
    appId: string | number;
}> = ({ league, setLeague, appId }) => {
    const { statistic } = useAppStatistic(appId);

    const xp = useMemo(() => Number(statistic?.gameXp ?? 0), [statistic]);

    const status = useMemo(() => {
        if (league === 'low') {
            if (xp < 100_000) {
                return 'inProgress';
            }

            return 'reached';
        }

        if (league === 'mid') {
            if (xp < 100_000) {
                return 'locked';
            }

            if (xp < 1_000_000) {
                return 'inProgress';
            }

            return 'reached';
        }

        if (league === 'high') {
            if (xp < 1_000_000) {
                return 'locked';
            }

            if (xp < 100_000_000) {
                return 'inProgress';
            }

            return 'reached';
        }

        return 'reached';
    }, [xp, league]);

    const goal = useMemo(() => {
        if (xp < 100_000) {
            return 100_000;
        }

        if (xp < 1_000_000) {
            return 1_000_000;
        }

        return 100_000_000;
    }, [xp]);

    const percentage = useMemo(() => Math.round((xp / goal) * 100), [xp, goal]);

    const leftClickHandler = () => {
        if (league === 'low') {
            return;
        }

        if (league === 'mid') {
            setLeague('low');
        }

        if (league === 'high') {
            setLeague('mid');
        }
    };

    const rightClickHandler = () => {
        if (league === 'high') {
            return;
        }

        if (league === 'mid') {
            setLeague('high');
        }

        if (league === 'low') {
            setLeague('mid');
        }
    };

    return (
        <div className={classNames(styles.container, styles[league])}>
            {league !== 'low' && (
                <div
                    onClick={leftClickHandler}
                    className={classNames(styles.angle, styles.angleLeft)}
                >
                    <Icons.AngleLeft width={24} height={24} />
                </div>
            )}
            {league !== 'high' && (
                <div
                    onClick={rightClickHandler}
                    className={classNames(styles.angle, styles.angleRight)}
                >
                    <Icons.AngleRight width={24} height={24} />
                </div>
            )}
            <div className={styles.header}>
                <Title size="xxl" className={styles.title}>
                    {titleFromLeague[league]}
                </Title>
                <div className={styles.icon}>{emojiFromLeague[league]}</div>
            </div>

            <div className={classNames(styles.progress, styles[status])}>
                {status === 'reached' && (
                    <Typography size="m" className={styles.progressText}>
                        <Icons.CheckCircle
                            width={20}
                            height={20}
                            style={{ marginRight: 8 }}
                        />{' '}
                        Reached
                    </Typography>
                )}

                {status === 'locked' && (
                    <Typography size="m" className={styles.progressText}>
                        <Icons.Lock
                            width={20}
                            height={20}
                            style={{ marginRight: 8 }}
                        />{' '}
                        {league === 'mid' && (
                            <>
                                Collect a minimum of 100K XP
                                <br /> to enter the Silver league
                            </>
                        )}
                        {league === 'high' && (
                            <>
                                Collect a minimum of 1 million XP
                                <br /> to enter the Diamond league
                            </>
                        )}
                    </Typography>
                )}

                {status === 'inProgress' && (
                    <>
                        <div
                            style={{ width: `${percentage}%` }}
                            className={classNames(
                                styles.progressBar,
                                styles[`${league}Bar`],
                            )}
                        ></div>

                        <div className={styles.content}>
                            <Title
                                className={styles.contentTitle}
                                size="s"
                                variant="white"
                                weight="700"
                            >
                                {Intl.NumberFormat('ru-RU', {
                                    notation: 'standard',
                                }).format(xp)}{' '}
                                <Icons.XP width={20} height={20} />
                            </Title>

                            {league !== 'high' && (
                                <Typography
                                    className={styles.contentDescription}
                                    size="m"
                                >
                                    of{' '}
                                    {Intl.NumberFormat('ru-RU', {
                                        notation: 'standard',
                                    }).format(goal)}
                                </Typography>
                            )}
                        </div>
                    </>
                )}
            </div>
            <div className={styles.background}></div>
        </div>
    );
};
