.container {
    position: relative;

    width: 100%;
    height: 264px;

    border-radius: 0px 0px 44px 44px;
    overflow: hidden;
    background-color: var(--color-bg-page);

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    padding: 8px;
    padding-top: 24px;
}

.angle {
    z-index: 100;

    cursor: pointer;

    position: absolute;

    width: 44px;
    height: 44px;

    display: flex;
    justify-content: center;
    align-items: center;

    opacity: 0.48;

    top: 30%;

    &Left {
        left: 8px;
    }

    &Right {
        right: 8px;
    }
}

.low {
    & .background {
        mask-image: radial-gradient(
            149.5% 100% at 50% 0%,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.01) 13.84%,
            rgba(255, 255, 255, 0.04) 25.54%,
            rgba(255, 255, 255, 0.09) 35.36%,
            rgba(255, 255, 255, 0.14) 43.55%,
            rgba(255, 255, 255, 0.21) 50.37%,
            rgba(255, 255, 255, 0.29) 56.08%,
            rgba(255, 255, 255, 0.37) 60.94%,
            rgba(255, 255, 255, 0.46) 65.2%,
            rgba(255, 255, 255, 0.55) 69.12%,
            rgba(255, 255, 255, 0.63) 72.96%,
            rgba(255, 255, 255, 0.72) 76.98%,
            rgba(255, 255, 255, 0.8) 81.44%,
            rgba(255, 255, 255, 0.88) 86.59%,
            rgba(255, 255, 255, 0.94) 92.69%,
            #fff 100%
        );
        background: rgba(135, 102, 79, 1);
    }

    & .title {
        background: #87664f;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        mask-image: linear-gradient(
            0deg,
            rgba(159, 159, 191, 0.08) 0%,
            #9f9fbf 100%
        );
    }

    // & .progress {
    //     &.inProgress {
    //         border: 1px solid rgba(255, 255, 255, 0.00);
    //         background: linear-gradient(180deg, #131314 0%, rgba(19, 19, 20, 0.48) 100%);
    //     }
    // }
}

.high {
    & .background {
        mask-image: radial-gradient(
            149.5% 100% at 50% 0%,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.01) 13.84%,
            rgba(255, 255, 255, 0.04) 25.54%,
            rgba(255, 255, 255, 0.09) 35.36%,
            rgba(255, 255, 255, 0.14) 43.55%,
            rgba(255, 255, 255, 0.21) 50.37%,
            rgba(255, 255, 255, 0.29) 56.08%,
            rgba(255, 255, 255, 0.37) 60.94%,
            rgba(255, 255, 255, 0.46) 65.2%,
            rgba(255, 255, 255, 0.55) 69.12%,
            rgba(255, 255, 255, 0.63) 72.96%,
            rgba(255, 255, 255, 0.72) 76.98%,
            rgba(255, 255, 255, 0.8) 81.44%,
            rgba(255, 255, 255, 0.88) 86.59%,
            rgba(255, 255, 255, 0.94) 92.69%,
            #fff 100%
        );
        background-color: rgba(90, 189, 255, 1);
    }

    & .title {
        background: #5abdff;

        mask-image: linear-gradient(
            0deg,
            rgba(159, 159, 191, 0.08) 0%,
            #9f9fbf 100%
        );
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.mid {
    & .background {
        background: rgba(100, 100, 105, 1);

        mask-image: radial-gradient(
            149.5% 100% at 50% 0%,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.01) 13.84%,
            rgba(255, 255, 255, 0.04) 25.54%,
            rgba(255, 255, 255, 0.09) 35.36%,
            rgba(255, 255, 255, 0.14) 43.55%,
            rgba(255, 255, 255, 0.21) 50.37%,
            rgba(255, 255, 255, 0.29) 56.08%,
            rgba(255, 255, 255, 0.37) 60.94%,
            rgba(255, 255, 255, 0.46) 65.2%,
            rgba(255, 255, 255, 0.55) 69.12%,
            rgba(255, 255, 255, 0.63) 72.96%,
            rgba(255, 255, 255, 0.72) 76.98%,
            rgba(255, 255, 255, 0.8) 81.44%,
            rgba(255, 255, 255, 0.88) 86.59%,
            rgba(255, 255, 255, 0.94) 92.69%,
            #fff 100%
        );
    }

    & .title {
        background: #646469;

        mask-image: linear-gradient(
            0deg,
            rgba(159, 159, 191, 0.08) 0%,
            #9f9fbf 100%
        );
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    // & .progress {
    //     &.inProgress {
    //         background: #646469;
    //         border: 1.5px solid #FFF;
    //         box-shadow: -5px -8px 24px 0px rgba(255, 255, 255, 0.32) inset;
    //     }
    // }
}

.background {
    z-index: 0;

    position: absolute;
    left: 0;
    top: 0;

    height: 100%;
    width: 100%;
}

.header {
    z-index: 20;

    position: relative;

    display: flex;
    flex-direction: column;
    align-items: center;
}

.icon {
    cursor: default;
    position: absolute;
    display: flex;

    font-size: 96px;

    top: 14px;
}

.title {
    cursor: default;
}

.progress {
    position: relative;

    z-index: 10;

    width: 100%;
    height: 72px;

    border-radius: 36px;

    display: flex;
    justify-content: center;
    align-items: center;

    overflow: hidden;

    &.locked {
        background: rgba(255, 255, 255, 0.08);
    }

    &.inProgress {
        // border: 1.5px solid #FFF;
        box-shadow: -5px -8px 24px 0px rgba(255, 255, 255, 0.32) inset;
    }

    &.reached {
        background: rgba(255, 255, 255, 0.08);
    }

    &Bar {
        position: absolute;

        height: 100%;
        left: 0;

        z-index: 0;

        border-radius: 50px 4px 4px 50px;
        // border: 1.5px solid #FFF;
        box-shadow: -5px -8px 24px 0px rgba(255, 255, 255, 0.32) inset;

        &.midBar {
            background: #646469;
        }

        &.lowBar {
            background: #87664f;
        }
    }

    & .content {
        z-index: 1;

        display: flex;
        flex-direction: column;
        align-items: center;

        &Title {
            display: flex;
            align-items: center;

            gap: 4px;
        }

        &Description {
            opacity: 0.64;
        }
    }

    &.locked {
        & .progressText {
            color: rgba(255, 255, 255, 0.6) !important;
        }
    }

    &.reached {
        & .progressText {
            color: var(--color-positive) !important;
        }
    }

    &Text {
        display: flex;
        align-items: center;

        text-align: center;
    }
}
