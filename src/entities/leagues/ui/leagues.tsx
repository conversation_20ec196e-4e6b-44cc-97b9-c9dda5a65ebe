'use client';
import * as amplitude from '@amplitude/analytics-browser';
import { useApp } from '@entities/app';
import { useCoreStore } from '@entities/core';
import { useTelegramRouterBack } from '@hooks';
import { getContrastRatio } from '@utils';
import { AppNavTouch } from '@widgets/app-nav';
import moment from 'moment';
import { CSSProperties, FC, useEffect, useMemo } from 'react';

import { useLeaders } from '../hooks';
import { Leaders } from './leaders';
import { League } from './league';
import styles from './leagues.module.scss';

export const Leagues: FC<{ appId: string }> = ({ appId }) => {
    useTelegramRouterBack('/');
    const { app } = useApp(appId);
    const {
        users,
        isLoading,
        selectedLeague,
        setSelectedLeague,
        userRank,
        userLeague,
    } = useLeaders(appId);

    const { userId } = useCoreStore();

    useEffect(() => {
        amplitude.logEvent('View_LeaderboardPage', {
            userId: userId,
            timestamp: moment().unix(),
            appId: appId,
        });
    }, [userId, appId]);

    const appAccentColor = useMemo(
        () => app?.color ?? 'var(--color-accent)',
        [app],
    );

    const appTextColor = useMemo(() => {
        if (!app) {
            return '#fff';
        }

        const ratio = getContrastRatio(app.color, '#fff');

        return ratio >= 4.5 ? '#fff' : '#000';
    }, [app]);

    return (
        <div
            className={styles.container}
            style={
                {
                    '--color-app-accent': appAccentColor,
                    '--color-app-accent-text': appTextColor,
                } as CSSProperties
            }
        >
            <div className={styles.header}>
                <League
                    league={selectedLeague}
                    setLeague={setSelectedLeague}
                    appId={appId}
                />
            </div>

            <div className={styles.content}>
                <Leaders
                    users={users}
                    userRank={userRank}
                    userLeague={userLeague}
                    selectedLeague={selectedLeague}
                />
            </div>

            <div className={styles.footer}>
                <AppNavTouch
                    appId={appId}
                    avatar={app.avatar}
                    isTokenExist={!!app.token}
                />
            </div>
        </div>
    );
};
