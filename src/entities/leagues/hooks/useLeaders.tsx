import { useQuery } from '@tanstack/react-query';
import { useMemo, useState } from 'react';

import { fetchLeaders } from '../api';

export const useLeaders = (gameId: string | number) => {
    const [selectedLeague, setSelectedLeague] = useState<
        'low' | 'mid' | 'high'
    >('low');

    const { data, isLoading } = useQuery({
        queryKey: ['leaders', gameId, selectedLeague],
        queryFn: () => fetchLeaders(gameId, selectedLeague),
    });

    return useMemo(
        () => ({
            users: data?.list,
            userRank: data?.userRank?.rank,
            userLeague: data?.userRank?.league,
            isLoading,
            selectedLeague,
            setSelectedLeague,
        }),
        [data, isLoading, selectedLeague, setSelectedLeague],
    );
};
