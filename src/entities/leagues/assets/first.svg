<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="24" height="24" rx="12" fill="#E28328"/>
<rect width="24" height="24" rx="12" fill="url(#paint0_radial_11050_47045)" fill-opacity="0.32"/>
<rect x="0.5" y="0.5" width="23" height="23" rx="11.5" stroke="url(#paint1_linear_11050_47045)" stroke-opacity="0.32"/>
<g filter="url(#filter0_d_11050_47045)">
<path d="M18.2802 8.24862C17.8539 8.07195 17.3343 8.17485 17.0085 8.50048L15.5 10.0012L12.8248 7.33982C12.6061 7.12223 12.3094 7 12 7C11.6906 7 11.3939 7.12223 11.1752 7.33982L8.5 10.0012L6.9915 8.50048C6.66518 8.17594 6.14654 8.07334 5.72024 8.24899C5.29394 8.42464 5.0001 8.86202 5 9.32106C5 10.2659 5.37861 11.912 5.78362 13.4026C6.07897 14.4895 6.22664 15.033 6.57962 15.4907C6.87783 15.8774 7.32336 16.218 7.77468 16.4044C8.3089 16.625 8.91084 16.625 10.1147 16.625H13.8853C15.0892 16.625 15.6911 16.625 16.2253 16.4044C16.6766 16.218 17.1222 15.8774 17.4204 15.4907C17.7734 15.033 17.921 14.4895 18.2164 13.4026C18.6214 11.912 19 10.2659 19 9.32106C19.0001 8.8791 18.6905 8.41789 18.2802 8.24862Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_11050_47045" x="4" y="7" width="16" height="11.625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.5 0 0 0 0 0.241667 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11050_47045"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11050_47045" result="shape"/>
</filter>
<radialGradient id="paint0_radial_11050_47045" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12) rotate(90) scale(24 22.338)">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</radialGradient>
<linearGradient id="paint1_linear_11050_47045" x1="12" y1="0" x2="12" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
