import { axios } from '@constants';

import { Leader } from '../types';

const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL!;

export const fetchLeaders = async (gameId: string | number, league: string) =>
    await axios
        .get<{
            list: Leader[];
            userRank: {
                rank: number;
                league: 'low' | 'mid' | 'high';
            };
        }>(`${apiUrl}/user_statistic/${gameId}/leaderBoardLeague`, {
            params: {
                league,
                skip: 0,
                take: 100,
            },
            headers: {
                withAuth: 'true',
            },
        })
        .then((res) => res.data);
