'use client';
import { Carousel } from '@ui';
import { FC, useMemo } from 'react';

import styles from './apps-carousel.module.scss';

interface IAppsCarouselProps {
    items: React.ReactNode[];
}

export const AppsCarousel: FC<IAppsCarouselProps> = ({ items }) => (
    <div className={styles.container}>
        <Carousel
            items={items}
            speed="medium"
            direction="left"
            className={styles.carousel}
            gap={12}
        />
        <Carousel
            items={items}
            speed="low"
            direction="right"
            className={styles.carousel}
            gap={12}
        />
    </div>
);
