import { create } from 'zustand';

const getAccessToken = () =>
    typeof window !== 'undefined'
        ? window.localStorage.getItem('accessToken')
        : undefined;

const getRefreshToken = () =>
    typeof window !== 'undefined'
        ? window.localStorage.getItem('refreshToken')
        : undefined;

interface CoreState {
    isTma?: boolean;
    initDataRaw?: string;
    urlAuthData?: string;
    accessToken?: string;
    refreshToken?: string;
    userId?: string;
}

export const useCoreStore = create<CoreState>()((_set) => ({
    isTma: undefined,
    initDataRaw: undefined,
    urlAuthData: undefined,
    accessToken: getAccessToken() || undefined,
    refreshToken: getRefreshToken() || undefined,
    userId: undefined,
}));
