'use client';

import { App } from '@types';
import { Button, Typography } from '@ui';
import { useRouter } from 'next/navigation';
import { FC, useCallback } from 'react';

import styles from './random-game.module.scss';

interface IRandomGameProps {
    apps: App[];
    gamesCount: number;
}

export const RandomGame: FC<IRandomGameProps> = ({ apps, gamesCount }) => {
    const router = useRouter();

    const playRandomHandler = useCallback(() => {
        router.push(
            `/app/${apps[Math.floor(Math.random() * apps.length)].id}/earn`,
        );
    }, [router, apps]);

    return (
        <div className={styles.container}>
            <Typography
                weight="600"
                variant="primary"
                size="xxl"
                className={styles.description}
            >
                {gamesCount}
            </Typography>
            <Typography
                weight="400"
                variant="tertiary"
                size="lm"
                className={styles.description}
            >
                games launched on Soda
            </Typography>
            <Button
                onClick={playRandomHandler}
                view="action"
                size="ml"
                borderRadius="xxl"
                className={styles.button}
            >
                Play random game
            </Button>
        </div>
    );
};
