'use client';
import { Icons } from '@assets';
import { Trade } from '@entities/app-trades/types';
import { openLink } from '@telegram-apps/sdk-react';
import {
    AmountTemplate,
    DealType,
    InvestorTemplate,
    ITableColumnConfig,
    ITableConfig,
    ITableRowConfig,
    TypeTemplate,
    Typography,
} from '@ui';
import { convertTimestampAgo } from '@utils';
import { useIsTma } from '@hooks';
import { useCoreStore } from '@entities/core';

import styles from './table.module.scss';

export const liveInvestorCellsConfig: ITableColumnConfig<Trade>[] = [
    {
        title: 'Investor',
        headRows: 2,
        getBodyCell: (dataItem) => InvestorTemplate({ investor: dataItem }),
        bodyCellRows: 1,
    },
    {
        title: 'Type',
        headRows: 1,
        getBodyCell: (dataItem) =>
            TypeTemplate({
                type: dataItem.isBuy ? DealType.Buy : DealType.Sell,
            }),
        bodyCellRows: 1,
    },
    {
        title: 'Sol',
        headRows: 1,
        getBodyCell: (dataItem) =>
            AmountTemplate({ trade: dataItem, isToken: false }),
        bodyCellRows: 1,
    },
    {
        title: 'Token',
        headRows: 1,
        getBodyCell: (dataItem) =>
            AmountTemplate({ trade: dataItem, isToken: true }),
        bodyCellRows: 1,
    },
    {
        title: 'Time',
        headRows: 1,
        getBodyCell: (dataItem) => (
            <div
                style={{
                    maxWidth: '100%',
                }}
            >
                <p
                    style={{
                        textAlign: 'left',
                        color: 'var(--color-fg-tertiary)',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%',
                    }}
                >
                    {convertTimestampAgo(dataItem.timestampTrade)}
                </p>
            </div>
        ),
        bodyCellRows: 1,
    },
    {
        title: 'Transaction',
        headRows: 1,
        getBodyCell: (dataItem) => (
            <Typography className={styles.link} variant="app-accent" size="m">
                <Icons.Link /> Open link
            </Typography>
        ),
        bodyCellRows: 1,
    },
];

export const liveInvestorRowConfig: ITableRowConfig<Trade> = {
    getOnClick: (dataItem) => () => {
        const isTma = useCoreStore.getState().isTma;
        const url = `https://explorer.sophon.xyz/tx/${dataItem.signature}`;

        if (isTma) {
            openLink(url);
        } else {
            window.open(url, '_blank');
        }
    },
    getKey: (dataItem) => dataItem.signature,
    getRecentRow: (dataItem) => {
        const currentTime = Math.floor(Date.now() / 1000);
        const transactionTime = Number(dataItem.timestampTrade);
        const timeDifference = currentTime - transactionTime;

        return timeDifference <= 120;
    },
};

export const liveInvestorConfig: ITableConfig<Trade> = {
    cells: liveInvestorCellsConfig,
    row: liveInvestorRowConfig,
};

export const topInvestorCellsConfig: ITableColumnConfig<Trade>[] = [
    {
        title: '#',
        headRows: 1,
        getBodyCell: (dataItem, index) => (
            <div
                style={{
                    maxWidth: '100%',
                }}
            >
                <p
                    style={{
                        textAlign: 'left',
                        color: 'var(--color-fg-tertiary)',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%',
                    }}
                >
                    {index + 1}
                </p>
            </div>
        ),
        bodyCellRows: 1,
    },
    {
        title: 'Investor',
        headRows: 1,
        getBodyCell: (dataItem) => InvestorTemplate({ investor: dataItem }),
        bodyCellRows: 1,
    },
    {
        title: 'Bid, TON',
        headRows: 1,
        getBodyCell: (dataItem) => AmountTemplate({ trade: dataItem }),
        bodyCellRows: 1,
        bodyCellStyle: { display: 'flex', justifyContent: 'flex-end' },
    },
];

export const topInvestorRowConfig: ITableRowConfig<Trade> = {
    getOnClick: (dataItem) => () =>
        openLink(`https://testnet.tonviewer.com/${dataItem.traderAddress}`),
    getKey: (dataItem) => dataItem.signature,
};

export const topInvestorConfig: ITableConfig<Trade> = {
    cells: topInvestorCellsConfig,
    row: topInvestorRowConfig,
};
