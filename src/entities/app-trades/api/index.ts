import { axios } from '@constants';

import { AppTradesResponse } from '../types';

const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

export const fetchAppTrades = async (
    appId: number | string,
    pageParam: number,
) =>
    await axios
        .get<AppTradesResponse>(`${apiUrl}/game/${appId}/transactions`, {
            params: {
                skip: pageParam * 10,
                take: 10,
            },
        })
        .then((res) => res.data);
