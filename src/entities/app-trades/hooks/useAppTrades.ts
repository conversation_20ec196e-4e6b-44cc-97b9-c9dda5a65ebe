import { useInfiniteQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { fetchAppTrades } from '../api';
import { AppTradesResponse } from '../types';

export const useAppTrades = (enabled: boolean, appId?: string | number) => {
    const { data, isLoading, hasNextPage, fetchNextPage } =
        useInfiniteQuery<AppTradesResponse>({
            queryKey: ['app-trades', String(appId)],
            queryFn: async ({ pageParam = 0 }) => {
                if (appId === undefined || appId === null) {
                    return { list: [], count: 0 };
                }

                const page = pageParam as number;

                return await fetchAppTrades(appId, page);
            },
            getNextPageParam: (lastPage, pages) => {
                const nextPage = pages.length;
                const previousInvestors = pages.reduce(
                    (acc, page) => acc + page.list.length,
                    0,
                );

                return previousInvestors < lastPage.count
                    ? nextPage
                    : undefined;
            },
            enabled: appId !== undefined && appId !== null && enabled,
            initialPageParam: 0,
            refetchInterval: 10_000,
            maxPages: 5,
        });

    const trades = useMemo(
        () => data?.pages.flatMap((page) => page.list) ?? [],
        [data],
    );

    return useMemo(
        () => ({
            trades,
            isLoading,
            hasNextPage,
            fetchNextPage,
        }),
        [trades, isLoading, hasNextPage, fetchNextPage],
    );
};
