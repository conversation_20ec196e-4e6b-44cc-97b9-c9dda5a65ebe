export interface User {
    id?: number;
    nickname?: string;
    telegramId?: number;
    avatar?: string;
}

export interface Trade {
    id: number;
    amountToken: string;
    amountSol: string;
    price: string;
    tokenAddress: string;
    timestampTrade: string;
    signature: string;
    tradingFee: string;
    traderAddress: string;
    user: User;
    isBuy: boolean;
}

export interface AppTradesResponse {
    list: Trade[];
    count: number;
}
