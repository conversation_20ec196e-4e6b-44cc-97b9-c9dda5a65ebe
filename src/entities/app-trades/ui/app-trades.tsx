'use client';
import { Label, Table, Title } from '@ui';
import { FC, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';

import { useAppTrades } from '../hooks/useAppTrades';
import { liveInvestorConfig } from '../lib/configs';
import styles from './app-trades.module.scss';

export const AppTrades: FC<{ appId: string | number }> = ({ appId }) => {
    const { ref, inView } = useInView();

    const { trades, isLoading, hasNextPage, fetchNextPage } = useAppTrades(
        true,
        appId,
    );

    useEffect(() => {
        if (inView && hasNextPage) {
            fetchNextPage();
        }
    }, [inView, hasNextPage, fetchNextPage]);

    return (
        <div className={styles.container}>
            <div className={styles.title}>
                <Title size="xs">Transactions</Title>
            </div>
            {/* <div className={styles.buttonContainer}>
                <Button
                    onClick={() => setActiveMode(ActiveMode.Top)}
                    view={'outline'}
                    size="m"
                    borderRadius="l"
                    fill={activeMode === ActiveMode.Top}
                >
                    <Icons.Dollar />
                    Top Holders
                </Button>
                <Button
                    onClick={() => setActiveMode(ActiveMode.Live)}
                    view={'outline'}
                    size="m"
                    borderRadius="l"
                    fill={activeMode === ActiveMode.Live}
                >
                    <Icons.Live />
                    Live Transactions
                </Button>
            </div> */}
            <Table
                className={styles.table}
                tbodyClassName={styles.tbody}
                isEmpty={!trades.length && !isLoading}
                isLoading={isLoading}
                ref={ref}
                dataSet={trades}
                config={liveInvestorConfig}
            />
        </div>
    );
};
