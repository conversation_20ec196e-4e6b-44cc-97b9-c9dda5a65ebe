'use client';
import { useCoreStore } from '@entities/core';
import { useEffect, useLayoutEffect } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { MAX_RECENT_APPS, RECENT_APPS_STORAGE_KEY } from '../constants';

interface RecentAppStore {
    recentAppsIds: number[];
    addAppId: (id: number) => void;
    setRecentAppsIds: (ids: number[]) => void;
}

export const useRecentAppsHandler = () => {
    const isTma = useCoreStore((store) => store.isTma);
    const { recentAppsIds, setRecentAppsIds } = useRecentAppsStore();

    useLayoutEffect(() => {
        (async () => {
            // get all recent apps from storage on init
            if (isTma && recentAppsIds.length === 0) {
                const storedAppIds = await localStorage.getItem(
                    RECENT_APPS_STORAGE_KEY,
                );
                if (storedAppIds) {
                    const recentApps =
                        storedAppIds.match(/\d+/g)?.map(Number) ?? [];
                    setRecentAppsIds(recentApps);
                }
            }
        })();
    }, [isTma, recentAppsIds, setRecentAppsIds]);

    useEffect(() => {
        localStorage.setItem(
            RECENT_APPS_STORAGE_KEY,
            JSON.stringify(recentAppsIds),
        );
    }, [recentAppsIds]);
};

export const useRecentAppsStore = create<RecentAppStore>()(
    devtools(
        immer((set) => ({
            recentAppsIds: [],
            addAppId: (id: number) =>
                set((state) => {
                    const newAppsIds = [
                        id,
                        ...state.recentAppsIds.filter((appId) => appId !== id),
                    ];
                    if (newAppsIds.length > MAX_RECENT_APPS) {
                        newAppsIds.pop();
                    }
                    state.recentAppsIds = newAppsIds;
                }),
            setRecentAppsIds: (ids: number[]) =>
                set((state) => {
                    state.recentAppsIds = ids;
                }),
        })),
        { name: 'recentAppIdsStore' },
    ),
);

export { useRecentApps } from './use-recent-apps';
