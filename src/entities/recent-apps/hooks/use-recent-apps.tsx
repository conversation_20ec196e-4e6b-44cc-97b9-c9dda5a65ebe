import { useQuery } from '@tanstack/react-query';
import { FC, useMemo } from 'react';

import { fetchRecentApps } from '../api';

export const useRecentApps = () => {
    const { data, isLoading } = useQuery({
        queryKey: ['recent-apps'],
        queryFn: fetchRecentApps,
    });

    return useMemo(
        () => ({
            apps: data?.list ?? [],
            count: data?.count ?? 0,
            isLoading,
        }),
        [data, isLoading],
    );
};
