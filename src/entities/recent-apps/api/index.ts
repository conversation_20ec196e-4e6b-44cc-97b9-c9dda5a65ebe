import { axios } from '@constants';
import { App } from '@types';

const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

export const fetchRecentApps = () =>
    axios
        .get<{ list: App[]; count: number }>(`${apiUrl}/game/played`, {
            params: {
                skip: 0,
                take: 50,
            },
            headers: {
                withAuth: true,
            },
        })
        .then((res) => res.data);
