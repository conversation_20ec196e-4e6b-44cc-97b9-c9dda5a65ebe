'use client';
import { useTelegramRouterBack } from '@hooks';
import { AppCard, Title } from '@ui';
import { FC } from 'react';

import { useRecentApps } from '../../hooks';
import styles from './recent-apps-list.module.scss';

export const RecentAppsList: FC = () => {
    useTelegramRouterBack('/');
    const { apps, count } = useRecentApps();

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <Title size="l" weight="600">
                    Recent
                </Title>
            </div>
            <div className={styles.content}>
                {apps?.map((app, index) => (
                    <AppCard.Big
                        key={`slider-group-item-${app.name}-${index}`}
                        name={app.name}
                        imageSrc={app.avatar}
                        color={app.color}
                        id={app.id}
                        usersCount={app.usersCount}
                        marketCapUsd={app?.token?.marketCapUsd}
                        collectedSol={app?.token?.collectedSol}
                    />
                ))}
            </div>
        </div>
    );
};
