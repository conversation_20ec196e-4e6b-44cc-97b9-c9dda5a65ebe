'use client';
import {
    useR<PERSON>entApps,
    useRecentAppsHandler,
} from '@entities/recent-apps/hooks';
import { AppCard, Typography } from '@ui';
import { AppsFeed } from '@widgets/apps-feed';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FC, useCallback } from 'react';

import { Icons } from '../../assets';
import styles from './recent-apps-row.module.scss';

export const RecentAppsRow: FC = () => {
    const { apps: recentApps, count } = useRecentApps();
    const router = useRouter();

    const appClickHandler = useCallback(
        (id: number) => () => {
            router.push(`/app/${id}`);
        },
        [router],
    );

    if (!recentApps.length) {
        return null;
    }

    return (
        <AppsFeed
            icon={<Icons.Recent />}
            title="Recently Played"
            href="/recent-apps"
        >
            <div className={styles.recentRow}>
                {recentApps.map((app) => (
                    <AppCard.Small
                        onClick={appClickHandler(app.id)}
                        key={app.id}
                        title={app.name}
                        image={app.avatar}
                        color={app.color}
                    />
                ))}
                {count > 100 && (
                    <Link href="/recent-apps" className={styles.recentLast}>
                        <Typography size="l" variant="tertiary" weight="500">
                            +{count - recentApps.length}
                        </Typography>
                    </Link>
                )}
            </div>
        </AppsFeed>
    );
};
