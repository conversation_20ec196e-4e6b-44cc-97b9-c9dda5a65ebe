import { useCoreStore } from '@entities/core';
import { useQuery } from '@tanstack/react-query';
import { parseInitDataQuery } from '@telegram-apps/sdk-react';
import { useMemo } from 'react';

import { fetchUserInfo } from '../api';

export const useUserInfo = () => {
    const initDataRaw = useCoreStore((store) => store.initDataRaw);

    const { data, isLoading, error } = useQuery({
        queryKey: ['waitlistUserInfo', initDataRaw],
        queryFn: async () => {
            const user = await fetchUserInfo(initDataRaw!);

            const initData = parseInitDataQuery(initDataRaw!);

            return {
                ...user,
                telegramId: initData.user?.id
                    ? String(initData.user?.id)
                    : undefined,
                nickname: initData.user?.username,
            };
        },
        enabled: !!initDataRaw,
    });

    return useMemo(
        () => ({
            user: data,
            isLoading,
            error,
        }),
        [data, isLoading, error],
    );
};
