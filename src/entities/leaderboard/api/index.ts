/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from 'axios';

const apiUrl = process.env.NEXT_PUBLIC_WL_BACKEND_URL;

export const fetchUserInfo = async (webAppInit: any) =>
    await axios
        .post<{
            wl: boolean;
            referralsCount: number;
            rank: number;
            totalUsers: number;
            refLink: string;
        }>(`${apiUrl}/user/info`, {
            webAppInit,
        })
        .then((res) => res.data);
