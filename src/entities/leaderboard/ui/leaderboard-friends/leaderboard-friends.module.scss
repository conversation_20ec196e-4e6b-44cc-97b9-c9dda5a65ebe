.container {
    position: relative;
}

.shadow {
    position: absolute;
    top: 0;

    width: 100%;
    height: 55px;

    mask-image: linear-gradient(180deg, #0d0d0d 0%, rgba(13, 13, 13, 0) 100%);
    background: var(--color-bg-page);
}

.animation {
    position: absolute;
    top: 0;

    width: 100%;

    z-index: -1;

    & .rect {
        animation: leaderboard-animation 5s ease-in-out infinite;

        animation-delay: var(--delay);
    }
}

@keyframes leaderboard-animation {
    0% {
        opacity: 0.5;
    }
    50% {
        opacity: 0;
    }
    100% {
        opacity: 0.5;
    }
}

.content {
    padding-top: 32px;
    padding-bottom: 44px;

    display: flex;
    flex-direction: column;
    align-items: center;

    gap: 12px;
}

.title {
    color: var(--color-fg-primary);

    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.13px;
}

.value {
    color: var(--color-fg-primary);

    font-size: 48px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -1px;

    display: flex;
    align-items: center;
}

.chain {
    width: 23px;

    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    margin-right: 8px;
    margin-left: 8px;
}

.rank {
    border-radius: 100px;
    background: var(--color-bg-card);

    padding: 6px 10px;

    color: var(--color-fg-primary);

    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: -0.09px;

    & span {
        color: var(--color-fg-tertiary);
    }
}
