/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { useUserInfo } from '@entities/leaderboard';
import { formatNumber } from '@utils';
import { FC } from 'react';

import styles from './leaderboard-friends.module.scss';

export const LeaderboardFriends: FC = () => {
    const { user } = useUserInfo();

    return (
        <div className={styles.container}>
            <svg
                className={styles.animation}
                viewBox="0 0 393 120"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <rect
                    className={styles.rect}
                    style={{ ['--delay' as any]: '0.4s' }}
                    opacity="0.5"
                    x="313"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '5s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="98"
                    y="100"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '2s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="273"
                    y="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '8s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="60"
                    y="40"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '4s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="333"
                    y="80"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '10s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="313"
                    y="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="20"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '6s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="20"
                    y="40"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    y="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="40"
                    y="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '1s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '0s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="40"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="100"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="60"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />

                <rect
                    style={{ ['--delay' as any]: '3s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="140"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="160"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '5s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="78"
                    y="80"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '0s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="40"
                    y="100"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />

                <rect
                    opacity="0.5"
                    x="120"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="373"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="353"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="353"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="333"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />

                <rect
                    opacity="0.5"
                    x="293"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="293"
                    y="40"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />

                <rect
                    opacity="0.5"
                    x="353"
                    y="40"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="373"
                    y="60"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '2s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="373"
                    y="98"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    style={{ ['--delay' as any]: '5s' }}
                    className={styles.rect}
                    opacity="0.5"
                    x="313"
                    y="100"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />

                <rect
                    opacity="0.5"
                    x="273"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="253"
                    y="20"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="233"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
                <rect
                    opacity="0.5"
                    x="273"
                    width="20"
                    height="20"
                    fill="var(--color-bg-card)"
                />
            </svg>
            <div className={styles.shadow}></div>
            <div className={styles.content}>
                <div className={styles.title}>Your frens</div>

                <div className={styles.value}>
                    <div className={styles.chain}>🔗️</div>{' '}
                    {formatNumber(user?.referralsCount ?? 0)}{' '}
                    <div className={styles.chain}></div>
                </div>

                <div className={styles.rank}>
                    Rank {formatNumber(user?.rank ?? 0)}{' '}
                    <span>of {formatNumber(user?.totalUsers ?? 0)}</span>
                </div>
            </div>
        </div>
    );
};
