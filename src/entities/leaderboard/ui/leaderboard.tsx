'use client';
import { LeaderboardTable } from '@entities/leaderboard-table';
import { useModalStore } from '@entities/modal';
import { InviteFriend } from '@features/invite-friend';
import { Title } from '@ui';
import { FC, useCallback, useEffect } from 'react';

import { useUserInfo } from '../hooks';
import styles from './leaderboard.module.scss';
import { LeaderboardFriends } from './leaderboard-friends';

export const Leaderboard: FC = () => {
    const { user } = useUserInfo();
    const showModal = useModalStore((store) => store.showModal);

    const handleLeaderboardEnter = useCallback(async () => {
        if (typeof window !== 'undefined') {
            const isFirstEnter = localStorage.getItem(
                'isFirstEnterLeaderboard',
            );

            if (!isFirstEnter) {
                showModal({
                    modalType: 'earlyAccessBonus',
                    modalState: {},
                });

                localStorage.setItem('isFirstEnterLeaderboard', 'true');
            }
        }
    }, [showModal]);

    useEffect(() => {
        handleLeaderboardEnter();
    }, [handleLeaderboardEnter]);

    return (
        <div className={styles.container}>
            <LeaderboardFriends />

            <div className={styles.invite}>
                <InviteFriend refLink={user?.refLink} />
            </div>

            <div className={styles.table}>
                <div className={styles.tableTitle}>
                    <Title>Leaderboard</Title>
                </div>

                <LeaderboardTable />
            </div>
        </div>
    );
};
