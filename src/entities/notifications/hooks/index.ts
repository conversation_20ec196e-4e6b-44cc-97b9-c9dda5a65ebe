import { useCallback, useMemo } from 'react';

import { useNotificationsStore } from '../model';

export const useNotificationsAdd = () => {
    const add = useNotificationsStore((store) => store.addNotification);

    const addNotification = useCallback(
        (type: 'info' | 'error' | 'success', text: string) => {
            const uuid = crypto.randomUUID();

            add({
                text: text,
                id: uuid,
                type: type,
            });
        },
        [add],
    );

    return useMemo(() => addNotification, [addNotification]);
};
