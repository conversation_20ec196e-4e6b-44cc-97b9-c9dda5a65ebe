'use client';
import { Notification as INotification } from '@types';
import classNames from 'classnames';
import { AnimationDefinition, motion } from 'framer-motion';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';

import { useNotificationsStore } from '../model';
import styles from './notifications.module.scss';

const variants = {
    show: {
        top: 16,
    },
    hide: {
        top: -40,
    },
};

const Notification: FC<INotification> = ({ id, text, type }) => {
    const remove = useNotificationsStore((store) => store.removeNotification);
    const [animate, setAnimate] = useState('hide');

    useEffect(() => {
        setAnimate('show');

        const timer = setTimeout(() => {
            setAnimate('hide');
        }, 4000);

        return () => clearTimeout(timer);
    }, []);

    const onAnimationComplete = useCallback(
        (definition: AnimationDefinition) => {
            if (definition === 'hide') {
                remove(id);
            }
        },
        [remove, id],
    );

    return (
        <motion.div
            initial="hide"
            animate={animate}
            variants={variants}
            onAnimationComplete={onAnimationComplete}
            className={classNames(styles.notification, styles[type])}
        >
            {text}
        </motion.div>
    );
};

export const Notificaitons: FC = () => {
    const { notifications } = useNotificationsStore();

    const notification = useMemo(() => {
        if (!notifications.length) {
            return null;
        }

        return notifications[0];
    }, [notifications]);

    return (
        <div className={styles.container}>
            {notification ? (
                <Notification key={notification.id} {...notification} />
            ) : null}
        </div>
    );
};
