.container {
    position: fixed;

    top: 0;
    left: 0;

    width: 100vw;

    z-index: var(--notification-z-index);

    display: flex;
    justify-content: center;
    align-items: center;
}

.notification {
    position: absolute;
    padding: 8px 16px;
    border-radius: 20px;
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.07);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.09px;
}

.info {
    border: 1px solid var(--color-border-base);
    background: var(--color-bg-card-inverted);
    color: var(--color-fg-primary-inverted);
}

.error {
    border: 1px solid var(--color-border-base);
    background: var(--color-negative);
    color: #ffffff;
}

.success {
    border: 1px solid var(--color-border-base);
    background: var(--color-positive);
    color: #ffffff;
}
