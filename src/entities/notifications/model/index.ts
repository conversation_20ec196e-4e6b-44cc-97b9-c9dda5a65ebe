import { Notification } from '@types';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

interface INotificationsState {
    notifications: Notification[];
    addNotification: (payload: Notification) => void;
    removeNotification: (payload: string) => void;
}

export const useNotificationsStore = create<INotificationsState>()(
    devtools(
        immer((set) => ({
            notifications: [],
            addNotification: (payload) =>
                set((state) => {
                    state.notifications.push(payload);
                }),
            removeNotification: (payload) =>
                set((state) => {
                    state.notifications = state.notifications.filter(
                        (notification) => notification.id !== payload,
                    );
                }),
        })),
    ),
);
