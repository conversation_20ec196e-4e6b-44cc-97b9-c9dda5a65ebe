import { Advertisement } from './ui/advertisement';
import { ChooseTaskType } from './ui/choose-task-type';
import { Emoji } from './ui/emoji';
import { FrensOnboarding } from './ui/frens-onboarding';
import { LaunchAppLoader } from './ui/launch-app-loader';
import { TasksOnboarding } from './ui/tasks-onboarding';

export const contentFromModalType: Record<string, React.ReactNode | undefined> =
    {
        advertisementContent: <Advertisement />,
        emojiPicker: <Emoji />,
        chooseTaskType: <ChooseTaskType />,
        launchAppLoader: <LaunchAppLoader />,
        frensOnboarding: <FrensOnboarding />,
        tasksOnboarding: <TasksOnboarding />,
    };
