import { App } from '@types';
import { AppCard, Slider, SliderMandatory } from '@ui';
import { FC, useMemo } from 'react';

import styles from './apps-banner-slider.module.scss';

interface IAppsBannerSliderProps {
    items: App[];
}

export const AppsBannerSlider: FC<IAppsBannerSliderProps> = ({ items }) => {
    const sliderItems = useMemo(
        () =>
            items.map((item) => (
                <div key={item.id} className={styles.item}>
                    <AppCard.Banner {...item} />
                </div>
            )),
        [items],
    );

    if (!items[0]) {
        return null;
    }

    return (
        <div className={styles.container}>
            {/* <div key={items[0].id} className={styles.item}> */}
            <AppCard.Banner {...items[0]} />
            {/* </div> */}
            {/* <SliderMandatory dots={true} items={sliderItems} /> */}
        </div>
    );
};
