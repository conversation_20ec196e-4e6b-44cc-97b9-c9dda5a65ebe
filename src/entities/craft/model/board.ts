/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable indent */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

import { BoardElement } from '../types';

interface BoardState {
    board: Record<string, BoardElement>;
    positions: Record<string, { left: number; top: number }>;
}

export const useBoardStore = create<BoardState>()(
    immer((set) => ({
        board: {},
        positions: {},
    })),
);
