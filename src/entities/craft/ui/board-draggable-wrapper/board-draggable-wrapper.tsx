import { FC, PropsWithChildren, useEffect } from 'react';
import { DragPreviewImage, useDrag, useDragLayer, XYCoord } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';

import { useBoardStore } from '../../model/index';

interface IBoardDraggableItem extends PropsWithChildren {
    id: string;
    name: string;
    icon: string;
    databaseId: string;
}

export const BoardDraggableItem: FC<IBoardDraggableItem> = ({
    children,
    id,
    name,
    icon,
    databaseId,
}) => {
    const positions = useBoardStore((store) => store.positions);

    const [{ isDragging }, dragRef, preview] = useDrag({
        type: 'element',
        item: {
            elementId: id,
            elementName: name,
            icon,
            databaseId,
            left: positions[id].left,
            top: positions[id].top,
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    useEffect(() => {
        preview(getEmptyImage(), { captureDraggingState: true });
    }, []);

    // if (isDragging) {
    //     return null;
    // }

    return (
        <div
            ref={dragRef}
            style={{
                position: 'absolute',
                // transform: `translate(${positions[id].left}px, ${positions[id].top}px)`,
                // WebkitTransform: `translate(${positions[id].left}px, ${positions[id].top}px)`,
                left: positions[id].left,
                top: positions[id].top,
                zIndex: 120,
                opacity: isDragging ? 0 : 1,
                height: isDragging ? 0 : 'auto',
            }}
        >
            {children}
        </div>
    );
};
