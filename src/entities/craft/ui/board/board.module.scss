.container {
    position: relative;

    display: flex;
    justify-content: center;

    width: 100%;
    height: 300px;

    border-radius: 0px 0px 24px 24px;
    background: var(--color-app-accent);

    overflow: hidden;
}

.points {
    z-index: 1;

    position: absolute;

    top: 20px;

    display: flex;
    align-items: center;

    gap: 8px;
}

.clear {
    z-index: 1;

    cursor: pointer;

    position: absolute;

    right: 16px;
    bottom: 16px;

    width: 40px;
    height: 40px;

    display: flex;
    justify-content: center;
    align-items: center;

    background: linear-gradient(
            0deg,
            rgba(255, 255, 255, 0.24) 0%,
            rgba(255, 255, 255, 0.24) 100%
        ),
        var(--color-app-accent);

    border-radius: 50%;
}

.background {
    pointer-events: none;

    width: 100%;
    height: 100%;

    position: absolute;
    top: 0;
    left: 0;
}

.loader {
    position: absolute;

    display: flex;
    justify-content: center;
    align-items: center;

    left: 16px;
    bottom: 16px;
}
