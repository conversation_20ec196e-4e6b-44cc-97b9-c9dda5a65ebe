import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

import { Element } from '../types';

export const useElements = (userId: string) =>
    useQuery({
        queryKey: ['elements', userId],
        queryFn: () =>
            axios
                .get<Element[]>('/api/craft/elements', { params: { userId } })
                .then((res) => res.data),
    });

export const useCraft = (userId: string, appId: number | string) => {
    const queryClient = useQueryClient();

    const craft = async (element1Id: string, element2Id: string) => {
        const response = await axios
            .post('/api/craft', { element1Id, element2Id, userId, appId })
            .then((res) => res.data);

        queryClient.invalidateQueries({
            queryKey: ['elements'],
        });

        queryClient.invalidateQueries({
            queryKey: ['app-statistic', String(appId)],
        });

        return response;
    };

    return { craft };
};
