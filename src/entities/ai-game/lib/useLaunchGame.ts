/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-restricted-syntax */
'use client';

import * as amplitude from '@amplitude/analytics-browser';
import { useCoreStore } from '@entities/core';
import { useModalStore } from '@entities/modal';
import { useNotificationsAdd } from '@entities/notifications';
import { sleep } from '@utils';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';

import { DEFAULT_SOUND, ERROR_MESSAGES, GAME_TYPES } from '../constants';
import { baseGameUrl } from '../constants';
import { gameApi } from '../services/api';
import { LaunchAppParams, LaunchAppPayload, LaunchGameError } from '../types';
import { useProgressBar } from './useProgressBar';

interface ProgressStep {
    progress: number;
    text?: string;
    delay: number;
}

const PROGRESS_STEPS: ProgressStep[] = [
    { progress: 10, delay: 1000 },
    { progress: 35, text: 'Devs drinking latte', delay: 1500 },
    { progress: 60, delay: 1500 },
    { progress: 85, text: 'Cleaning stuff', delay: 1500 },
    { progress: 100, delay: 100 },
];

export const useLaunchGame = ({
    gameId,
    version,
}: {
    gameId: string;
    version: string;
}) => {
    const router = useRouter();
    const showModal = useModalStore((state) => state.showModal);
    const closeModal = useModalStore((state) => state.closeModal);
    const addNotification = useNotificationsAdd();
    const { userId } = useCoreStore();
    const { simulateProgress } = useProgressBar();

    const createLaunchAppPayload = useCallback(
        (params: LaunchAppParams): LaunchAppPayload => ({
            gameName: params.projectName,
            description: params.description,
            avatar: params.iconUrl,
            tapImageUrl: params.iconUrl,
            color: params.color,
            vibration: params.vibration,
            type: GAME_TYPES.OTHER,
            gameFile: `${baseGameUrl}/${gameId}/v${version}.html`,
            sound: params.sounds ? DEFAULT_SOUND : undefined,
            gameIdFromAiBuilder: gameId,
        }),
        [gameId, version],
    );

    const handleError = useCallback(
        (error: LaunchGameError) => {
            amplitude.logEvent('Creation_Builder_Error', {
                userId,
                timestamp: moment().unix(),
            });

            simulateProgress(() => {
                closeModal();
                addNotification(
                    'error',
                    ERROR_MESSAGES[error] ||
                        'Something went wrong. Please, try again later!',
                );
            });
        },
        [userId, closeModal, addNotification, simulateProgress],
    );

    const launchApp = useCallback(
        async (params: LaunchAppParams) => {
            try {
                showModal({
                    modalType: 'launchAppLoader',
                    modalState: {
                        text: 'Creating app',
                        progress: 0,
                    },
                });

                const existedApp = await gameApi.fetchAppWithAiGameId(gameId);

                if (existedApp) {
                    const payload = {
                        gameFile: `${baseGameUrl}/${gameId}/v${version}.html`,
                    };

                    const updatedApp = await gameApi.updateApp(
                        existedApp.id,
                        payload,
                    );

                    if (!updatedApp?.id) {
                        throw new Error(LaunchGameError.LAUNCH_APP);
                    }

                    amplitude.logEvent('Update_Builder_Success', {
                        userId,
                        timestamp: moment().unix(),
                        appId: updatedApp?.id,
                    });

                    router.prefetch(`/app/${updatedApp.id}`);
                    await simulateProgress(() => {
                        router.push(`/app/${updatedApp.id}`);
                    });
                } else {
                    const payload = createLaunchAppPayload(params);
                    const app = await gameApi.launchApp(payload);

                    amplitude.logEvent('Creation_Builder_Success', {
                        userId,
                        timestamp: moment().unix(),
                        appId: app?.id,
                    });

                    if (app?.id) {
                        router.prefetch(`/app/${app.id}`);
                        await simulateProgress(() => {
                            router.push(`/app/${app.id}`);
                        });
                    } else {
                        handleError(LaunchGameError.LAUNCH_APP);
                    }
                }
            } catch (error) {
                handleError(LaunchGameError.LAUNCH_APP);
            }
        },
        [
            gameId,
            version,
            userId,
            router,
            showModal,
            createLaunchAppPayload,
            handleError,
            simulateProgress,
        ],
    );

    return useMemo(() => ({ launchApp }), [launchApp]);
};
