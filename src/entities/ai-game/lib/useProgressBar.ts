import { useModalStore } from '@entities/modal';
import { sleep } from '@utils';
import { useCallback } from 'react';

import { ProgressStep } from '../types';

const PROGRESS_STEPS: ProgressStep[] = [
    { progress: 10, delay: 1000 },
    { progress: 35, text: 'Devs drinking latte', delay: 1500 },
    { progress: 60, delay: 1500 },
    { progress: 85, text: 'Cleaning stuff', delay: 1500 },
    { progress: 100, delay: 100 },
];

export const useProgressBar = () => {
    const updateModalState = useModalStore((state) => state.updateModalState);

    const simulateProgress = useCallback(
        async (callback: () => void) => {
            for (const step of PROGRESS_STEPS) {
                updateModalState({
                    progress: step.progress,
                    ...(step.text && { text: step.text }),
                });
                await sleep(step.delay);
            }
            callback();
        },
        [updateModalState],
    );

    return { simulateProgress };
};
