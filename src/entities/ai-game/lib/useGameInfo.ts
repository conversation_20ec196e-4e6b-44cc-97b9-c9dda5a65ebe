import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { fetchGameInfoWithIcon } from '../api';

export const useGameInfo = (gameId: string, enabled = true) => {
    const { data, isLoading } = useQuery({
        queryKey: ['game-info', gameId],
        queryFn: async () => fetchGameInfoWithIcon(gameId),
        retry: true,
        retryDelay: (attemptIndex) =>
            Math.min(1000 * 2 ** attemptIndex, 10_000),
        enabled,
    });

    return useMemo(() => ({ info: data, isLoading }), [data, isLoading]);
};
