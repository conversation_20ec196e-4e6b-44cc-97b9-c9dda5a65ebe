/* eslint-disable @typescript-eslint/no-explicit-any */
import { axios } from '@constants';
import { App } from '@types';

export const fetchGameCode = async (payload: string) =>
    await axios
        .get(payload, {
            headers: {
                'Content-Type': 'text/html',
            },
        })
        .then((res) => res.data);

export const fetchGameInfoWithIcon = async (payload: string) => {
    const info = await axios
        .get<{
            accentColor: string;
            description: string;
            iconUrl: string;
            projectName: string;
            tokenName: string;
            tokenSymbol: string;
        }>(
            `${process.env.NEXT_PUBLIC_BUILDER_BACKEND_URL}/games/${payload}/info`,
        )
        .then((res) => res.data);

    if (!info.iconUrl) {
        return {
            ...info,
            image: null,
            iconUrl:
                'https://soda-s3-aws-bucket.s3.us-east-1.amazonaws.com/default_icon.png',
        };
    }

    const imageBlob = await axios
        .get(info.iconUrl, {
            responseType: 'blob',
        })
        .then((res) => res.data);

    const image = new File([imageBlob], 'image.webp', {
        type: imageBlob.type,
    });

    Object.assign(image, {
        width: 100,
        height: 100,
        aspectRatio: 1,
        preview: URL.createObjectURL(imageBlob),
    });

    return { ...info, image };
};

const backendApiUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

export const fetchLaunchApp = async (data: any) =>
    await axios
        .post<App>(`${backendApiUrl}/game/create`, data, {
            headers: {
                withAuth: true,
            },
        })
        .then((res) => res.data)
        .catch(() => ({
            id: process.env.NEXT_PUBLIC_APP_ID as string,
        }));

export const fetchUploadTokenImage = async (image: File) => {
    const body = new FormData();

    body.append('image', image as any);

    return await axios
        .post<{ url: string }>(`${backendApiUrl}/helper/image/upload`, body, {
            headers: {
                'Content-Type': 'multipart/form-data',
                withAuth: true,
            },
        })
        .then((res) => res.data.url);
};
