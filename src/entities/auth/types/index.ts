export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
}

export interface TelegramAuthResponse extends AuthTokens {
    accessToken: string;
    refreshToken: string;
}

export interface UserResponse {
    id: string;
    // другие поля пользователя
}

export interface CoreState extends AuthTokens {
    userId: string | undefined;
    isTma: boolean;
    // другие поля состояния
}

export type AuthState = Pick<
    CoreState,
    'accessToken' | 'refreshToken' | 'userId'
>;
