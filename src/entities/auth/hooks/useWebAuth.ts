/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { fetchUser } from '@api';
import { useCoreStore } from '@entities/core';
import { useClientOnce } from '@hooks';
import { useMutation } from '@tanstack/react-query';
import { useCallback, useEffect } from 'react';

import { AuthState, UserResponse } from '../types';
import { isTokenExpired } from '../utils/token';

export const useWebAuth = () => {
    const { accessToken, refreshToken, userId } = useCoreStore();

    const { mutateAsync: fetchUserData } = useMutation<UserResponse, Error>({
        mutationKey: ['user'],
        mutationFn: fetchUser,
    });

    const webAuth = useCallback(async () => {
        const tokens = {
            accessToken: accessToken,
            refreshToken: refreshToken,
        };

        if (tokens.accessToken && tokens.refreshToken) {
            if (isTokenExpired(tokens.accessToken)) {
                useCoreStore.setState({
                    accessToken: undefined,
                    refreshToken: undefined,
                });
            }
        }

        const userData = await fetchUserData();

        const newState: Partial<AuthState> = {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            userId: userData.id,
        };

        useCoreStore.setState(newState);
    }, [accessToken, refreshToken, fetchUserData]);

    useClientOnce(() => {
        webAuth();
    });

    useEffect(() => {
        if (accessToken && refreshToken && userId) {
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('refreshToken', refreshToken);
            localStorage.setItem('userId', userId);
        }
    }, [accessToken, refreshToken, userId]);

    return null;
};
