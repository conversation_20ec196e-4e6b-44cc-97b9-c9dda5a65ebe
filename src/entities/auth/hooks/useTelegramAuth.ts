/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { fetchLinkSolanaWallet, fetchTelegramAuth, fetchUser } from '@api';
import { useCoreStore } from '@entities/core';
import { useSelectedWallet } from '@entities/wallet/hooks';
import { useMutation } from '@tanstack/react-query';
import { cloudStorage, initData, useSignal } from '@telegram-apps/sdk-react';
import { useCallback, useContext, useEffect, useRef } from 'react';

import { AuthState, TelegramAuthResponse, UserResponse } from '../types';
import {
    getTokensFromStorage,
    isTokenExpired,
    saveTokensToStorage,
} from '../utils/token';

export const useTelegramAuth = () => {
    const walletConnection = useSelectedWallet();
    const wallet = walletConnection?.wallet;
    const isTma = useCoreStore((store) => store.isTma);
    const authState = useCoreStore((store) => ({
        accessToken: store.accessToken,
        refreshToken: store.refreshToken,
        userId: store.userId,
    }));
    const initDataRaw = useSignal(initData.raw);
    const isCloudStorageSupported = useSignal(cloudStorage.isSupported);
    const isAuthPending = useRef(false);

    const { mutateAsync: authenticateWithTelegram } = useMutation<
        TelegramAuthResponse,
        Error,
        string
    >({
        mutationKey: ['telegramAuth'],
        mutationFn: fetchTelegramAuth,
    });

    const { mutateAsync: fetchUserData } = useMutation<UserResponse, Error>({
        mutationKey: ['user'],
        mutationFn: fetchUser,
    });

    // Основная логика аутентификации
    const handleTelegramAuth = useCallback(async () => {
        if (isAuthPending.current || !isCloudStorageSupported || !initDataRaw) {
            return;
        }

        isAuthPending.current = true;

        try {
            let tokens = await getTokensFromStorage(cloudStorage);
            console.log('tokens', tokens);
            // Проверяем токены и обновляем при необходимости
            if (tokens.accessToken && tokens.refreshToken) {
                if (isTokenExpired(tokens.refreshToken)) {
                    tokens = await authenticateWithTelegram(initDataRaw);
                }
            } else {
                tokens = await authenticateWithTelegram(initDataRaw);
            }

            useCoreStore.setState({
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
            });

            // Получаем данные пользователя
            const userData = await fetchUserData();

            // Обновляем состояние
            const newState: Partial<AuthState> = {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
                userId: userData.id,
            };

            useCoreStore.setState(newState);
        } catch (error) {
            console.error('handleTelegramAuth failed:', error);
            // Здесь можно добавить обработку ошибок, например, показ уведомления
        } finally {
            isAuthPending.current = false;
        }
    }, [
        isAuthPending,
        initDataRaw,
        isCloudStorageSupported,
        authenticateWithTelegram,
        fetchUserData,
    ]);

    // Инициализация аутентификации
    useEffect(() => {
        if (isTma) {
            void handleTelegramAuth();
        }
    }, [handleTelegramAuth, isTma]);

    // Сохранение токенов в storage
    useEffect(() => {
        if (
            isTma &&
            authState.accessToken &&
            authState.refreshToken &&
            authState.userId &&
            cloudStorage.isSupported()
        ) {
            void saveTokensToStorage(
                {
                    accessToken: authState.accessToken,
                    refreshToken: authState.refreshToken,
                },
                cloudStorage,
            );
            void cloudStorage.setItem('userId', authState.userId);
        }
    }, [isTma, authState]);

    return null;
};
