import { cloudStorage } from '@telegram-apps/sdk-react';
import { jwtDecode } from 'jwt-decode';
import moment from 'moment';

import { AuthTokens } from '../types';

export const isTokenExpired = (token: string): boolean => {
    try {
        const { exp } = jwtDecode(token);

        return exp ? exp < moment().unix() : true;
    } catch {
        return true;
    }
};

export const saveTokensToStorage = async (
    tokens: AuthTokens,
    storage: typeof cloudStorage,
): Promise<void> => {
    await Promise.all([
        storage.setItem('accessToken', tokens.accessToken),
        storage.setItem('refreshToken', tokens.refreshToken),
    ]);
};

export const getTokensFromStorage = async (
    storage: typeof cloudStorage,
): Promise<Partial<AuthTokens>> => {
    type StorageData =
        | { accessToken?: string; refreshToken?: string }
        | string
        | null;

    const [accessToken, refreshToken] = await Promise.all([
        storage
            .getItem('accessToken')
            .then((data: StorageData) =>
                typeof data === 'object' ? data?.accessToken : data,
            )
            .catch(() => undefined),
        storage
            .getItem('refreshToken')
            .then((data: StorageData) =>
                typeof data === 'object' ? data?.refreshToken : data,
            )
            .catch(() => undefined),
    ]);

    return {
        accessToken: accessToken || undefined,
        refreshToken: refreshToken || undefined,
    };
};
