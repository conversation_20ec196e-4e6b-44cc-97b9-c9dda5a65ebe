.deposit {
    border-radius: 22px;
    border: 1px solid var(--color-border-base);
    background: rgba(244, 150, 28, 0.12);

    &Balance {
        display: flex;
        align-items: center;

        gap: 4px;

        &.red {
            color: var(--color-negative);
        }
    }

    &Data {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &Item {
        display: flex;
        flex-direction: column;

        gap: 4px;
    }

    &Header {
        padding: 12px 16px;

        display: flex;
        align-items: center;
        justify-content: center;

        gap: 8px;

        color: #f4961c;
    }

    &Content {
        width: 100%;

        padding: 16px;

        border-radius: 22px;
        border: 1px solid var(--color-border-base);
        background: var(--color-bg-elevated);
    }
}
