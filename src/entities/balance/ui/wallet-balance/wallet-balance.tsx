'use client';
import { FC, useMemo } from 'react';
import classNames from 'classnames';

import { Icons } from '@assets';
import { useBalance } from '@entities/balance/hooks';
import { useSelectedWallet } from '@entities/wallet/hooks';
import { Typography } from '@ui';
import { shortAddress } from '@utils';

import styles from './wallet-balance.module.scss';

export const WalletBalance: FC = () => {
    const walletConnection = useSelectedWallet();
    const wallet = walletConnection?.wallet;
    const { balance } = useBalance(wallet?.address);
    const address = useMemo(() => wallet?.address, [wallet]);

    return (
        <div className={styles.depositContent}>
            <div className={styles.depositData}>
                <div className={styles.depositItem}>
                    <Typography variant="tertiary" size="m">
                        Your wallet
                    </Typography>

                    <Typography variant="primary" size="l">
                        {shortAddress(address)}
                    </Typography>
                </div>
                <div className={styles.depositItem}>
                    <Typography variant="tertiary" size="m">
                        Balance
                    </Typography>

                    <Typography
                        className={classNames(styles.depositBalance, {
                            [styles.red]: balance < 0.01,
                        })}
                        variant="primary"
                        size="l"
                        weight="600"
                    >
                        <Icons.Eth />
                        {Intl.NumberFormat('en-US', {
                            style: 'decimal',
                        }).format(balance)}
                    </Typography>
                </div>
            </div>
        </div>
    );
};
