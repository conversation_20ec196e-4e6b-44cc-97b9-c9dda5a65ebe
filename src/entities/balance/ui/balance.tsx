'use client';
import { Icons } from '@assets';
import { useBottomSheetStore } from '@entities/bottom-sheet';
import { Deposit } from '@features/deposit';
import { Typography } from '@ui';
import { motion } from 'framer-motion';
import { FC, useCallback } from 'react';

import { useBalance } from '../hooks';
import styles from './balance.module.scss';

export const Balance: FC = () => {
    const { balance } = useBalance();
    const show = useBottomSheetStore((store) => store.show);

    const handleShowDeposit = useCallback(() => {
        show({
            title: 'Deposit SOL',
            description: 'Send any amount on your SOL wallet',
            customContent: <Deposit />,
        });
    }, [show]);

    return (
        <div className={styles.container}>
            <div className={styles.balance}>
                <Icons.Solana width={20} height={20} />

                <Typography size="l" variant="primary">
                    {balance.toFixed(2)}
                </Typography>
            </div>

            <motion.div
                onClick={handleShowDeposit}
                whileTap={{ scale: 0.95 }}
                className={styles.deposit}
            >
                <Icons.PlusCircle
                    color="var(--color-accent)"
                    width={16}
                    height={16}
                />
            </motion.div>
        </div>
    );
};
