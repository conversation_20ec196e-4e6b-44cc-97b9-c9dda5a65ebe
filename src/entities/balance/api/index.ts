import { createPublicClient, formatEther, http, define<PERSON>hain } from 'viem';
import { mainnet, sepolia } from 'viem/chains';
import { CHAIN_IDS } from '@shared/constants/ethereum';

// Define local development chain (Anvil)
const anvilChain = defineChain({
    id: CHAIN_IDS.ANVIL_LOCAL, // 31337
    name: 'Anvil Local',
    nativeCurrency: {
        decimals: 18,
        name: 'Ether',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: [process.env.NEXT_PUBLIC_RPC_URL || 'http://localhost:8545'],
        },
    },
    blockExplorers: {
        default: { name: 'Explorer', url: '' },
    },
});

// Define Sophon testnet chain
const sophonTestnet = defineChain({
    id: CHAIN_IDS.SOPHON_TESTNET, // 531050104
    name: 'Sophon Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON>ther',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.testnet.sophon.xyz'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Sophon Explorer',
            url: 'https://explorer.testnet.sophon.xyz',
        },
    },
});

// Determine which chain and transport to use
const getChainConfig = () => {
    const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;
    const rpcUrl = process.env.NEXT_PUBLIC_RPC_URL;

    // Use chain ID if explicitly set
    if (chainId) {
        const id = parseInt(chainId, 10);
        switch (id) {
            case CHAIN_IDS.ANVIL_LOCAL:
                return {
                    chain: anvilChain,
                    transport: http(rpcUrl || 'http://localhost:8545'),
                };
            case CHAIN_IDS.SOPHON_TESTNET:
                return {
                    chain: sophonTestnet,
                    transport: http('https://rpc.testnet.sophon.xyz'),
                };
            case CHAIN_IDS.SEPOLIA:
                return {
                    chain: sepolia,
                    transport: http(),
                };
            case CHAIN_IDS.MAINNET:
                return {
                    chain: mainnet,
                    transport: http(),
                };
        }
    }

    // Fallback: If RPC_URL is set to localhost, use anvil chain
    if (rpcUrl && rpcUrl.includes('localhost')) {
        return {
            chain: anvilChain,
            transport: http(rpcUrl),
        };
    }

    // Default: use mainnet/sepolia based on NODE_ENV
    const chain = process.env.NODE_ENV === 'production' ? mainnet : sepolia;
    return {
        chain,
        transport: http(),
    };
};

const { chain, transport } = getChainConfig();

const publicClient = createPublicClient({
    chain,
    transport,
});

export const fetchBalance = async (address: string) => {
    if (!address) {
        return 0;
    }

    try {
        const balance = await publicClient.getBalance({
            address: address as `0x${string}`,
        });

        return parseFloat(formatEther(balance));
    } catch (error) {
        console.error('Error fetching balance:', error);
        return 0;
    }
};
