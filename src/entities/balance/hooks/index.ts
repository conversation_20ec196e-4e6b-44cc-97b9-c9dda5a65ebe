'use client';
import { useQuery } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

import { fetchBalance as fetchBalanceRequest } from '../api';

export const useBalance = (address?: string) => {
    const fetchBalance = useCallback(async () => {
        if (!address) {
            return { balance: 0 };
        }

        const balance = await fetchBalanceRequest(address);

        return {
            balance: balance,
        };
    }, [address]);

    const { data } = useQuery({
        queryKey: ['balance', address],
        queryFn: fetchBalance,
        refetchInterval: 5000,
    });

    return useMemo(() => ({ balance: data?.balance ?? 0 }), [data]);
};
