import { App } from '@types';
import { AppCard } from '@ui';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FC, useCallback, useMemo } from 'react';
import { SliderMandatory } from 'src/shared/ui/slider-mandatory';

import styles from './my-games.module.scss';

interface IAppsBannerSliderProps {
    items: App[];
}

export const MyGamesSlider: FC<IAppsBannerSliderProps> = ({ items }) => {
    const router = useRouter();

    const seeMoreHandler = useCallback(() => {
        router.push('/my-apps');
    }, [router]);

    const sliderItems = useMemo(() => {
        const visibleItems = items.slice(0, 5).map((item) => (
            <motion.div
                whileTap={{ scale: 0.95 }}
                key={item.id}
                className={styles.item}
            >
                <AppCard.BannerSmall {...item} />
            </motion.div>
        ));

        if (items.length > 5) {
            visibleItems.push(
                <motion.div
                    whileTap={{ scale: 0.95 }}
                    key="more"
                    className={styles.item}
                >
                    <AppCard.More
                        count={items.length - 5}
                        description="See all my Games & Tokens"
                        onClick={seeMoreHandler}
                    />
                </motion.div>,
            );
        }

        return visibleItems;
    }, [items, seeMoreHandler]);

    return (
        <div className={styles.container}>
            <div className={styles.wrapper}>
                <SliderMandatory items={sliderItems} />
            </div>
        </div>
    );
};
