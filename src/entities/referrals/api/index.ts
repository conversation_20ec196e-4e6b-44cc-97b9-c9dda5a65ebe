import { useCoreStore } from '@entities/core';
import { Referral } from '@types';
import axios from 'axios';

const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

export const fetchUserReferrals = async (
    appId: string | number,
    userId: string,
) =>
    await axios
        .get<{
            list: Referral[];
            count: number;
        }>(`${apiUrl}/referral_account/${userId}/${appId}/list_referrals`, {
            params: {
                take: 100,
                skip: 0,
            },
        })
        .then((res) => res.data);
