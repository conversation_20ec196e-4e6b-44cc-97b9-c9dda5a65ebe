/* eslint-disable max-lines-per-function */
'use client';
import { Icons } from '@assets';
import { Referral } from '@types';
import classNames from 'classnames';
import Image from 'next/image';
import { FC, useMemo, useState } from 'react';

import styles from './referrals-table.module.scss';

interface IReferralsTableItem extends Referral {
    index: number;
}

const ReferralsTableItem: FC<IReferralsTableItem> = ({
    user,
    index,
    inviteXp,
}) => {
    const [imageError, setImageError] = useState(false);

    return (
        <div className={classNames(styles.item)}>
            <div className={styles.itemLeft}>
                <div className={classNames(styles.itemPosition)}>{index}</div>
                <div className={styles.itemUser}>
                    <div className={styles.itemAvatar}>
                        {imageError ? (
                            <Icons.Avatar />
                        ) : (
                            <Image
                                src={`https://t.me/i/userpic/160/${user.nickname}.jpg`}
                                width={28}
                                height={28}
                                alt={user.nickname ?? 'avatar'}
                                onError={() => {
                                    setImageError(true);
                                }}
                            />
                        )}
                    </div>
                    <div className={styles.itemNickname}>
                        {user.nickname ? user.nickname : 'noname'}
                    </div>
                </div>
            </div>

            <div className={styles.itemRight}>
                <div className={styles.itemPoints}>
                    {Intl.NumberFormat('en-US', {
                        notation: 'compact',
                        compactDisplay: 'short',
                    }).format(inviteXp)}
                    &nbsp;
                    <svg
                        style={{ marginLeft: 6 }}
                        width="18"
                        height="18"
                        viewBox="0 0 18 18"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <g filter="url(#filter0_i_9214_50670)">
                            <path
                                d="M2.33814 14.7918C-0.90979 11.5439 -0.75491 6.12307 2.68408 2.68408C6.12307 -0.75491 11.5439 -0.90979 14.7918 2.33814C18.0398 5.58608 17.8849 11.0069 14.4459 14.4459C11.0069 17.8849 5.58608 18.0398 2.33814 14.7918Z"
                                fill="#FFF728"
                            />
                            <path
                                d="M2.33814 14.7918C-0.90979 11.5439 -0.75491 6.12307 2.68408 2.68408C6.12307 -0.75491 11.5439 -0.90979 14.7918 2.33814C18.0398 5.58608 17.8849 11.0069 14.4459 14.4459C11.0069 17.8849 5.58608 18.0398 2.33814 14.7918Z"
                                fill="url(#paint0_radial_9214_50670)"
                            />
                        </g>
                        <g filter="url(#filter1_i_9214_50670)">
                            <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M2.33814 14.7918L3.20817 15.6619C6.45611 18.9098 11.8769 18.7549 15.3159 15.3159C18.7549 11.8769 18.9098 6.45611 15.6619 3.20817L14.7918 2.33814C18.0398 5.58608 17.8849 11.0069 14.4459 14.4459C11.0069 17.8849 5.58608 18.0398 2.33814 14.7918Z"
                                fill="#EE6C00"
                            />
                        </g>
                        <path
                            d="M2.42226 14.7077C-0.774753 11.5107 -0.628932 6.16533 2.7682 2.7682C6.16533 -0.628932 11.5107 -0.774753 14.7077 2.42226C17.9047 5.61927 17.7589 10.9646 14.3618 14.3618C10.9646 17.7589 5.61927 17.9047 2.42226 14.7077Z"
                            stroke="url(#paint1_radial_9214_50670)"
                            strokeOpacity="0.72"
                            strokeWidth="0.237916"
                        />
                        <g filter="url(#filter2_ii_9214_50670)">
                            <path
                                d="M4.03781 3.55905C1.38777 6.20908 1.13712 10.5975 3.68923 13.1496C6.24134 15.7017 10.6298 15.4511 13.2798 12.8011C15.9299 10.151 16.1805 5.76258 13.6284 3.21047C11.0763 0.65836 6.68784 0.909006 4.03781 3.55905Z"
                                fill="#FFB700"
                            />
                        </g>
                        <path
                            d="M3.85994 3.67207C1.17038 6.36163 0.90615 10.8257 3.51136 13.4309C6.11658 16.0361 10.5806 15.7719 13.2702 13.0823C15.9597 10.3928 16.224 5.92871 13.6188 3.3235C11.0135 0.718284 6.5495 0.982515 3.85994 3.67207Z"
                            stroke="url(#paint2_radial_9214_50670)"
                            strokeOpacity="0.48"
                            strokeWidth="0.237916"
                        />
                        <g filter="url(#filter3_i_9214_50670)">
                            <path
                                d="M9.26578 4.94577C9.49783 4.62354 10.0085 4.71166 10.114 5.09213L10.6021 6.85339C10.6436 7.00322 10.7587 7.12294 10.909 7.17264L12.6442 7.7466C13.0113 7.86802 13.0841 8.34496 12.7693 8.56606L11.2126 9.65936C11.0898 9.74562 11.0161 9.88386 11.0142 10.0318L10.9898 11.926C10.9848 12.3146 10.5243 12.5266 10.2152 12.2826L8.81397 11.1761C8.68674 11.0756 8.51572 11.0461 8.36104 11.098L6.65762 11.6687C6.28191 11.7946 5.92324 11.4412 6.05434 11.0744L6.69342 9.28622C6.74333 9.14657 6.72246 8.99201 6.63719 8.86985L5.55645 7.32144C5.33789 7.00831 5.57298 6.5845 5.96037 6.59326L7.79158 6.63472C7.95016 6.63831 8.10014 6.56432 8.19152 6.43744L9.26578 4.94577Z"
                                fill="#EE6400"
                            />
                        </g>
                        <path
                            d="M8.78921 4.48007C9.02127 4.15784 9.53195 4.24596 9.6374 4.62643L10.1255 6.3877C10.1671 6.53752 10.2821 6.65724 10.4324 6.70694L12.1677 7.28091C12.5348 7.40233 12.6076 7.87926 12.2927 8.10036L10.736 9.19366C10.6132 9.27992 10.5396 9.41816 10.5377 9.5661L10.5132 11.4603C10.5082 11.8489 10.0477 12.0609 9.73864 11.8169L8.33741 10.7104C8.21018 10.6099 8.03915 10.5804 7.88448 10.6323L6.18106 11.203C5.80535 11.3289 5.44668 10.9755 5.57778 10.6087L6.21686 8.82052C6.26677 8.68087 6.24589 8.52632 6.16062 8.40415L5.07988 6.85574C4.86133 6.54261 5.09642 6.1188 5.48381 6.12757L7.31501 6.16902C7.4736 6.17261 7.62358 6.09863 7.71496 5.97174L8.78921 4.48007Z"
                            fill="#F9D131"
                        />
                        <path
                            d="M8.78921 4.48007C9.02127 4.15784 9.53195 4.24596 9.6374 4.62643L10.1255 6.3877C10.1671 6.53752 10.2821 6.65724 10.4324 6.70694L12.1677 7.28091C12.5348 7.40233 12.6076 7.87926 12.2927 8.10036L10.736 9.19366C10.6132 9.27992 10.5396 9.41816 10.5377 9.5661L10.5132 11.4603C10.5082 11.8489 10.0477 12.0609 9.73864 11.8169L8.33741 10.7104C8.21018 10.6099 8.03915 10.5804 7.88448 10.6323L6.18106 11.203C5.80535 11.3289 5.44668 10.9755 5.57778 10.6087L6.21686 8.82052C6.26677 8.68087 6.24589 8.52632 6.16062 8.40415L5.07988 6.85574C4.86133 6.54261 5.09642 6.1188 5.48381 6.12757L7.31501 6.16902C7.4736 6.17261 7.62358 6.09863 7.71496 5.97174L8.78921 4.48007Z"
                            fill="url(#paint3_radial_9214_50670)"
                        />
                        <path
                            d="M9.52276 4.6582L10.0109 6.41947C10.0633 6.60837 10.2079 6.75798 10.3951 6.81988L12.1303 7.39385C12.4063 7.48512 12.4577 7.83914 12.2244 8.00301L10.6677 9.09631C10.5141 9.20415 10.4211 9.37777 10.4187 9.56457L10.3943 11.4588C10.3906 11.7439 10.0478 11.9094 9.81236 11.7235L8.41113 10.617C8.25215 10.4915 8.03914 10.455 7.84669 10.5195L6.14327 11.0902C5.85566 11.1865 5.59424 10.9161 5.6898 10.6487L6.32888 8.86055C6.39182 8.68445 6.36536 8.48963 6.25817 8.33606L5.17743 6.78765C5.0175 6.55852 5.18603 6.23981 5.48112 6.24649L7.31232 6.28794C7.50942 6.29241 7.69681 6.20051 7.81149 6.04126L8.88574 4.54959C9.06075 4.30658 9.44457 4.37606 9.52276 4.6582Z"
                            stroke="url(#paint4_radial_9214_50670)"
                            strokeOpacity="0.48"
                            strokeWidth="0.237916"
                        />
                        <defs>
                            <filter
                                id="filter0_i_9214_50670"
                                x="-0.475833"
                                y="-0.475833"
                                width="17.6047"
                                height="17.6058"
                                filterUnits="userSpaceOnUse"
                                colorInterpolationFilters="sRGB"
                            >
                                <feFlood
                                    floodOpacity="0"
                                    result="BackgroundImageFix"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="BackgroundImageFix"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dx="-0.475833" dy="-0.475833" />
                                <feGaussianBlur stdDeviation="1.90333" />
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.48 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect1_innerShadow_9214_50670"
                                />
                            </filter>
                            <filter
                                id="filter1_i_9214_50670"
                                x="-0.237916"
                                y="-0.237916"
                                width="18.2379"
                                height="18.2379"
                                filterUnits="userSpaceOnUse"
                                colorInterpolationFilters="sRGB"
                            >
                                <feFlood
                                    floodOpacity="0"
                                    result="BackgroundImageFix"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="BackgroundImageFix"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dx="-0.237916" dy="-0.237916" />
                                <feGaussianBlur stdDeviation="0.713749" />
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 0.988235 0 0 0 0 0.811765 0 0 0 0 0 0 0 0 0.64 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect1_innerShadow_9214_50670"
                                />
                            </filter>
                            <filter
                                id="filter2_ii_9214_50670"
                                x="1.90625"
                                y="1.42749"
                                width="15.4072"
                                height="15.4085"
                                filterUnits="userSpaceOnUse"
                                colorInterpolationFilters="sRGB"
                            >
                                <feFlood
                                    floodOpacity="0"
                                    result="BackgroundImageFix"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="BackgroundImageFix"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dx="1.90333" dy="1.90333" />
                                <feGaussianBlur stdDeviation="3.80666" />
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.64 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect1_innerShadow_9214_50670"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dx="0.475833" dy="0.475833" />
                                <feGaussianBlur stdDeviation="0.951665" />
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 0.9 0 0 0 0 0.225 0 0 0 0 0 0 0 0 0.64 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="effect1_innerShadow_9214_50670"
                                    result="effect2_innerShadow_9214_50670"
                                />
                            </filter>
                            <filter
                                id="filter3_i_9214_50670"
                                x="5.23474"
                                y="4.51025"
                                width="7.73401"
                                height="7.87641"
                                filterUnits="userSpaceOnUse"
                                colorInterpolationFilters="sRGB"
                            >
                                <feFlood
                                    floodOpacity="0"
                                    result="BackgroundImageFix"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="BackgroundImageFix"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dx="-0.237916" dy="-0.237916" />
                                <feGaussianBlur stdDeviation="0.713749" />
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 0.996078 0 0 0 0 0.690196 0 0 0 0 0 0 0 0 1 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect1_innerShadow_9214_50670"
                                />
                            </filter>
                            <radialGradient
                                id="paint0_radial_9214_50670"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(0.197096 0.197096) rotate(45) scale(23.216)"
                            >
                                <stop offset="0.06" stopColor="#FBFFA0" />
                                <stop
                                    offset="0.835"
                                    stopColor="#FBFFA0"
                                    stopOpacity="0"
                                />
                            </radialGradient>
                            <radialGradient
                                id="paint1_radial_9214_50670"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(0.197096 0.197098) rotate(45) scale(24.2254)"
                            >
                                <stop stopColor="white" />
                                <stop
                                    offset="0.886255"
                                    stopColor="white"
                                    stopOpacity="0"
                                />
                            </radialGradient>
                            <radialGradient
                                id="paint2_radial_9214_50670"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(15.4238 15.2359) rotate(-135) scale(19.5149)"
                            >
                                <stop stopColor="white" />
                                <stop
                                    offset="1"
                                    stopColor="white"
                                    stopOpacity="0"
                                />
                            </radialGradient>
                            <radialGradient
                                id="paint3_radial_9214_50670"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(8.05724 0.841169) rotate(82.9223) scale(15.1678 13.7763)"
                            >
                                <stop offset="0.06" stopColor="#FBFFA0" />
                                <stop
                                    offset="0.835"
                                    stopColor="#FBFFA0"
                                    stopOpacity="0"
                                />
                            </radialGradient>
                            <radialGradient
                                id="paint4_radial_9214_50670"
                                cx="0"
                                cy="0"
                                r="1"
                                gradientUnits="userSpaceOnUse"
                                gradientTransform="translate(8.89428 13.0446) rotate(-100.83) scale(9.95693 9.41181)"
                            >
                                <stop stopColor="white" />
                                <stop
                                    offset="1"
                                    stopColor="white"
                                    stopOpacity="0"
                                />
                            </radialGradient>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>
    );
};

export const ReferralsTable: FC<{ referrals: Referral[] }> = ({
    referrals,
}) => {
    const content = useMemo(
        () => (
            <div className={styles.table}>
                {referrals?.map((referral, index) => (
                    <ReferralsTableItem
                        key={referral.user.id}
                        index={index + 1}
                        {...referral}
                    />
                ))}
            </div>
        ),
        [referrals],
    );

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <div className={styles.headerLeft}>
                    <div className={styles.headerIndex}>#</div>

                    <div className={styles.headerItem}>Fren</div>
                </div>

                <div className={styles.headerRight}>
                    <div className={styles.headerItem}>Earned</div>
                </div>
            </div>

            {content}
        </div>
    );
};
