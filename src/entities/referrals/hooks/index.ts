import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { fetchUserReferrals } from '../api';

export const useUserReferrals = (appId: string | number, userId?: string) => {
    const { data, isLoading } = useQuery({
        queryKey: ['userReferrals', appId, userId],
        queryFn: () => {
            if (userId) {
                return fetchUserReferrals(appId, userId);
            }

            return null;
        },
    });

    return useMemo(
        () => ({ referrals: data?.list, total: data?.count, isLoading }),
        [data, isLoading],
    );
};
