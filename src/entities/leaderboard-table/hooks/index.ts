import { useUserInfo } from '@entities/leaderboard/hooks';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { fetchLeaderboardTable } from '../api';

export const useLeaderboard = () => {
    const { data, isLoading } = useQuery({
        queryKey: ['leaderboard'],
        queryFn: async () => await fetchLeaderboardTable(),
    });
    const { user } = useUserInfo();

    const leaders = useMemo(() => {
        let isUserExist = false;

        const result = data?.users.slice(0, 3).map((value) => {
            if (value.telegramId === user?.telegramId) {
                isUserExist = true;

                return {
                    ...value,
                    isUser: true,
                    rank: user?.rank,
                };
            }

            return value;
        });

        return result;
    }, [data, user]);

    const leaderboard = useMemo(() => {
        let isUserExist = false;

        const result = data?.users.map((value) => {
            if (value.telegramId === user?.telegramId) {
                isUserExist = true;

                return {
                    ...value,
                    isUser: true,
                    rank: user?.rank,
                };
            }

            return value;
        });

        if (!isUserExist) {
            result?.push({
                id: 0,
                isUser: true,
                ...user,
            });
        }

        return result;
    }, [data, user]);

    return useMemo(
        () => ({
            leaderboard,
            leaders,
            isLoading,
        }),
        [data, isLoading, leaders],
    );
};
