.container {
    display: flex;
    flex-direction: column;

    gap: 10px;

    margin-bottom: 20px;
}

.table {
    border-radius: 20px;
    background-color: var(--color-bg-card);
    border: 1px solid var(--color-border-base);

    overflow: hidden;

    &.top {
        box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.12);
    }

    &:first-child {
        transform: scale(1.04);
    }
}

.skeleton {
    width: 100%;
    height: 200px;
    border-radius: 20px;

    background: var(--color-fg-skeleton);
}

.item {
    padding: 10px 16px;

    display: flex;
    align-items: center;
    justify-content: space-between;

    &:not(:last-child) {
        border-bottom: 1px solid var(--color-border-divider);
    }

    &.me {
        background-color: var(--color-bg-card-timer);

        & .itemNickname {
            font-weight: 500;
        }
    }

    &.absolute {
        position: fixed;
        width: calc(100% - 32px);

        border-radius: 20px;
        border: 1px solid var(--color-border-base);
        background-color: var(--color-bg-elevated);

        overflow: hidden;

        bottom: 20px;

        & .itemPosition {
            font-weight: 600;
        }
    }

    &Left {
        display: flex;
        align-items: center;
    }

    &Position {
        margin-right: 18px;

        color: var(--color-fg-primary);

        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: -0.13px;

        min-width: 20px;
    }

    &Medal {
        font-size: 20px;
    }

    &User {
        display: flex;
        align-items: center;
    }

    &Avatar {
        display: flex;
        margin-right: 10px;

        & svg,
        img {
            width: 28px;
            height: 28px;

            border-radius: 50%;
        }
    }

    &Nickname {
        overflow: hidden;
        color: var(--color-informative);
        text-overflow: ellipsis;

        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: -0.13px;
    }

    &Score {
        color: var(--color-fg-primary);
        text-align: right;

        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: -0.13px;
    }
}
