/* eslint-disable indent */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Icons } from '@assets';
import classNames from 'classnames';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { FC, useEffect, useMemo, useState } from 'react';
import { useInView } from 'react-intersection-observer';

import { useLeaderboard } from '../hooks';
import { ILeaderboardTableItem } from '../types';
import styles from './leaderboard-table.module.scss';

const medalFromPosition: any = {
    '1': '🥇',
    '2': '🥈',
    '3': '🥉',
};

interface ILeaderboardItem extends ILeaderboardTableItem {
    position: number;
    isUser?: boolean;
    isInView?: boolean;
    isLeader?: boolean;
}

const LeaderboardItem: FC<ILeaderboardItem> = ({
    id,
    nickname,
    referralsCount,
    position,
    isUser,
    isInView,
    rank,
    isLeader,
}) => {
    const isMedal = Boolean(medalFromPosition[position]);
    const [imageError, setImageError] = useState(false);

    return (
        <motion.div
            variants={{
                show: {
                    scale: 1.04,
                },
                hidden: {
                    scale: 1,
                },
            }}
            initial={!isInView && isUser ? 'show' : 'hidden'}
            animate={!isInView && isUser ? 'show' : 'hidden'}
            className={classNames(styles.item, {
                [styles.me]: isUser && isInView,
                [styles.absolute]: isUser && !isInView,
            })}
        >
            <div className={styles.itemLeft}>
                <div
                    className={classNames(styles.itemPosition, {
                        [styles.itemMedal]: isMedal,
                    })}
                >
                    {isLeader
                        ? (medalFromPosition[position] ?? position)
                        : isUser
                          ? rank
                          : position}
                </div>
                <div className={styles.itemUser}>
                    <div className={styles.itemAvatar}>
                        {imageError ? (
                            <Icons.Avatar />
                        ) : (
                            <Image
                                src={`https://t.me/i/userpic/160/${nickname}.jpg`}
                                width={28}
                                height={28}
                                alt={nickname ?? 'avatar'}
                                onError={() => {
                                    setImageError(true);
                                }}
                            />
                        )}
                    </div>
                    <div className={styles.itemNickname}>
                        {nickname ? nickname : 'noname'}
                    </div>
                </div>
            </div>

            <div className={styles.itemScore}>{referralsCount}&nbsp;🔗</div>
        </motion.div>
    );
};

export const LeaderboardTable: FC = () => {
    const { leaderboard, leaders, isLoading } = useLeaderboard();
    const { ref, inView } = useInView();

    const leadersContent = useMemo(() => {
        if (!leaders || !leaders.length) {
            return null;
        }

        return (
            <div className={classNames(styles.table, styles.top)}>
                {leaders?.map((item, index) => (
                    <>
                        <LeaderboardItem
                            key={`top-${item.id}`}
                            position={index + 1}
                            isUser={item.isUser}
                            isInView={true}
                            isLeader={true}
                            {...item}
                        />
                    </>
                ))}
            </div>
        );
    }, [leaderboard, inView]);

    const leaderboardContent = useMemo(() => {
        if (!leaderboard || !leaderboard.length || leaderboard.length <= 3) {
            return null;
        }

        return (
            <div className={styles.table}>
                {leaderboard?.map((item, index) => {
                    if (index < 3) {
                        return null;
                    }

                    return (
                        <>
                            {item.isUser ? (
                                <div key={'ref'} ref={ref}></div>
                            ) : null}
                            <LeaderboardItem
                                key={item.id}
                                position={index + 1}
                                isUser={item.isUser}
                                isInView={inView}
                                {...item}
                            />
                        </>
                    );
                })}
            </div>
        );
    }, [leaderboard, inView]);

    if (isLoading) {
        return <div className={styles.skeleton}></div>;
    }

    return (
        <div className={styles.container}>
            {leadersContent}
            {leaderboardContent}
        </div>
    );
};
