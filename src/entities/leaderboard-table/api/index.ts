import axios from 'axios';

import { ILeaderboardTableItem } from '../types';

const apiUrl = process.env.NEXT_PUBLIC_WL_BACKEND_URL;

export const fetchLeaderboardTable = async () =>
    await axios
        .get<{
            users: ILeaderboardTableItem[];
            total: number;
        }>(`${apiUrl}/leaderboard`, {
            params: {
                take: 100,
                skip: 0,
            },
        })
        .then((res) => res.data);
