.card {
    scroll-snap-align: start;
    position: relative;

    height: 100%;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;

    background: linear-gradient(
            180deg,
            var(--color-app-accent) 17.5%,
            rgba(143, 30, 255, 0) 100%
        ),
        var(--color-bg-page);

    padding-top: 10px;

    display: flex;
    flex-direction: column;

    @media screen and (min-width: 1024px) {
        border-radius: 32px;

        // border: 1px solid var(--color-border-base);

        overflow: hidden;
    }

    &App {
        z-index: 1;

        width: 100%;

        padding: 16px;
    }
}

.game {
    flex: 1;

    margin-top: 36px;
}

.upgrade {
    width: 100%;

    margin-top: 36px;

    display: flex;
    justify-content: center;

    position: relative;

    z-index: 1;
}

.container {
    position: relative;
    overflow: hidden;

    height: 100%;

    @media screen and (min-width: 1024px) {
        height: 90%;
        overflow: visible;
    }
}

.main {
    width: 100%;
    height: 100%;

    display: flex;
    align-items: center;

    gap: 24px;
}

.customGameHeader {
    width: 100%;

    padding: 0px 16px;

    display: flex;

    height: 48px;

    & .headerLeft {
        display: flex;
        align-items: center;

        gap: 6px;
    }
}

.content {
    width: 100%;
    height: 100%;

    @media screen and (min-width: 1024px) {
        width: calc(100% - 64px);

        position: relative;
    }

    &Game {
        width: 100%;
        height: 100%;

        padding: 8px 16px;
        padding-top: 32px;

        display: flex;
        flex-direction: column;
        align-items: center;

        @media screen and (min-width: 768px) {
            border-radius: 32px;
        }

        @media screen and (max-width: 768px) {
            height: 100%;
        }
    }

    &Other {
        width: 100%;
        height: 100%;

        // background: linear-gradient(
        //     180deg,
        //     var(--color-app-accent) 17.5%,
        //     rgba(166, 77, 255, 0) 87.03%
        // );

        display: flex;
        flex-direction: column;
        align-items: center;

        @media screen and (max-width: 768px) {
            height: 100%;
        }
    }

    &Tasks {
        width: 100vw;
        height: 100vh;

        padding: 8px 16px;
    }
}

.controls {
    width: 64px;

    display: flex;
    flex-direction: column;

    gap: 16px;

    @media screen and (max-width: 1024px) {
        display: none;
    }

    &Button {
        cursor: pointer;

        padding: 12px 20px;

        border-radius: 9999px;

        background: rgba(215, 215, 234, 0.08);

        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.slide {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.slide h2 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.navTouch {
    @media screen and (min-width: 1024px) {
        display: none;
    }
}
