'use client';
import * as amplitude from '@amplitude/analytics-browser';
import { Icons } from '@assets';
import { AiGame } from '@entities/ai-game';
import { useCoreStore } from '@entities/core';
import { Craft } from '@entities/craft';
import { useAppStatistic, usePlayGame } from '@entities/earn/hooks';
import { usePlayCustomGame } from '@entities/earn/hooks/usePlayCustomGame';
import { FarmStatistic } from '@entities/farm-statistic';
import { Game } from '@entities/game';
import { useApps, usePopularApps } from '@entities/home/<USER>';
import { useTelegramRouterBack, useThrottle } from '@hooks';
import NumberFlow from '@number-flow/react';
import { App } from '@types';
import { AppCard, Title, UpgradeButton } from '@ui';
import { getContrastRatio } from '@utils';
import { NavTouch } from '@widgets/nav';
import { motion } from 'framer-motion';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import {
    CSSProperties,
    FC,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';

import { useQuickyOnboardingSheet } from '../hooks';
import styles from './reels.module.scss';

const ReelsItem: FC<App & { userId?: string }> = ({
    tapImageUrl,
    color,
    id,
    token,
    avatar,
    vibration,
    usersCount,
    userId,
    name,
    type,
    gameFile,
    sound,
}) => {
    const router = useRouter();
    const { statistic } = useAppStatistic(id);
    const { points, onClick } = usePlayGame(id);
    const { points: pointsCustom, addPoints } = usePlayCustomGame(id);

    useEffect(() => {
        amplitude.logEvent('View_Reels_Item', {
            userId: userId,
            appId: id,
            timestamp: moment().unix(),
        });
    }, []);

    const upgradeClickHandler = useCallback(() => {
        router.push(`/app/${id}/upgrades`);
    }, [router, id]);

    const appAccentColor = useMemo(
        () => color ?? 'var(--color-accent)',
        [color],
    );
    const appTextColor = useMemo(() => {
        if (!color) {
            return '#fff';
        }

        const ratio = getContrastRatio(color, '#fff');

        return ratio >= 4.5 ? '#fff' : '#000';
    }, [color]);

    const content = useMemo(() => {
        if (type === 'INFINITY') {
            return (
                <div className={styles.contentOther}>
                    <Craft appId={id} points={points ?? 0} />
                </div>
            );
        }

        if (type === 'OTHER') {
            return (
                <div className={styles.contentOther}>
                    <div className={styles.customGameHeader}>
                        <div className={styles.headerLeft}>
                            <Icons.XP width={18} height={18} />

                            <Title size="s" variant="primary">
                                <NumberFlow
                                    value={
                                        isNaN(Number(pointsCustom))
                                            ? 0
                                            : Number(pointsCustom)
                                    }
                                />
                            </Title>
                        </div>
                    </div>

                    <AiGame
                        addPoints={addPoints}
                        appId={id}
                        codeUrl={gameFile}
                    />
                </div>
            );
        }

        return (
            <div className={styles.contentGame}>
                {/* <Switch
                    className={styles.switch}
                    items={[
                        {
                            label: 'Farm',
                            value: 'farm',
                        },
                        {
                            label: 'Tasks',
                            value: 'tasks',
                        },
                    ]}
                    value={selectedTab}
                    onChange={tabChangeHandler}
                    view="game"
                    size="m"
                /> */}
                <div className={styles.statistic}>
                    {statistic && (
                        <FarmStatistic points={points} statistic={statistic} />
                    )}
                </div>

                <div className={styles.game} style={{ pointerEvents: 'none' }}>
                    <Game
                        onClick={onClick}
                        vibration={vibration}
                        sound={sound}
                        image={tapImageUrl}
                    />
                </div>

                <div className={styles.upgrades}>
                    <UpgradeButton onClick={upgradeClickHandler}>
                        Upgrades
                    </UpgradeButton>
                </div>
            </div>
        );
    }, [
        tapImageUrl,
        statistic,
        points,
        onClick,
        upgradeClickHandler,
        id,
        vibration,
        sound,
        type,
        gameFile,
        addPoints,
        pointsCustom,
    ]);

    return (
        <div
            className={styles.card}
            style={
                {
                    '--color-app-accent': appAccentColor,
                    '--color-app-accent-text': appTextColor,
                } as CSSProperties
            }
        >
            {content}

            <div className={styles.cardApp}>
                <AppCard.Big
                    id={id}
                    name={name}
                    imageSrc={avatar}
                    color={color}
                    usersCount={usersCount}
                    marketCapUsd={token?.marketCapUsd}
                    collectedSol={token?.collectedSol}
                    isButton={false}
                />
            </div>
        </div>
    );
};

export const Reels: FC = () => {
    useQuickyOnboardingSheet();
    useTelegramRouterBack('/');
    const { apps } = useApps();
    const { apps: popularApps } = usePopularApps();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isScrolling, setIsScrolling] = useState(false);
    const touchStartY = useRef<number | null>(null);
    const { userId } = useCoreStore();
    const slides = useMemo(
        () => [
            ...popularApps,
            ...apps.filter(
                (app) =>
                    !popularApps.some((popularApp) => popularApp.id === app.id),
            ),
        ],
        [popularApps, apps],
    );

    const handleTouchStart = useCallback(
        (e: React.TouchEvent<HTMLDivElement>) => {
            if (isScrolling) {
                return;
            }

            touchStartY.current = e.touches[0].clientY;
        },
        [isScrolling],
    );

    const goToNextSlide = useCallback(() => {
        setCurrentIndex((prev) => {
            if (prev < slides.length - 1) {
                return prev + 1;
            }

            return prev;
        });
    }, [slides.length]);

    const goToPreviousSlide = useCallback(() => {
        setCurrentIndex((prev) => {
            if (prev > 0) {
                return prev - 1;
            }

            return prev;
        });
    }, []);

    const handleTouchMove = useCallback(
        (e: React.TouchEvent<HTMLDivElement>) => {
            if (!touchStartY.current || isScrolling) {
                return;
            }

            const touchEndY = e.touches[0].clientY;
            const deltaY = touchStartY.current - touchEndY;

            if (Math.abs(deltaY) > 50) {
                setIsScrolling(true);
                if (deltaY > 0) {
                    goToNextSlide();
                } else {
                    goToPreviousSlide();
                }
                touchStartY.current = null;

                setTimeout(() => {
                    setIsScrolling(false);
                }, 500);
            }
        },
        [isScrolling, goToNextSlide, goToPreviousSlide],
    );

    const handleKeyDown = useCallback(
        (e: KeyboardEvent) => {
            if (e.key === 'ArrowDown') {
                goToNextSlide();
            }

            if (e.key === 'ArrowUp') {
                goToPreviousSlide();
            }
        },
        [goToNextSlide, goToPreviousSlide],
    );

    const throttledHandleKeyDown = useThrottle(handleKeyDown, 500);

    useEffect(() => {
        window.addEventListener('keydown', throttledHandleKeyDown);

        return () => {
            window.removeEventListener('keydown', throttledHandleKeyDown);
        };
    }, [throttledHandleKeyDown]);

    const handleWheel = useCallback(
        (e: React.WheelEvent<HTMLDivElement>) => {
            amplitude.logEvent('Scroll_Reels_Item', {
                userId: userId,
                timestamp: moment().unix(),
            });

            e.preventDefault();

            if (e.deltaY > 0) {
                goToNextSlide();
            } else {
                goToPreviousSlide();
            }
        },
        [goToNextSlide, goToPreviousSlide, userId],
    );

    const throttledHandleWheel = useThrottle(handleWheel, 1200);

    const content = useMemo(
        () =>
            slides
                .filter((_, index) => Math.abs(index - currentIndex) <= 1)
                .map((slide) => {
                    const slideIndex = slides.findIndex(
                        (s) => s.id === slide.id,
                    );

                    if (slideIndex - currentIndex === -1) {
                        return (
                            <div
                                key={slide.id}
                                className={styles.slide}
                                style={{
                                    transform: `scale(${0}) translateY(${-400}px)`,
                                    transition:
                                        'transform 500ms ease-in-out, opacity 700ms ease-in-out',
                                    zIndex: `${0}`,
                                    opacity: `${0}`,
                                }}
                            >
                                <ReelsItem {...slide} userId={userId} />
                            </div>
                        );
                    }

                    return (
                        <div
                            key={slide.id}
                            className={styles.slide}
                            style={{
                                transform: `scale(${1 - (slideIndex - currentIndex) * 0.2}) translateY(${(slideIndex - currentIndex) * 20}%)`,
                                transition:
                                    'transform 500ms ease-in-out, opacity 700ms ease-in-out',
                                zIndex: `${100 - Math.abs(slideIndex - currentIndex)}`,
                                opacity: `${1 - (slideIndex - currentIndex) * 0.5}`,
                            }}
                        >
                            <ReelsItem {...slide} userId={userId} />
                        </div>
                    );
                }),
        [slides, currentIndex, userId],
    );

    if (slides.length === 0) {
        return <div className="loading">Loading...</div>;
    }

    return (
        <div
            className={styles.container}
            onWheel={throttledHandleWheel}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
        >
            <div className={styles.main}>
                <div className={styles.content}>{content}</div>
                <div className={styles.controls}>
                    <motion.div
                        animate={{ opacity: currentIndex === 0 ? 0.5 : 1 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={goToPreviousSlide}
                        className={styles.controlsButton}
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M11.2622 4.30562C11.6698 3.89813 12.3303 3.89813 12.7379 4.30562L18.3031 9.87088C18.7106 10.2784 18.7106 10.939 18.3031 11.3465C17.8956 11.754 17.235 11.754 16.8275 11.3465L13.0435 7.56266V18.9565C13.0435 19.5328 12.5763 20 12.0001 20C11.4238 20 10.9566 19.5328 10.9566 18.9565V7.56266L7.17268 11.3465C6.76518 11.754 6.10448 11.754 5.69698 11.3465C5.28948 10.939 5.28948 10.2784 5.69698 9.87088L11.2622 4.30562Z"
                                fill="#E5E5EA"
                            />
                        </svg>
                    </motion.div>
                    <motion.div
                        animate={{
                            opacity:
                                currentIndex === slides.length - 1 ? 0.5 : 1,
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={goToNextSlide}
                        className={styles.controlsButton}
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M11.2622 19.6944C11.6698 20.1019 12.3303 20.1019 12.7379 19.6944L18.3031 14.1291C18.7106 13.7216 18.7106 13.061 18.3031 12.6535C17.8956 12.246 17.235 12.246 16.8275 12.6535L13.0435 16.4373V5.04348C13.0435 4.4672 12.5763 4 12.0001 4C11.4238 4 10.9566 4.4672 10.9566 5.04348V16.4373L7.17268 12.6535C6.76518 12.246 6.10448 12.246 5.69698 12.6535C5.28948 13.061 5.28948 13.7216 5.69698 14.1291L11.2622 19.6944Z"
                                fill="#E5E5EA"
                            />
                        </svg>
                    </motion.div>
                </div>
            </div>

            <div className={styles.navTouch}>
                <NavTouch view="reels" />
            </div>
        </div>
    );
};
