import { useTheme } from '@hooks';
import { Theme } from '@types';
import classNames from 'classnames';
import Image from 'next/image';

import styles from './onboarding-banner-card.module.scss';

export const OnboardingBannerCard = () => {
    const { theme } = useTheme();

    return (
        <div
            className={classNames(styles.card, {
                [styles.cardDark]: theme === Theme.DARK,
            })}
        >
            <Image
                src={
                    theme === Theme.DARK
                        ? '/assets/images/onboarding-dark.png'
                        : '/assets/images/onboarding-light.png'
                }
                width={221}
                height={190}
                className={styles.banner}
                alt="onboarding-image"
            />
        </div>
    );
};
