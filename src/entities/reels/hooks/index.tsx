import { useBottomSheetStore } from '@entities/bottom-sheet';
import { Button } from '@ui';
import { useCallback, useEffect } from 'react';

import { QUICKY_SHEET_STORAGE_KEY } from '../constants';
import { OnboardingBannerCard } from '../ui/onboarding-banner-card';

export const useQuickyOnboardingSheet = () => {
    const show = useBottomSheetStore((store) => store.show);
    const close = useBottomSheetStore((store) => store.close);

    const checkOnboardingState = useCallback(async () => {
        if (typeof window !== 'undefined') {
            const isOnboarded = localStorage.getItem(QUICKY_SHEET_STORAGE_KEY);

            if (isOnboarded !== 'true') {
                show({
                    customHeader: <OnboardingBannerCard />,
                    title: 'Welcome to Quicky',
                    description:
                        'Invite frens, complete tasks and participate in funds to get more XP',
                    actions: [
                        <Button
                            size="ml"
                            view="action"
                            onClick={() => {
                                localStorage.setItem(
                                    QUICKY_SHEET_STORAGE_KEY,
                                    'true',
                                );
                                close();
                            }}
                            key={'onboarding-button'}
                        >
                            Okay
                        </Button>,
                    ],
                    isShowCloseIcon: false,
                });
            }
        }
    }, []);

    useEffect(() => {
        void checkOnboardingState();
    }, [checkOnboardingState]);
};
