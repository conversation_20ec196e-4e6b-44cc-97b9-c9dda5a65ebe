import './globals.css';

import { inter } from '@constants';
import { BottomSheet } from '@entities/bottom-sheet';
import { Modal } from '@entities/modal';
import { Notificaitons } from '@entities/notifications';
import { Providers } from '@providers';
import { PageLayout } from '@shared/ui/page-layout';
import type { Metadata, Viewport } from 'next';

export const metadata: Metadata = {
    title: {
        template: 'Soda | %s',
        absolute: 'Soda App Builder - Create Your Own Apps',
        default: 'Soda App Builder',
    },
    description:
        'Build and deploy your own applications with Soda App Builder. Create AI-powered games, manage your apps, and earn rewards.',
    keywords: 'app builder, ai games, web3, blockchain, telegram mini apps',
    openGraph: {
        title: {
            template: 'Soda | %s',
            absolute: 'Soda App Builder - Create Your Own Apps',
            default: 'Soda App Builder',
        },
        type: 'website',
        locale: 'en_US',
        description:
            'Build and deploy your own applications with Soda App Builder. Create AI-powered games, manage your apps, and earn rewards.',
    },
    twitter: {
        card: 'summary_large_image',
        title: {
            template: 'Soda | %s',
            absolute: 'Soda App Builder - Create Your Own Apps',
            default: 'Soda App Builder',
        },
        description:
            'Build and deploy your own applications with Soda App Builder. Create AI-powered games, manage your apps, and earn rewards.',
    },
    robots: 'index, follow',
    manifest: '/manifest.json',
    icons: {
        icon: '/favicon.png',
    },
};

export const viewport: Viewport = {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    minimumScale: 1,
    userScalable: false,
    themeColor: '#131314',
};

/**
 * Root layout component that wraps the entire application
 * Provides global providers, modals, notifications and base layout structure
 *
 * @component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @returns {JSX.Element} Root layout structure
 */
export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body className={inter.className}>
                <div id="root">
                    <Providers>
                        <Modal />
                        <BottomSheet />
                        <Notificaitons />
                        <PageLayout>{children}</PageLayout>
                    </Providers>
                </div>
            </body>
        </html>
    );
}
