'use client';

import * as Sentry from '@sentry/nextjs';
import { Button, Typography } from '@shared/ui';
import { useEffect } from 'react';

import styles from './global-error.module.scss';

interface GlobalErrorProps {
    error: Error & { digest?: string };
    reset: () => void;
}

/**
 * Компонент глобальной обработки ошибок
 * Отображает пользовательский интерфейс ошибки с возможностью повторной попытки
 *
 * @component
 * @param {Object} props - Свойства компонента
 * @param {Error} props.error - Объект ошибки
 * @param {Function} props.reset - Функция для повторной попытки
 * @returns {JSX.Element} Компонент ошибки
 */
export default function GlobalError({ error, reset }: GlobalErrorProps) {
    useEffect(() => {
        // Отправляем ошибку в Sentry
        Sentry.captureException(error);
    }, [error]);

    return (
        <html lang="en">
            <body>
                <div className={styles.container}>
                    <div className={styles.content}>
                        <div className={styles.title}>Что-то пошло не так</div>

                        <Typography
                            size="m"
                            variant="secondary"
                            className={styles.description}
                        >
                            Произошла непредвиденная ошибка. Наша команда уже
                            уведомлена и работает над исправлением.
                        </Typography>

                        {error.message && (
                            <div className={styles.errorDetails}>
                                <Typography size="sm" variant="secondary">
                                    Детали ошибки:
                                </Typography>
                                <code className={styles.errorCode}>
                                    {error.message}
                                </code>
                            </div>
                        )}

                        <div className={styles.actions}>
                            <Button
                                onClick={() => reset()}
                                view="action"
                                size="m"
                            >
                                Попробовать снова
                            </Button>
                            <Button
                                onClick={() => (window.location.href = '/')}
                                view="outline"
                                size="m"
                            >
                                Вернуться на главную
                            </Button>
                        </div>
                    </div>
                </div>
            </body>
        </html>
    );
}
