.background {
    position: fixed;

    z-index: -1;

    left: 0;
    top: 0;

    width: 100vw;
    height: 100vh;

    background: url('/assets/images/create-background.svg');
    background-color: var(--color-bg-page);

    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.header {
    width: 100%;

    @media screen and (min-width: 1200px) {
        max-width: 1200px;
        margin: 0 auto;
    }

    @media screen and (min-width: 1024px) {
        margin-bottom: 40px;
    }
}

.container {
    height: calc(100% - 72px);

    @media screen and (min-width: 1024px) {
        height: calc(100% - 72px - 40px);

        margin: 0 auto;
    }
}
