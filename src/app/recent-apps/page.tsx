import { RecentAppsList } from '@entities/recent-apps';
import { Header } from '@widgets/header';
import { NavDesktop } from '@widgets/nav';
import { FC } from 'react';

import styles from './page.module.scss';

const Page: FC = () => (
    <>
        <div className={styles.header}>
            <Header />
        </div>

        <div className={styles.container}>
            <div className={styles.left}>{/* <NavDesktop /> */}</div>
            <div className={styles.content}>
                <RecentAppsList />
            </div>

            <div className={styles.right}></div>
        </div>
    </>
);

export default Page;
