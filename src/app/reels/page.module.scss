.header {
    width: 100%;

    display: none;

    @media screen and (min-width: 1024px) {
        display: block;

        margin-bottom: 40px;
    }
}

.container {
    width: 100%;
    height: calc(100% - var(--nav-height));

    display: flex;
    justify-content: space-between;

    gap: 24px;

    @media screen and (min-width: 1200px) {
        height: calc(100% - var(--nav-height) - 40px);
        max-width: 1200px;
        margin: 0 auto;
        overflow: hidden;
    }

    @media screen and (min-width: 1024px) and (max-width: 1200px) {
        padding: 0 16px;
    }
}

.content {
    width: 100%;
}

.left {
    @media screen and (max-width: 1024px) {
        display: none;
    }
}

.right {
    @media screen and (min-width: 1200px) {
        width: 150px;
    }
}
