:root {
    --color-positive: #15c468;
    --color-positive-hover: #1ec16a;
    --color-positive-active: #14c259;
    --color-negative: #ff453a;
    --color-informative: #0a84ff;
    --color-builder-brand: #a64dff;

    --color-bg-page: #131314;
    --color-bg-secondary: rgba(215, 215, 234, 0.08);
    --color-bg-page-inverted: #ffffff;
    --color-bg-card: #212124;
    --color-bg-card-inverted: #f1f1f2;
    --color-bg-card-secondary: rgba(215, 215, 234, 0.08);
    --color-bg-elevated: #212124;
    --color-bg-scrim: rgba(19, 19, 20, 0.8);

    --color-bg-card-game: rgba(0, 0, 0, 0.2);
    --color-bg-card-tab: rgba(215, 215, 234, 0.06);
    --color-bg-scrim: rgba(19, 19, 20, 0.8);
    --color-bg-elevated: #212124;

    --color-fg-primary: #e5e5ea;
    --color-fg-secondary: #c0c0cc;
    --color-fg-placeholder: rgba(77, 77, 82, 1);
    --color-fg-tertiary: #909099;
    --color-fg-primary-inverted: #131314;
    --color-fg-tertiary-inverted: #7b7b85;
    --color-fg-skeleton-mono: rgba(215, 215, 234, 0.08);
    --color-fg-skeleton: linear-gradient(
        90deg,
        rgba(215, 215, 234, 0.08) 8%,
        rgba(215, 215, 234, 0.12) 16%,
        rgba(215, 215, 234, 0.08) 32%
    );

    --color-border-base: rgba(215, 215, 234, 0.06);
    --color-border-banner: rgba(0, 0, 0, 0.16);
    --color-border-divider: rgba(215, 215, 234, 0.04);

    --color-neutral: rgba(215, 215, 234, 0.06);
    --color-neutral-hover: rgba(215, 215, 234, 0.1);
    --color-neutral-active: rgba(215, 215, 234, 0.08);

    --color-accent: rgba(156, 117, 255, 1);
    --color-accent-hover: rgba(156, 117, 255, 0.8);
    --color-accent-active: rgba(156, 117, 255, 0.6);

    --color-text-fade: linear-gradient(
        270deg,
        #131314 0%,
        rgba(19, 19, 20, 0) 100%
    );

    --notification-z-index: 1200;
    --search-container-z-index: 1100;
    --generate-image-z-index: 1100;
    --modal-z-index: 1000;
    --bottom-sheet-z-index: 900;
    --app-nav-z-index: 800;
}

/* :root [data-theme='light'] {
    --color-positive: #15c468;
    --color-positive-hover: #1ec16a;
    --color-positive-active: #14c259;
    --color-negative: #ff453a;
    --color-informative: #0a84ff;
    --color-builder-brand: #a64dff;

    --color-bg-page: #ffffff;
    --color-bg-page-inverted: #131314;
    --color-bg-card-inverted: #212124;
    --color-bg-card-secondary: #fff;
    --color-bg-elevated: #ffffff;
    --color-bg-scrim: rgba(0, 0, 0, 0.4);

    --color-bg-card: #f1f1f2;
    --color-bg-card-game: rgba(0, 0, 0, 0.2);
    --color-bg-card-tab: #ffffff;
    --color-bg-scrim: rgba(0, 0, 0, 0.4);
    --color-bg-elevated: #ffffff;

    --color-fg-primary: #131314;
    --color-fg-secondary: #4d4d52;
    --color-fg-placeholder: rgba(176, 176, 184, 1);
    --color-fg-tertiary: #7b7b85;
    --color-fg-primary-inverted: #e5e5ea;
    --color-fg-tertiary-inverted: #909099;
    --color-fg-skeleton-mono: rgba(24, 24, 26, 0.08);
    --color-fg-skeleton: linear-gradient(
        90deg,
        rgba(24, 24, 26, 0.08) 8%,
        rgba(24, 24, 26, 0.12) 16%,
        rgba(24, 24, 26, 0.08) 32%
    );

    --color-border-base: rgba(24, 24, 26, 0.08);
    --color-border-banner: rgba(0, 0, 0, 0.16);
    --color-border-divider: rgba(24, 24, 26, 0.04);

    --color-neutral: rgba(24, 24, 26, 0.06);
    --color-neutral-hover: rgba(24, 24, 26, 0.1);
    --color-neutral-active: rgba(24, 24, 26, 0.08);

    --color-accent: #0593ff;
    --color-accent-hover: #1a9cff;
    --color-accent-active: #0091ff;

    --color-text-fade: linear-gradient(
        270deg,
        #fff 0%,
        rgba(255, 255, 255, 0) 100%
    );
} */

html,
body {
    max-width: 100vw;
    overflow: hidden;
    scrollbar-width: none;
}

body {
    max-width: 100vw;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scrollbar-width: none;

    background-color: var(--color-bg-page);
    color: var(--color-fg-primary);

    transition:
        background-color 0.5s ease,
        color 0.5s ease;

    &::-webkit-scrollbar {
        display: none;
    }
}

.content {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;

    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

@media screen and (min-width: 1200px) {
    .content {
        /* max-width: 1200px; */
        margin: 0 auto;
    }
}

@media screen and (max-width: 1200px) {
    .content {
        max-width: 100%;
    }
}

button {
    appearance: none;
    outline: none;

    -webkit-tap-highlight-color: transparent;
}

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    user-select: none;
}

a {
    color: inherit;
    text-decoration: none;
}

input,
textarea {
    font: inherit;
}
