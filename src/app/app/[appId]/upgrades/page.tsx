/* eslint-disable @typescript-eslint/no-unused-vars */
import { getQueryClient } from '@constants';
import { getAppPrefetchOptions } from '@entities/app';
import { fetchApp } from '@entities/app/api';
import { getUpgradesPrefetchOptions, Upgrades } from '@entities/upgrades';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { AppNavDesktop } from '@widgets/app-nav';
import { Header } from '@widgets/header';

import styles from './page.module.scss';

const Page = async (props: { params: { appId: string } }) => {
    const queryClient = getQueryClient();

    const app = await fetchApp(props.params.appId);

    await Promise.all([
        queryClient.prefetchQuery(
            getUpgradesPrefetchOptions(props.params.appId),
        ),
        queryClient.prefetchQuery(getAppPrefetchOptions(props.params.appId)),
    ]);

    return (
        <HydrationBoundary state={dehydrate(queryClient)}>
            <div className={styles.header}>
                <Header />
            </div>

            <div className={styles.container}>
                <div className={styles.left}>
                    {!app.token ? null : (
                        <AppNavDesktop appId={props.params.appId} />
                    )}
                </div>
                <div className={styles.content}>
                    <Upgrades appId={props.params.appId} />
                </div>
            </div>
        </HydrationBoundary>
    );
};

export const generateMetadata = async ({
    params,
}: {
    params: { appId: string };
}) => {
    const app = await fetchApp(params.appId);

    return {
        title: `${app.name} - Upgrades`,
        description: app.token?.description,
        alternates: {
            canonical: '/',
        },
        openGraph: {
            title: `${app.name} - Upgrades`,
            description: app.token?.description,
            images: [app.avatar],
        },
        twitter: {
            title: `${app.name} - Upgrades`,
            description: app.token?.description,
            images: [app.avatar],
        },
    };
};
export default Page;
