import { fetchApp } from '@entities/app/api';
import { Leagues } from '@entities/leagues';
import { AppNavDesktop } from '@widgets/app-nav';
import { Header } from '@widgets/header';

import styles from './page.module.scss';

const Page = async (props: { params: { appId: string } }) => {
    const { appId } = props.params;

    return (
        <>
            <div className={styles.header}>
                <Header />
            </div>

            <div className={styles.container}>
                <div className={styles.left}>
                    <AppNavDesktop appId={appId} />
                </div>
                <div className={styles.content}>
                    <Leagues appId={appId} />
                </div>
            </div>
        </>
    );
};

export const generateMetadata = async ({
    params,
}: {
    params: { appId: string };
}) => {
    const app = await fetchApp(params.appId);

    return {
        title: `${app.name} - Leagues`,
        description: app.token?.description,
        alternates: {
            canonical: '/',
        },
        openGraph: {
            title: `${app.name} - Leagues`,
            description: app.token?.description,
            images: [app.avatar],
        },
        twitter: {
            title: `${app.name} - Leagues`,
            description: app.token?.description,
            images: [app.avatar],
        },
    };
};
export default Page;
