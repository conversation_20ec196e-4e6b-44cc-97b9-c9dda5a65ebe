import { getQueryClient } from '@constants';
import {
    App,
    getAppPrefetchOptions,
    getPriceChangesPrefetchOptions,
    getTradeStatPrefetchOptions,
} from '@entities/app';
import { fetchApp } from '@entities/app/api';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { AppNavDesktop } from '@widgets/app-nav';
import { Header } from '@widgets/header';
import { redirect } from 'next/navigation';

import styles from './page.module.scss';

const Page = async (props: { params: { appId: string } }) => {
    const appId = props.params.appId;

    const queryClient = getQueryClient();

    await Promise.all([
        queryClient.prefetchQuery(getAppPrefetchOptions(appId)),
        queryClient.prefetchQuery(
            getTradeStatPrefetchOptions({
                appId,
                period: '60',
            }),
        ),
        queryClient.prefetchQuery(getPriceChangesPrefetchOptions(appId)),
    ]);

    return (
        <HydrationBoundary state={dehydrate(queryClient)}>
            <div className={styles.header}>
                <Header />
            </div>

            <div className={styles.container}>
                <div className={styles.left}>
                    <AppNavDesktop appId={appId} />
                </div>
                <div className={styles.content}>
                    <App appId={appId} />
                </div>
            </div>
        </HydrationBoundary>
    );
};

export const generateMetadata = async ({
    params,
}: {
    params: { appId: string };
}) => {
    const app = await fetchApp(params.appId);

    return {
        title: `${app.name}`,
        description: app.token?.description,
        alternates: {
            canonical: '/',
        },
        openGraph: {
            title: `${app.name}`,
            description: app.token?.description,
            images: [app.avatar],
        },
        twitter: {
            title: `${app.name}`,
            description: app.token?.description,
            images: [app.avatar],
        },
    };
};

export default Page;
