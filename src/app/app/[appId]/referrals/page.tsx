import { getQueryClient } from '@constants';
import { getAppPrefetchOptions } from '@entities/app';
import { fetchApp } from '@entities/app/api';
import { Referrals } from '@entities/referrals';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { AppNavDesktop } from '@widgets/app-nav';
import { Header } from '@widgets/header';

import styles from './page.module.scss';

const Page = async (props: { params: { appId: string } }) => {
    const { appId } = props.params;

    const queryClient = getQueryClient();

    await queryClient.prefetchQuery(getAppPrefetchOptions(appId));

    return (
        <HydrationBoundary state={dehydrate(queryClient)}>
            <div className={styles.header}>
                <Header />
            </div>

            <div className={styles.container}>
                <div className={styles.left}>
                    <AppNavDesktop appId={appId} />
                </div>
                <div className={styles.content}>
                    <Referrals appId={appId} />
                </div>
            </div>
        </HydrationBoundary>
    );
};

export const generateMetadata = async ({
    params,
}: {
    params: { appId: string };
}) => {
    const app = await fetchApp(params.appId);

    return {
        title: `${app.name} - Referrals`,
        description: app.token?.description,
        alternates: {
            canonical: '/',
        },
        openGraph: {
            title: `${app.name} - Referrals`,
            description: app.token?.description,
            images: [app.avatar],
        },
        twitter: {
            title: `${app.name} - Referrals`,
            description: app.token?.description,
            images: [app.avatar],
        },
    };
};
export default Page;
