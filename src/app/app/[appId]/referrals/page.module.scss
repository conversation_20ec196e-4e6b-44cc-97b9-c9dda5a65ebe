.header {
    width: 100%;

    @media screen and (min-width: 1024px) {
        margin-bottom: 40px;
    }
}

.container {
    width: 100%;

    display: flex;

    gap: 24px;

    @media screen and (min-width: 1200px) {
        max-width: 1200px;
        margin: 0 auto;
    }

    @media screen and (min-width: 1024px) and (max-width: 1200px) {
        padding: 0 16px;
    }
}

.content {
    width: 100%;
}

.left {
    @media screen and (max-width: 1024px) {
        display: none;
    }
}

.right {
    @media screen and (min-width: 1200px) {
        width: 150px;
    }
}
