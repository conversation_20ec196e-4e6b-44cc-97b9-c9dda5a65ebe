.container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    background-color: var(--color-bg-primary);
}

.content {
    max-width: 600px;
    width: 100%;
    padding: 40px;
    border-radius: 16px;
    background-color: var(--color-bg-secondary);
    text-align: center;
}

.title {
    font-size: 32px;
    font-weight: 700;
    color: var(--color-fg-primary);
    margin-bottom: 16px;
}

.description {
    margin-bottom: 24px;
}

.errorDetails {
    margin-bottom: 32px;
    padding: 16px;
    border-radius: 8px;
    background-color: var(--color-bg-tertiary);
    text-align: left;
}

.errorCode {
    display: block;
    margin-top: 8px;
    padding: 12px;
    border-radius: 6px;
    background-color: var(--color-bg-primary);
    color: var(--color-fg-secondary);
    font-family: monospace;
    font-size: 14px;
    line-height: 1.4;
    overflow-x: auto;
}

.actions {
    display: flex;
    gap: 12px;
    justify-content: center;

    @media (max-width: 480px) {
        flex-direction: column;
    }
}
