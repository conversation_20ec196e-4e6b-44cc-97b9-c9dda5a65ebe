import { getQueryClient } from '@constants';
import {
    fetchAppsPrefetchOptions,
    fetchPopularAppsPrefetchOptions,
    Home,
} from '@entities/home';
import { ErrorBoundary } from '@shared/ui/error-boundary';
import { ErrorPage } from '@shared/ui/error-page';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Header } from '@widgets/header';
import { NavDesktop } from '@widgets/nav';
import { Metadata } from 'next';

import styles from './page.module.scss';

/**
 * Main page component that displays the home screen with navigation and content
 * Implements SSR data fetching and hydration for optimal performance
 *
 * @component
 * @async
 * @returns {Promise<JSX.Element>} Rendered page component
 */
const Page = async () => {
    const queryClient = getQueryClient();

    try {
        await Promise.all([
            queryClient.prefetchQuery(fetchAppsPrefetchOptions),
            queryClient.prefetchQuery(fetchPopularAppsPrefetchOptions),
        ]);
    } catch (error) {
        console.error('Failed to prefetch data:', error);
        // Продолжаем выполнение, данные будут загружены на клиенте
    }

    return (
        <ErrorBoundary fallback={ErrorPage}>
            <HydrationBoundary state={dehydrate(queryClient)}>
                <div className={styles.header}>
                    <Header />
                </div>

                <div className={styles.container}>
                    <div className={styles.left}>
                        <NavDesktop />
                    </div>

                    <main className={styles.content}>
                        <Home />
                    </main>

                    <aside className={styles.right}>
                        {/* Зарезервировано для будущего контента */}
                    </aside>
                </div>
            </HydrationBoundary>
        </ErrorBoundary>
    );
};

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-cache';
export default Page;
