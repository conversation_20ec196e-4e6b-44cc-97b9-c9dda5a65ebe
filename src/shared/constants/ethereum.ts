import { Address } from 'viem';

// Chain IDs
export const CHAIN_IDS = {
    MAINNET: 1,
    SEPOLIA: 11155111,
    ANVIL_LOCAL: 31337,
    SOPHON_TESTNET: 531050104,
} as const;

// Get current chain ID from environment
export const getCurrentChainId = (): number => {
    const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;
    return chainId ? parseInt(chainId, 10) : CHAIN_IDS.ANVIL_LOCAL;
};

// Contract addresses - configurable by environment
export const getSodaFactoryAddress = (): Address => {
    // Check for environment variable first
    if (process.env.NEXT_PUBLIC_SODA_FACTORY_ADDRESS) {
        return process.env.NEXT_PUBLIC_SODA_FACTORY_ADDRESS as Address;
    }

    // Default addresses based on chain
    const chainId = getCurrentChainId();

    switch (chainId) {
        case CHAIN_IDS.MAINNET:
            return '******************************************' as Address; // Replace with actual mainnet address
        case CHAIN_IDS.SEPOLIA:
            return '******************************************' as Address; // Replace with actual sepolia address
        case CHAIN_IDS.SOPHON_TESTNET:
            return '******************************************' as Address; // Replace with actual Sophon testnet address
        case CHAIN_IDS.ANVIL_LOCAL:
        default:
            return '******************************************' as Address; // Common local deployment address
    }
};

export const ETHEREUM_NATIVE_ADDRESS: Address =
    '******************************************';

// Standard ERC20 ABI - minimal functions needed for token operations
export const ERC20_ABI = [
    {
        name: 'balanceOf',
        type: 'function',
        stateMutability: 'view',
        inputs: [{ name: 'account', type: 'address' }],
        outputs: [{ name: '', type: 'uint256' }],
    },
    {
        name: 'allowance',
        type: 'function',
        stateMutability: 'view',
        inputs: [
            { name: 'owner', type: 'address' },
            { name: 'spender', type: 'address' },
        ],
        outputs: [{ name: '', type: 'uint256' }],
    },
    {
        name: 'approve',
        type: 'function',
        stateMutability: 'nonpayable',
        inputs: [
            { name: 'spender', type: 'address' },
            { name: 'amount', type: 'uint256' },
        ],
        outputs: [{ name: '', type: 'bool' }],
    },
    {
        name: 'decimals',
        type: 'function',
        stateMutability: 'view',
        inputs: [],
        outputs: [{ name: '', type: 'uint8' }],
    },
] as const;

// Maximum uint256 value for infinite approval
export const MAX_UINT256 =
    '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff' as const;
