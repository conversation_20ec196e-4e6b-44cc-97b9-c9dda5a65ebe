/* eslint-disable max-lines-per-function */
/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import { Icons } from '@assets';
import { inter } from '@constants';
import { Token } from '@types';
import { AnimatedArrow, Button, Icon, Skeleton } from '@ui';
import { formatNumber } from '@utils';
import classNames from 'classnames';
import { motion } from 'framer-motion';
import React, { FC, useCallback, useMemo, useRef, useState } from 'react';

import styles from './swap-form.module.scss';

const variants = {
    open: { rotate: 180 },
    closed: { rotate: 0 },
};

interface ISwapForm {
    fromAmount: string;
    toAmount: string;
    setFromAmount: (amount: string) => void;
    tokenFrom?: Token;
    tokenTo?: Token;
    onSwitchTokens?: () => void;
    isEstimateLoading?: boolean;
    isLoading?: boolean;
    balanceFrom: string;
    balanceTo: string;
    openAssetsModal?: (isTokenFrom: boolean) => Promise<void>;
    customColor?: string;
}

export const SwapForm: FC<ISwapForm> = ({
    fromAmount,
    toAmount,
    setFromAmount,
    tokenFrom,
    tokenTo,
    onSwitchTokens,
    balanceFrom,
    balanceTo,
    isEstimateLoading,
    isLoading,
    openAssetsModal,
    customColor,
}) => {
    const input1Ref = useRef<HTMLInputElement | null>(null);
    const input2Ref = useRef<HTMLInputElement | null>(null);
    const [swapped, setSwapped] = useState<boolean>(false);
    const [arrow1Turn, setArrow1Turn] = useState<boolean>(false);
    const [arrow2Turn, setArrow2Turn] = useState<boolean>(false);

    const handleSwapClick = useCallback(() => {
        if (onSwitchTokens) {
            onSwitchTokens();
            setSwapped((prev) => !prev);
        }
    }, [tokenFrom, tokenTo, onSwitchTokens]);

    const amountChangeHandler = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            let inputValue = event.target.value.replace(/,/g, '.');
            inputValue = inputValue.replace(/^0+(?!$|\.)/, '');
            inputValue = inputValue.replace(/[^0-9.]+/, '');
            inputValue = inputValue.length === 0 ? '0' : inputValue;
            const parsedInput = parseFloat(inputValue);
            if (inputValue.startsWith('.')) {
                setFromAmount('0');

                return;
            }
            const isValidInput =
                /^-?\d*\.?\d*$/.test(inputValue) && !isNaN(parsedInput);

            if (isValidInput) {
                if (
                    tokenFrom?.type === 'native' &&
                    Number(inputValue) > 100_000
                ) {
                    setFromAmount('1000000');
                } else {
                    setFromAmount(inputValue);
                }
            }
        },
        [setFromAmount, tokenFrom],
    );

    const isError = useMemo(
        () => Number(fromAmount) > Number(balanceFrom),
        [fromAmount, balanceFrom],
    );

    const openAssetsModalHandler = useCallback(
        (isTokenFrom: boolean) => () => {
            if (openAssetsModal) {
                if (isTokenFrom) {
                    setArrow1Turn((prev) => !prev);
                } else {
                    setArrow2Turn((prev) => !prev);
                }
                openAssetsModal(isTokenFrom);
            }
        },
        [openAssetsModal],
    );

    return (
        <div className={styles.form}>
            <div
                onClick={() => input1Ref.current?.focus()}
                className={styles.formItem}
            >
                <div className={styles.formItemRow}>
                    <div className={styles.formItemText}>Pay</div>
                    <div className={styles.formItemText}>
                        Balance:{' '}
                        <span
                            onClick={() => {
                                setFromAmount(balanceFrom);
                            }}
                            style={{ cursor: 'pointer' }}
                        >
                            {formatNumber(balanceFrom)}
                        </span>
                    </div>
                </div>
                <div className={styles.formItemRow}>
                    {isLoading ? (
                        <Skeleton height="24px" width="100px" />
                    ) : tokenFrom?.type === 'native' ? (
                        <div
                            className={styles.formItemToken}
                            onClick={openAssetsModalHandler(true)}
                        >
                            <div className={styles.formItemTokenTon}>
                                <Icons.Eth />
                            </div>

                            <h2>ETH</h2>

                            {openAssetsModal && (
                                <AnimatedArrow arrowTurn={arrow1Turn} />
                            )}
                        </div>
                    ) : (
                        <div
                            className={styles.formItemToken}
                            onClick={openAssetsModalHandler(true)}
                        >
                            <Icon
                                autoplay
                                size="m"
                                src={tokenFrom?.image}
                                className={styles.tokenLogo}
                            />
                            <h2>{tokenFrom?.symbol}</h2>

                            {openAssetsModal && (
                                <AnimatedArrow arrowTurn={arrow1Turn} />
                            )}
                        </div>
                    )}
                    {
                        <input
                            ref={input1Ref}
                            value={fromAmount}
                            placeholder="0"
                            onChange={amountChangeHandler}
                            disabled={isLoading}
                            inputMode="decimal"
                            pattern="[0-9]*[.,]?[0-9]*"
                            className={classNames(
                                styles.input,
                                inter.className,
                                {
                                    [styles.error]: isError,
                                },
                            )}
                        />
                    }
                </div>
            </div>
            {onSwitchTokens && (
                <div className={styles.formSwitch}>
                    <Button
                        style={{
                            padding: '8px 12px',
                            margin: '0',
                            fontSize: '16px',
                            background:
                                'linear-gradient(0deg, var(--color-neutral) 0%, var(--color-neutral) 100%), var(--color-bg-card)',
                            color: 'var(--color-fg-tertiary)',
                        }}
                        size="icon"
                        borderRadius="l"
                        view="action"
                        onClick={handleSwapClick}
                    >
                        <motion.div
                            animate={swapped ? 'open' : 'closed'}
                            variants={variants}
                            className={styles.formSwitchWrapper}
                        >
                            <Icons.Swap />
                        </motion.div>
                    </Button>
                </div>
            )}

            <div
                onClick={() => input2Ref.current?.focus()}
                className={styles.formItem}
            >
                <div className={styles.formItemRow}>
                    <div className={styles.formItemText}>Receive</div>
                    <div className={styles.formItemText}>
                        Balance: {formatNumber(balanceTo)}
                    </div>
                </div>
                <div className={styles.formItemRow}>
                    {isLoading ? (
                        <Skeleton height="24px" width="100px" />
                    ) : tokenTo?.type === 'native' ? (
                        <div
                            className={styles.formItemToken}
                            onClick={openAssetsModalHandler(false)}
                        >
                            <div className={styles.formItemTokenTon}>
                                <Icons.Eth />
                            </div>

                            <h2>ETH</h2>

                            {openAssetsModal && (
                                <AnimatedArrow arrowTurn={arrow2Turn} />
                            )}
                        </div>
                    ) : (
                        <div
                            className={styles.formItemToken}
                            onClick={openAssetsModalHandler(false)}
                        >
                            <Icon
                                autoplay
                                className={styles.tokenLogo}
                                size="m"
                                src={tokenTo?.image}
                            />
                            <h2>{tokenTo?.symbol}</h2>

                            {openAssetsModal && (
                                <AnimatedArrow arrowTurn={arrow2Turn} />
                            )}
                        </div>
                    )}
                    {isEstimateLoading || isLoading ? (
                        <Skeleton height="28px" width="100px" />
                    ) : (
                        <input
                            ref={input2Ref}
                            value={Number(toAmount)
                                .toFixed(6)
                                .replace(/\.?0+$/, '')}
                            placeholder="0"
                            disabled
                            className={`${styles.input} ${inter.className}`}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};
