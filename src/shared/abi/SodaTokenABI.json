{"abi": [{"type": "constructor", "inputs": [{"name": "_params", "type": "tuple", "internalType": "struct ISodaToken.ConstructorParams", "components": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "creator", "type": "address", "internalType": "address"}, {"name": "totalSupply", "type": "uint256", "internalType": "uint256"}, {"name": "virtualTokenReserves", "type": "uint256", "internalType": "uint256"}, {"name": "virtualCollateralReserves", "type": "uint256", "internalType": "uint256"}, {"name": "feeBasisPoints", "type": "uint256", "internalType": "uint256"}, {"name": "migrationFeeFixed", "type": "uint256", "internalType": "uint256"}, {"name": "poolCreationFee", "type": "uint256", "internalType": "uint256"}, {"name": "mcLowerLimit", "type": "uint256", "internalType": "uint256"}, {"name": "mcUpperLimit", "type": "uint256", "internalType": "uint256"}, {"name": "tokensMigrationThreshold", "type": "uint256", "internalType": "uint256"}, {"name": "treasury", "type": "address", "internalType": "address"}, {"name": "uniV2Router", "type": "address", "internalType": "address"}, {"name": "WETH", "type": "address", "internalType": "address"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "MAX_BPS", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "buyExactIn", "inputs": [{"name": "_amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateralToPayWithFee", "type": "uint256", "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "buyExactOut", "inputs": [{"name": "_tokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_maxCollateralAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateralToPayWithFee", "type": "uint256", "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "creator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "factory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "feeBPS", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "fixedMigrationFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAmountInAndFee", "inputs": [{"name": "_amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "_reserveIn", "type": "uint256", "internalType": "uint256"}, {"name": "_reserveOut", "type": "uint256", "internalType": "uint256"}, {"name": "_paymentTokenIsOut", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "fee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAmountOut", "inputs": [{"name": "_amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "_collateralTokenIsIn", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "fee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAmountOutAndFee", "inputs": [{"name": "_amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "_reserveIn", "type": "uint256", "internalType": "uint256"}, {"name": "_reserveOut", "type": "uint256", "internalType": "uint256"}, {"name": "_paymentTokenIsIn", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "fee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurveProgressBps", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMarketCap", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialTokenSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mcLowerLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mcUpperLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "migrate", "inputs": [], "outputs": [{"name": "tokensToMigrate", "type": "uint256", "internalType": "uint256"}, {"name": "tokensToBurn", "type": "uint256", "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "pair", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "poolCreationFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sellExactIn", "inputs": [{"name": "_tokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_amountCollateralMin", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateralToReceiveMinusFee", "type": "uint256", "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "sellExactOut", "inputs": [{"name": "_tokenAmountMax", "type": "uint256", "internalType": "uint256"}, {"name": "_amountCollateral", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateralToReceiveMinusFee", "type": "uint256", "internalType": "uint256"}, {"name": "tokensOut", "type": "uint256", "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "sendingToPairNotAllowed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokensMigrationThreshold", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "tradingStopped", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "_to", "type": "address", "internalType": "address"}, {"name": "_value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "treasury", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "uniswapV2Router", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IPoolFactory"}], "stateMutability": "view"}, {"type": "function", "name": "virtualCollateralReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "virtualCollateralReservesInitial", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "virtualTokenReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedToSendETH", "inputs": []}, {"type": "error", "name": "InsufficientTokenReserves", "inputs": []}, {"type": "error", "name": "MarketcapThresholdReached", "inputs": []}, {"type": "error", "name": "NotEnoughETHReserves", "inputs": []}, {"type": "error", "name": "NotEnoughtETHToBuyTokens", "inputs": []}, {"type": "error", "name": "OnlyFactory", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SendingToPairIsNotAllowedBeforeMigration", "inputs": []}, {"type": "error", "name": "SlippageCheckFailed", "inputs": []}, {"type": "error", "name": "TradingStopped", "inputs": []}]}