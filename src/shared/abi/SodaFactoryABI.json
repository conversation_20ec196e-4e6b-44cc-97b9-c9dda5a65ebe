{"abi": [{"type": "constructor", "inputs": [{"name": "_totalSupply", "type": "uint256", "internalType": "uint256"}, {"name": "_virtualTokenReserves", "type": "uint256", "internalType": "uint256"}, {"name": "_virtualCollateralReserves", "type": "uint256", "internalType": "uint256"}, {"name": "_feeBasisPoints", "type": "uint256", "internalType": "uint256"}, {"name": "_migrationFeeFixed", "type": "uint256", "internalType": "uint256"}, {"name": "_poolCreationFee", "type": "uint256", "internalType": "uint256"}, {"name": "_mcUpperLimit", "type": "uint256", "internalType": "uint256"}, {"name": "_mcLowerLimit", "type": "uint256", "internalType": "uint256"}, {"name": "_tokensMigrationThreshold", "type": "uint256", "internalType": "uint256"}, {"name": "_treasury", "type": "address", "internalType": "address"}, {"name": "_uniswapV2Router", "type": "address", "internalType": "address"}, {"name": "_WETH", "type": "address", "internalType": "address"}, {"name": "_signer", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "MIN_REFUND_AMOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "UNISWAP_V2_ROUTER", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "WETH", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "buyExactIn", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "buyExactOut", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}, {"name": "_tokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_maxCollateralAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "createSodaToken", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_symbol", "type": "string", "internalType": "string"}, {"name": "_nonce", "type": "uint256", "internalType": "uint256"}, {"name": "_signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createSodaTokenAndBuy", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_symbol", "type": "string", "internalType": "string"}, {"name": "_nonce", "type": "uint256", "internalType": "uint256"}, {"name": "_tokenAmountMin", "type": "uint256", "internalType": "uint256"}, {"name": "_signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "feeBasisPoints", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mcLowerLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mcUpperLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "migrate", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "migrationFeeFixed", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "poolCreationFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "readyForMigration", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sellExactIn", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}, {"name": "_tokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_amountCollateralMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sellExactOut", "inputs": [{"name": "_token", "type": "address", "internalType": "address"}, {"name": "_tokenAmountMax", "type": "uint256", "internalType": "uint256"}, {"name": "_amountCollateral", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setConfig", "inputs": [{"name": "_totalSupply", "type": "uint256", "internalType": "uint256"}, {"name": "_virtualTokenReserves", "type": "uint256", "internalType": "uint256"}, {"name": "_virtualCollateralReserves", "type": "uint256", "internalType": "uint256"}, {"name": "_feeBasisPoints", "type": "uint256", "internalType": "uint256"}, {"name": "_migrationFeeFixed", "type": "uint256", "internalType": "uint256"}, {"name": "_poolCreationFee", "type": "uint256", "internalType": "uint256"}, {"name": "_mcUpperLimit", "type": "uint256", "internalType": "uint256"}, {"name": "_mcLowerLimit", "type": "uint256", "internalType": "uint256"}, {"name": "_tokensMigrationThreshold", "type": "uint256", "internalType": "uint256"}, {"name": "_treasury", "type": "address", "internalType": "address"}, {"name": "_WETH", "type": "address", "internalType": "address"}, {"name": "_signer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "sodaTokens", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "tokensMigrationThreshold", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "treasury", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "usedSignatures", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "virtualCollateralReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "virtualTokenReserves", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "BuyExactIn", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curvePositionAfterTrade", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curveProgressBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "marketCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BuyExactOut", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curvePositionAfterTrade", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "refund", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curveProgressBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "marketCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MarketcapReached", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Migrated", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "tokensToMigrate", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokensToBurn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralToMigrate", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "migrationFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "pair", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewSodaToken", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "creator", "type": "address", "indexed": false, "internalType": "address"}, {"name": "signature", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "NewSodaTokenAndBuy", "inputs": [{"name": "user", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "signature", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curveProgressBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "marketCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SellExactIn", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curvePositionAfterTrade", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curveProgressBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "marketCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SellExactOut", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curvePositionAfterTrade", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasuryFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "curveProgressBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "marketCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetConfig", "inputs": [{"name": "totalSupply", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "virtualTokenReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "virtualCollateralReserves", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeBasisPoints", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "migrationFeeFixed", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "poolCreationFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mcUpperLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "mcLowerLimit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokensMigrationThreshold", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "treasury", "type": "address", "indexed": false, "internalType": "address"}, {"name": "WETH", "type": "address", "indexed": false, "internalType": "address"}, {"name": "signer", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "DexTreasuryZeroValue", "inputs": []}, {"type": "error", "name": "FailedToSendETH", "inputs": []}, {"type": "error", "name": "FeeBPSCheckFailed", "inputs": []}, {"type": "error", "name": "InvalidSignature", "inputs": []}, {"type": "error", "name": "McLowerLimitGreaterThanUpperLimit", "inputs": []}, {"type": "error", "name": "McLowerLimitZeroValue", "inputs": []}, {"type": "error", "name": "McUpperLimitZeroValue", "inputs": []}, {"type": "error", "name": "Migration<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": []}, {"type": "error", "name": "NotReadyForMigration", "inputs": []}, {"type": "error", "name": "NotSodaToken", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SignatureIsUsed", "inputs": []}, {"type": "error", "name": "SignerZeroValue", "inputs": []}, {"type": "error", "name": "TokensMigrationThresholdZeroValue", "inputs": []}, {"type": "error", "name": "TotalSupplyWrongValue", "inputs": []}, {"type": "error", "name": "TotalSupplyZeroValue", "inputs": []}, {"type": "error", "name": "TreasuryZeroValue", "inputs": []}, {"type": "error", "name": "VirtualCollateralReservesZeroValue", "inputs": []}, {"type": "error", "name": "VirtualTokenReservesZeroValue", "inputs": []}, {"type": "error", "name": "WETHZeroValue", "inputs": []}]}