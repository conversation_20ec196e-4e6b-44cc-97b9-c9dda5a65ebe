// simulate-trades.ts - rewritten with Type<PERSON> and viem
import fs from 'fs';
import path from 'path';
import assert from 'assert';
import { webcrypto as crypto } from 'crypto';
import { fileURLToPath } from 'url';

// Compute __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

import {
    createTestClient,
    createWalletClient,
    createPublicClient,
    http,
    encodeAbiParameters,
    keccak256,
    hexToBytes,
    parseEther,
    parseUnits,
    decodeEventLog,
} from 'viem';
import { foundry } from 'viem/chains';
import { privateKeyToAccount } from 'viem/accounts';

// Helper function for setting balance
async function setBalance(
    testClient: any,
    address: `0x${string}`,
    value: bigint,
) {
    await testClient.setBalance({ address, value });
}

(async () => {
    try {
        // Initialize test client
        const testClient = createTestClient({
            chain: foundry,
            mode: 'anvil',
            transport: http(),
        });

        // Initialize public client for reading
        const publicClient = createPublicClient({
            chain: foundry,
            transport: http(),
        });

        // 1) Setup signer wallet client
        const signerPrivateKey =
            '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
        const signerAccount = privateKeyToAccount(signerPrivateKey);
        const walletClient = createWalletClient({
            chain: foundry,
            transport: http(),
            account: signerAccount,
        });
        console.log('Signer:', signerAccount.address);
        await testClient.setBalance({
            address: signerAccount.address,
            value: parseEther('5'),
        });
        const signerBalance = await publicClient.getBalance({
            address: signerAccount.address,
        });
        assert(signerBalance === parseEther('5'), 'Signer must have some ETH');

        // 2) Deploy TestWETH
        const TestWETHArtifact = JSON.parse(
            fs.readFileSync(
                path.join(__dirname, 'out', 'TestWETH.sol', 'TestWETH.json'),
                'utf8',
            ),
        ) as { abi: any; bytecode: { object: string } };
        const txHashWETH = await walletClient.deployContract({
            abi: TestWETHArtifact.abi,
            bytecode: TestWETHArtifact.bytecode.object as `0x${string}`,
            args: [],
        });
        const receiptWETH = await publicClient.waitForTransactionReceipt({
            hash: txHashWETH,
        });
        const wethAddress = receiptWETH.contractAddress as `0x${string}`;
        console.log('Deployed TestWETH at', wethAddress);

        // 3) Deploy SodaFactory
        const SodaFactoryArtifact = JSON.parse(
            fs.readFileSync(
                path.join(
                    __dirname,
                    'out',
                    'SodaFactory.sol',
                    'SodaFactory.json',
                ),
                'utf8',
            ),
        ) as { abi: any; bytecode: { object: string } };
        const txHashFactory = await walletClient.deployContract({
            abi: SodaFactoryArtifact.abi,
            bytecode: SodaFactoryArtifact.bytecode.object as `0x${string}`,
            args: [
                parseUnits('1000000000', 18), // totalSupply - much smaller
                parseUnits('1060000000', 18), // virtualTokenReserves - much smaller
                parseUnits('1.6', 18), // virtualCollateralReserves - much smaller
                100, // feeBasisPoints (now the only fee)
                parseUnits('0.1', 18), // migrationFeeFixed
                parseUnits('0.05', 18), // poolCreationFee
                parseUnits('27', 18), // mcUpperLimit - higher than lower limit
                parseUnits('25', 18), // mcLowerLimit - lower than upper limit
                parseUnits('*********', 18), // tokensMigrationThreshold - much smaller
                '******************************************', // treasury
                '******************************************', // UNISWAP_V2_ROUTER
                wethAddress, // WETH
                signerAccount.address, // signer
            ],
        });
        const receiptFactory = await publicClient.waitForTransactionReceipt({
            hash: txHashFactory,
        });
        const factoryAddress = receiptFactory.contractAddress as `0x${string}`;
        console.log('Deployed SodaFactory at', factoryAddress);

        // 4) Signature helper
        async function createSignature(
            name: string,
            symbol: string,
            nonce: number,
            factoryAddr: `0x${string}`,
            chainId: number,
            sender: `0x${string}`,
        ) {
            // Use encodePacked to match the contract's expectation
            const types = [
                'string',
                'string',
                'uint256',
                'address',
                'uint256',
                'address',
            ];
            const values = [
                name,
                symbol,
                BigInt(nonce),
                factoryAddr,
                BigInt(chainId),
                sender,
            ];

            // Manually create packed encoding since viem doesn't have encodePacked
            const nameBytes = new TextEncoder().encode(name);
            const symbolBytes = new TextEncoder().encode(symbol);
            const nonceBytes = new Uint8Array(32);
            const factoryBytes = hexToBytes(factoryAddr);
            const chainIdBytes = new Uint8Array(32);
            const senderBytes = hexToBytes(sender);

            // Convert bigints to bytes (32 bytes each, big endian)
            const nonceView = new DataView(nonceBytes.buffer);
            nonceView.setBigUint64(24, BigInt(nonce), false); // big endian

            const chainIdView = new DataView(chainIdBytes.buffer);
            chainIdView.setBigUint64(24, BigInt(chainId), false); // big endian

            // Concatenate all bytes
            const packed = new Uint8Array(
                nameBytes.length + symbolBytes.length + 32 + 20 + 32 + 20,
            );
            let offset = 0;
            packed.set(nameBytes, offset);
            offset += nameBytes.length;
            packed.set(symbolBytes, offset);
            offset += symbolBytes.length;
            packed.set(nonceBytes, offset);
            offset += 32;
            packed.set(factoryBytes, offset);
            offset += 20;
            packed.set(chainIdBytes, offset);
            offset += 32;
            packed.set(senderBytes, offset);

            const hash = keccak256(
                `0x${Array.from(packed)
                    .map((b) => b.toString(16).padStart(2, '0'))
                    .join('')}` as `0x${string}`,
            );

            // Sign the message using EIP-191 format (this is handled automatically by signMessage)
            return await walletClient.signMessage({
                message: { raw: hexToBytes(hash) },
            });
        }

        // 5) Create token and buy
        const creatorKey = ('0x' + '05'.padStart(64, '0')) as `0x${string}`;
        const creatorAccount = privateKeyToAccount(creatorKey);
        const creatorClient = createWalletClient({
            chain: foundry,
            transport: http(),
            account: creatorAccount,
        });
        await setBalance(testClient, creatorAccount.address, parseEther('5'));
        const creatorBal = await publicClient.getBalance({
            address: creatorAccount.address,
        });
        assert(creatorBal === parseEther('5'), 'Creator must have some ETH');
        console.log('Airdropped ETH to creator', creatorAccount.address);

        const name = 'MyToken';
        const symbol = 'MTK';
        const nonce = 1;
        const chainId = await publicClient.getChainId();
        const sig = await createSignature(
            name,
            symbol,
            nonce,
            factoryAddress,
            chainId,
            creatorAccount.address,
        );

        const buyTx = await creatorClient.writeContract({
            address: factoryAddress,
            abi: SodaFactoryArtifact.abi,
            functionName: 'createSodaTokenAndBuy',
            args: [name, symbol, BigInt(nonce), BigInt(0), sig], // no DEX fee/treasury args
            value: parseEther('1'),
        });
        const buyReceipt = await publicClient.waitForTransactionReceipt({
            hash: buyTx,
        });

        // Extract token address from the NewSodaTokenAndBuy event
        let tokenAddress: `0x${string}` | null = null;

        for (const log of buyReceipt.logs) {
            try {
                const decodedLog = decodeEventLog({
                    abi: SodaFactoryArtifact.abi,
                    data: log.data,
                    topics: log.topics,
                }) as any; // explicitly cast to any to avoid TS error

                if (decodedLog.eventName === 'NewSodaTokenAndBuy') {
                    tokenAddress = decodedLog.args.token as `0x${string}`;
                    break;
                }
            } catch (e) {
                // Ignore logs that don't match our ABI
                continue;
            }
        }

        if (!tokenAddress) {
            throw new Error(
                'NewSodaTokenAndBuy event not found in transaction logs',
            );
        }
        console.log('Created MTK token at', tokenAddress);

        // load token ABI
        const TokenArtifact = JSON.parse(
            fs.readFileSync(
                path.join(__dirname, 'out', 'SodaToken.sol', 'SodaToken.json'),
                'utf8',
            ),
        ) as { abi: any };

        // Helper function for random delay
        const randomDelay = () => {
            const delayMs = Math.random() * 1500 + 500; // 0.5 to 2 seconds
            return new Promise((resolve) => setTimeout(resolve, delayMs));
        };

        // 6) Simulate trades
        for (let i = 0; ; i++) {
            // Add random delay between trades
            const delay = Math.random() * 1500 + 500; // 0.5 to 2 seconds in ms
            console.log(
                `Waiting ${(delay / 1000).toFixed(2)} seconds before next trade...`,
            );
            await new Promise((resolve) => setTimeout(resolve, delay));

            const rand = crypto.getRandomValues(new Uint8Array(32));
            const traderKey = `0x${Buffer.from(rand).toString(
                'hex',
            )}` as `0x${string}`;
            const traderAccount = privateKeyToAccount(traderKey);
            const traderClient = createWalletClient({
                chain: foundry,
                transport: http(),
                account: traderAccount,
            });

            await setBalance(
                testClient,
                traderAccount.address,
                parseEther('5'),
            );
            const bal = await publicClient.getBalance({
                address: traderAccount.address,
            });
            console.log(
                'Trader',
                traderAccount.address,
                'balance',
                bal.toString(),
            );

            // buy
            const buyAmount = parseEther(
                (Math.random() * 0.02 + 0.001).toFixed(18),
            );
            console.log('Buying with', buyAmount.toString(), 'ETH');
            try {
                await traderClient.writeContract({
                    address: factoryAddress,
                    abi: SodaFactoryArtifact.abi,
                    functionName: 'buyExactIn',
                    args: [tokenAddress, BigInt(0)],
                    value: buyAmount,
                });
                console.log('Buy successful for', traderAccount.address);
            } catch (e: any) {
                console.log(
                    'Buy failed for',
                    traderAccount.address,
                    'Reason:',
                    e.message,
                );
            }

            // sell
            if (Math.random() < 0.5) {
                const tokenBal = await publicClient.readContract({
                    address: tokenAddress,
                    abi: TokenArtifact.abi,
                    functionName: 'balanceOf',
                    args: [traderAccount.address],
                });
                if ((tokenBal as bigint) > 0n) {
                    await traderClient.writeContract({
                        address: tokenAddress,
                        abi: TokenArtifact.abi,
                        functionName: 'approve',
                        args: [
                            factoryAddress,
                            BigInt(
                                '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff',
                            ),
                        ],
                    });
                    const sellAmt = (tokenBal as bigint) / 2n + 1n;
                    console.log(
                        'Selling',
                        sellAmt.toString(),
                        'tokens for',
                        traderAccount.address,
                    );
                    try {
                        await traderClient.writeContract({
                            address: factoryAddress,
                            abi: SodaFactoryArtifact.abi,
                            functionName: 'sellExactIn',
                            args: [tokenAddress, sellAmt, BigInt(0)],
                        });
                        console.log(
                            'Sell successful for',
                            traderAccount.address,
                        );
                    } catch (e: any) {
                        console.log(
                            'Sell failed for',
                            traderAccount.address,
                            'Reason:',
                            e.message,
                        );
                    }
                } else {
                    console.log(
                        'Trader',
                        traderAccount.address,
                        'has no tokens to sell',
                    );
                }
            }
        }
    } catch (e: any) {
        console.error('Unhandled exception:', e);
        if (e instanceof Error) {
            console.error(e.stack);
        } else {
            console.error(JSON.stringify(e, Object.getOwnPropertyNames(e), 2));
        }
        process.exit(1);
    }
})();
