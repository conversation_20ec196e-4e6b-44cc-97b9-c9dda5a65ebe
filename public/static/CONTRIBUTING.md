## General
* Please use English. We want to share experience between our customers to increase effectiveness of this bug tracker.

## Reporting a bug
* Please help us spend our time effectively and always update your Library's build to the latest available version to check if the issue still happens. You can also use the Charting Library [demo website](https://charting-library.tradingview-widget.com/) to try your scenario.
* Report a version of the library. You can check it with `TradingView.version()` from the browser console
* Report a device/browser where the issue can be replicated
* Provide a step-by-step way to reproduce the issue

If possible
* Provide a code example and/or give a link to your Charting Library where we can reproduce it. Alternatively you can use an online editor and share the code with us. Documentation can be found [here](https://www.tradingview.com/charting-library-docs/latest/getting_started/Online-Editors).

## Asking a question
* We spent a lot of time creating those [docs](https://www.tradingview.com/charting-library-docs/) for you to make your life easier. Please give it a try. If you are a newcomer please make sure that you've read the [Best Practices](https://www.tradingview.com/charting-library-docs/latest/getting_started/Best-Practices) and [Frequently Asked Questions](https://www.tradingview.com/charting-library-docs/latest/getting_started/Frequently-Asked-Questions).
* Describe what do you want to achieve
* Provide screenshots and/or a gif and/or a video if possible
 
## Requesting a new feature
* While we would love to be able to implement everything, we simply don’t have the resources. At this time, please be aware we have long term plans and quick implementation of new feature requests should not be expected. However, we review everything and take your many great suggestions into account. 
* Thank you for playing an active part in the TradingView community!
