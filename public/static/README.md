# TradingView Advanced Charts

[Demo][demo-url] | [Documentation][doc-url] | [Tutorial][tutorial-url] | [Discord community][discord-url]

This repository contains the TradingView Advanced Charts package. If you use Git in your project, please feel free to use this repository as a submodule in yours.

The `master` branch contains the most recent features and fixes.

Before using the library, please read the [documentation][doc-url] and the [Best Practices][best-practices-url] article.

### Issues Tracking
We use GitHub [Issues tracker][issues-url] for our project. Feel free to create bug reports and feature requests. Make sure to read the documentation before asking questions — this will avoid repeated questions, leaving us more time for developing the library.

### Stay Tuned
[Follow us][x-url] on X to receive updates.

[demo-url]: https://charting-library.tradingview.com/
[doc-url]: https://www.tradingview.com/charting-library-docs/
[tutorial-url]: https://github.com/tradingview/charting-library-tutorial
[best-practices-url]: https://www.tradingview.com/charting-library-docs/latest/getting_started/Best-Practices
[issues-url]: https://github.com/tradingview/charting_library/issues
[x-url]: https://twitter.com/intent/follow?screen_name=tv_charts
[discord-url]: https://discord.gg/UC7cGkvn4U
