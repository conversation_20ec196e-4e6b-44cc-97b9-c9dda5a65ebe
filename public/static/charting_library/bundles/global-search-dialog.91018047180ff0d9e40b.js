(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9754,9685],{74786:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});const o=function(){}},70048:e=>{e.exports={wrapper:"wrapper-GZajBGIm",input:"input-GZajBGIm",box:"box-GZajBGIm",icon:"icon-GZajBGIm",noOutline:"noOutline-GZajBGIm","intent-danger":"intent-danger-GZajBGIm",check:"check-GZajBGIm",dot:"dot-GZajBGIm"}},25650:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},82343:e=>{e.exports={wrap:"wrap-HAxAr6QG",image:"image-HAxAr6QG",text:"text-HAxAr6QG"}},95390:e=>{e.exports={section:"section-Og4Rg_SK",heading:"heading-Og4Rg_SK"}},32108:e=>{e.exports={item:"item-nuuDM7vP",normal:"normal-nuuDM7vP",big:"big-nuuDM7vP",selected:"selected-nuuDM7vP",contentCell:"contentCell-nuuDM7vP",content:"content-nuuDM7vP",favourite:"favourite-nuuDM7vP",iconCell:"iconCell-nuuDM7vP",icon:"icon-nuuDM7vP",checkboxInput:"checkboxInput-nuuDM7vP",label:"label-nuuDM7vP"}},61213:e=>{e.exports={dialog:"dialog-UAy2ZKyS",wrap:"wrap-UAy2ZKyS",empty:"empty-UAy2ZKyS",image:"image-UAy2ZKyS",emptyState:"emptyState-UAy2ZKyS"}},44445:e=>{e.exports={accessible:"accessible-rm8yeqY4"}},51331:e=>{e.exports={loaderWrap:"loaderWrap-jGEARQlM",big:"big-jGEARQlM",loader:"loader-jGEARQlM"}},22436:e=>{e.exports={item:"item-GJX1EXhk",interactive:"interactive-GJX1EXhk",hovered:"hovered-GJX1EXhk",disabled:"disabled-GJX1EXhk",active:"active-GJX1EXhk",shortcut:"shortcut-GJX1EXhk",normal:"normal-GJX1EXhk",big:"big-GJX1EXhk",iconCell:"iconCell-GJX1EXhk",icon:"icon-GJX1EXhk",checkmark:"checkmark-GJX1EXhk",content:"content-GJX1EXhk",label:"label-GJX1EXhk",checked:"checked-GJX1EXhk",toolbox:"toolbox-GJX1EXhk",showToolboxOnHover:"showToolboxOnHover-GJX1EXhk",arrowIcon:"arrowIcon-GJX1EXhk",subMenu:"subMenu-GJX1EXhk",invisibleHotkey:"invisibleHotkey-GJX1EXhk"}},86838:e=>{e.exports={row:"row-DFIg7eOh",line:"line-DFIg7eOh",hint:"hint-DFIg7eOh"}},36002:e=>{e.exports={menu:"menu-Tx5xMZww"}},29122:e=>{e.exports={item:"item-WJDah4zD",emptyIcons:"emptyIcons-WJDah4zD",loading:"loading-WJDah4zD",disabled:"disabled-WJDah4zD",interactive:"interactive-WJDah4zD",hovered:"hovered-WJDah4zD",normal:"normal-WJDah4zD",big:"big-WJDah4zD",icon:"icon-WJDah4zD",label:"label-WJDah4zD",title:"title-WJDah4zD",nested:"nested-WJDah4zD",shortcut:"shortcut-WJDah4zD",remove:"remove-WJDah4zD"}},33927:e=>{e.exports={separator:"separator-Ymxd0dt_"}},66076:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},27306:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},70673:(e,t,n)=>{"use strict";n.d(t,{CheckboxInput:()=>h})
;var o=n(50959),s=n(97754),l=n(90186),i=n(9745),a=n(65890),r=n(70048),c=n.n(r);function h(e){const t=s(c().box,c()[`intent-${e.intent}`],{[c().check]:!Boolean(e.indeterminate),[c().dot]:Boolean(e.indeterminate),[c().noOutline]:-1===e.tabIndex}),n=s(c().wrapper,e.className);return o.createElement("span",{className:n,title:e.title,style:e.style},o.createElement("input",{id:e.id,tabIndex:e.tabIndex,className:c().input,type:"checkbox",name:e.name,checked:e.checked,disabled:e.disabled,value:e.value,autoFocus:e.autoFocus,role:e.role,onChange:function(){e.onChange&&e.onChange(e.value)},ref:e.reference,"aria-required":e["aria-required"],"aria-describedby":e["aria-describedby"],"aria-invalid":e["aria-invalid"],...(0,l.filterDataProps)(e)}),o.createElement("span",{className:t},o.createElement(i.Icon,{icon:a,className:c().icon})))}},26996:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>a});var o=n(50959),s=n(97754),l=n(25650),i=n.n(l);function a(e){const{className:t,size:n="medium",staticPosition:l,color:a="black"}=e,r=s(i().item,i()[a],i()[n]);return o.createElement("span",{className:s(i().loader,l&&i().static,t)},o.createElement("span",{className:r}),o.createElement("span",{className:r}),o.createElement("span",{className:r}))}},89324:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Components:()=>h,showDefaultSearchDialog:()=>c,showSymbolSearchItemsDialog:()=>i.showSymbolSearchItemsDialog});var o=n(82992),s=(n(32563),n(31330)),l=n(65106),i=n(1861),a=n(97145),r=n(84015);n(14483),n(49483);!(0,r.isOnMobileAppPage)("any")&&window.matchMedia("(min-width: 602px) and (min-height: 445px)").matches;function c(e){new a.WatchedValue({});const t=(0,l.getSymbolSearchCompleteOverrideFunction)(),{defaultValue:n,showSpreadActions:r,source:c,onSearchComplete:h,trackResultsOptions:d,...u}=e,v={...u,showSpreadActions:null!=r?r:(0,s.canShowSpreadActions)(),onSymbolFiltersParamsChange:void 0,onSearchComplete:(e,n)=>{null==n||n.symbolType;t(e[0].symbol,e[0].result).then((e=>{o.linking.setSymbolAndLogInitiator(e.symbol,"symbol search"),null==h||h(e.symbol)}))},onEmptyResults:void 0};(0,i.showSymbolSearchItemsDialog)({...v,defaultValue:n})}const h={SymbolSearchWatchlistDialogContentItem:null,SymbolSearchWatchlistDialog:null}},1861:(e,t,n)=>{"use strict";n.d(t,{showSymbolSearchItemsDialog:()=>r});var o=n(50959),s=n(962),l=n(50655),i=n(51826),a=n(22350);function r(e){const{initialMode:t="symbolSearch",autofocus:n=!0,defaultValue:r,showSpreadActions:c,selectSearchOnInit:h,onSearchComplete:d,dialogTitle:u,placeholder:v,fullscreen:m,initialScreen:p,wrapper:g,dialog:b,contentItem:w,onClose:f,onOpen:S,footer:y,symbolTypes:E,searchInput:x,emptyState:k,hideMarkedListFlag:C,dialogWidth:M="auto",manager:_,shouldReturnFocus:A,onSymbolFiltersParamsChange:I,onEmptyResults:H}=e;if(i.dialogsOpenerManager.isOpened("SymbolSearch")||i.dialogsOpenerManager.isOpened("ChangeIntervalDialog"))return;const L=document.createElement("div"),T=o.createElement(l.SlotContext.Provider,{value:null!=_?_:null},o.createElement(a.SymbolSearchItemsDialog,{onClose:D,initialMode:t,defaultValue:r,
showSpreadActions:c,hideMarkedListFlag:C,selectSearchOnInit:h,onSearchComplete:d,dialogTitle:u,placeholder:v,fullscreen:m,initialScreen:p,wrapper:g,dialog:b,contentItem:w,footer:y,symbolTypes:E,searchInput:x,emptyState:k,autofocus:n,dialogWidth:M,shouldReturnFocus:A,onSymbolFiltersParamsChange:I,onEmptyResults:H}));function D(){s.unmountComponentAtNode(L),i.dialogsOpenerManager.setAsClosed("SymbolSearch"),f&&f()}return s.render(T,L),i.dialogsOpenerManager.setAsOpened("SymbolSearch"),S&&S(),{close:D}}},72539:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalSearchDialogRenderer:()=>Xe});var o=n(50959),s=n(962),l=n(98310),i=n(85067),a=n(12481),r=n(11542),c=n(56840),h=n(68335),d=n(1722),u=n(69654),v=n(97754),m=n(9745),p=n(26843),g=n(45345),b=n(67562),w=n(66619),f=n(82343);function S(e){const{text:t,showIcon:n=!0,className:s}=e,l=g.watchedTheme.value()===p.StdTheme.Dark?w:b;return o.createElement("div",{className:v(f.wrap,s)},n&&o.createElement(m.Icon,{icon:l,className:f.image}),o.createElement("span",{className:f.text},t))}var y=n(35057),E=n(63651),x=n(98715),k=n(32470),C=n(74786),M=n(40173),_=n(50267),A=n(10772),I=n(32108);const H=(0,M.mergeThemes)(_.DEFAUL_CONTEXT_MENU_ITEM_THEME,I);function L(e){const{action:t,isSelected:n,activeElRef:s,onExecute:l}=e;return o.createElement(A.ContextMenuAction,{theme:H,onShowSubMenu:C.default,isSubMenuOpened:!1,checkboxInput:!0,reference:s,selected:n,action:t,onExecute:l})}var T=n(95390);function D(e){const{heading:t,selectedId:n,items:s,activeElRef:l,onExecute:i}=e;return o.createElement("table",{className:T.section},o.createElement("tbody",null,o.createElement("tr",null,o.createElement("td",{className:T.heading},t)),s.map((e=>o.createElement(L,{key:e.id,action:e,isSelected:e.id===n,activeElRef:e.id===n?l:void 0,onExecute:i})))))}var N=n(61213);const P=[{name:"drawingsActions",label:r.t(null,void 0,n(22772))},{name:"functionActions",label:r.t(null,void 0,n(15327))},{name:"settingsActions",label:r.t(null,void 0,n(89517))}];function O(e){const{dialogId:t,items:s,onClose:l,shouldReturnFocus:i}=e,[v,m]=(0,o.useState)(""),[p,g]=(0,o.useState)([]),b=(0,o.useRef)(null),w=(0,o.useRef)(null),{activeIdx:f,setActiveIdx:C}=(0,E.useKeyboardNavigation)(b.current,p,(function(e,t){t.preventDefault();const n=p[f];n&&!n.isDisabled()&&(n.execute(),n.isCheckable()?_():l())}),"keyup");(0,k.useResetActiveIdx)(C,[v,s]),(0,x.useScrollToRef)(w,f),(0,o.useEffect)((()=>{var e;null===(e=b.current)||void 0===e||e.focus()}),[]),(0,o.useEffect)((()=>{const e=b.current;if(e)return e.addEventListener("input",L),L(),()=>{e&&e.removeEventListener("input",L)}}),[]);const M=(0,o.useCallback)((0,a.default)((e=>{}),1e3),[]),_=(0,o.useCallback)((0,a.default)(l,200),[]);(0,o.useEffect)((()=>()=>{M.flush(),_.cancel()}),[]);const A=(0,o.useMemo)((()=>{const e=new Set(c.getJSON("GlobalSearchDialog.recent",[])),t=[];for(const n of e){const e=s.find((e=>e.getState().id===n));e&&t.push(e)}return t.reverse(),t}),[]),I=(0,o.useMemo)((()=>P.reduce(((e,t)=>(e.set(t.name,p.filter((e=>e.getState().category===t.name))),e)),new Map)),[p])
;return o.createElement(y.AdaptivePopupDialog,{dataName:t,title:r.t(null,void 0,n(78842)),onClose:l,onClickOutside:l,shouldReturnFocus:i,render:()=>o.createElement(o.Fragment,null,o.createElement(u.DialogSearch,{reference:b}),o.createElement("div",{className:N.wrap},v?o.createElement(o.Fragment,null,p.length?P.map((e=>{const t=I.get(e.name);return t&&t.length?o.createElement(D,{key:e.name,heading:e.label,items:t,selectedId:T(),activeElRef:w,onExecute:H}):null})):o.createElement(S,{text:r.t(null,void 0,n(45850)),className:N.emptyState})):o.createElement(o.Fragment,null,Boolean(p.length)?o.createElement(D,{heading:r.t(null,void 0,n(90612)),selectedId:T(),activeElRef:w,items:p,onExecute:H}):o.createElement(S,{text:r.t(null,void 0,n(64185)),showIcon:!1,className:N.emptyState})))),className:N.dialog,onKeyDown:function(e){27===(0,h.hashFromEvent)(e)&&(e.preventDefault(),l())},isOpened:!0});function H(e){e.getState().checkable?_():l()}function L(){const e=b.current?b.current.value.toLocaleLowerCase().trim():"";if(m(e),e){const t=s.filter((t=>O(t).includes(e)||function(e,t){const{aliases:n}=t.getState();if(n)return n.some((t=>t.toLowerCase().includes(e)));return!1}(e,t))).sort((t=>O(t)===e?-1:0));g(t),t.length||M(e)}else g(A)}function T(){return-1!==f?p[f].id:null}function O(e){const{label:t}=e.getState();return(0,d.isString)(t)?t.toLocaleLowerCase():""}}var R=n(82992),z=n(54270),V=n(53180),B=n(10638),F=n(14483),Z=(n(92249),n(28853),n(39347));class G extends Z.Action{constructor({id:e,category:t,favourite:n,onFavouriteClick:o,hotkeyGroup:s,hotkeyHash:l,aliases:i,...a}){super({actionId:"UnknownAction",options:{...a,doNotCloseOnClick:!0},id:e}),this.execute=()=>{super.execute();const e=new Set(c.getJSON("GlobalSearchDialog.recent",[])),t=this._searchOptions.id;e.has(t)&&e.delete(t),e.add(t),c.setJSON("GlobalSearchDialog.recent",Array.from(e).slice(-10))},this.getState=()=>({...super.getState(),id:this._searchOptions.id,category:this._searchOptions.category,favourite:this._searchOptions.favourite,onFavouriteClick:this._onFavouriteClick,aliases:this._searchOptions.aliases}),this.update=e=>{this._searchOptions=Object.assign(this._searchOptions,e),super.update(e)},this._onFavouriteClick=e=>{this._searchOptions.onFavouriteClick&&(this.update({favourite:!this._searchOptions.favourite}),this._searchOptions.onFavouriteClick(e))},this._searchOptions={id:e,category:t,favourite:n,onFavouriteClick:o,aliases:i}}}var U,W=n(36298),X=n(11095);!function(e){e.None="all",e.Following="following",e.Private="private"}(U||(U={}));var J=n(49152),K=n(16410),j=n(45876),q=n(11014),Q=n(49483);function Y(e){const t=e.match(/^(\d+).(\d+).(\d+)/);if(!t)return null;const[,n,o,s]=t;return[parseInt(n),parseInt(o),parseInt(s)]}function $(e){const t=(0,Q.desktopAppVersion)();return!!t&&function(e,t){const n=Y(e),o=Y(t);if(!n||!o)return!1;const[s,l,i]=n,[a,r,c]=o;return s!==a?s<a:l!==r?l<r:i!==c&&i<c}(t,e)}const ee=e=>{const t=t=>{const o=[];if(t&&t.length&&window.is_authenticated&&t.forEach((t=>{o.push(new G({id:t,category:"settingsActions",
label:`${r.t(null,void 0,n(32409))} ${q.translateStdThemeName(t)}`,onExecute:()=>{q.loadTheme(e.chartWidgetCollection(),{themeName:t,standardTheme:!1}).then((()=>{e.readOnly()||window.saver.saveChartSilently()}))}}))})),!(0,Q.isDesktopApp)()||$("1.0.10")){const[t,s]=q.getStdThemeNames();o.push(new G({id:"DarkColorTheme",category:"settingsActions",label:r.t(null,void 0,n(66365)),checkable:!0,checked:q.getCurrentTheme().name===s,onExecute:()=>{const n=q.getCurrentTheme().name===s?t:s;q.loadTheme(e.chartWidgetCollection(),{themeName:n,standardTheme:!0}).then((()=>{e.readOnly()||window.saver.saveChartSilently()}))}}))}return o};return window.is_authenticated?q.getThemeNames().then(t):Promise.resolve(t())};var te=n(88348),ne=n(18540);n(46100);var oe=n(36147),se=n(87872),le=n(90995);const{DrawingSyncMode:ie}=te,ae={drawings:"ToggleHideAllDrawingTools",indicators:"ToggleHideAllIndicators",positions:"ToggleHideAllPositions",all:"ToggleHideAll"},re=new W.TranslatedString("stay in drawing mode",r.t(null,void 0,n(52010))),ce=new W.TranslatedString("sync drawings",r.t(null,void 0,n(95612))),he=r.t(null,void 0,n(49421)),de=r.t(null,void 0,n(35888)),ue=(r.t(null,void 0,n(77989)),r.t(null,void 0,n(19407)),r.t(null,void 0,n(37057))),ve=r.t(null,void 0,n(45265)),me=r.t(null,void 0,n(85422));var pe=n(5894),ge=n(64358),be=n(89324);class we extends G{constructor(e){super({label:r.t(null,void 0,n(82785)),id:"InvertScale",category:"settingsActions",checkable:!0,onExecute:()=>{this._model.invertPriceScale(this._model.mainSeries().priceScale())},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+73)}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().isInverted).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isInverted()}}class fe extends G{constructor(e){super({label:r.t(null,void 0,n(51102)),checkable:!0,id:"TogglePercantage",category:"settingsActions",onExecute:()=>{this.isChecked()?this._model.setPriceScaleRegularScaleMode(this._model.mainSeries().priceScale()):this._model.togglePriceScalePercentageScaleMode(this._model.mainSeries().priceScale())},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+80),disabled:e.mainSeries().priceScale().isLockScale()||6===e.mainSeries().properties().childs().style.value(),checked:e.mainSeries().priceScale().isPercentage()}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().percentage).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isPercentage()}}class Se extends G{constructor(e){super({label:r.t(null,void 0,n(12285)),id:"ToggleLogScale",category:"settingsActions",checkable:!0,onExecute:()=>{this.isChecked()?this._model.setPriceScaleRegularScaleMode(this._model.mainSeries().priceScale()):this._model.togglePriceScaleLogScaleMode(this._model.mainSeries().priceScale())},shortcutHint:(0,
h.humanReadableHash)(h.Modifiers.Alt+76),disabled:e.mainSeries().priceScale().isLockScale()||6===e.mainSeries().properties().childs().style.value(),checked:e.mainSeries().priceScale().isLog()}),this._model=e;(this._props=this._model.mainSeries().priceScale().properties().childs().log).subscribe(this,(()=>{this._onUpdate.fire(this)}))}destroy(){super.destroy(),this._props.unsubscribeAll(this)}isChecked(){return this._model.mainSeries().priceScale().isLog()}}const ye=F.enabled("show_average_close_price_line_and_label"),Ee=new W.TranslatedString("change session",r.t(null,void 0,n(65303))),xe=new W.TranslatedString("change plus button visibility",r.t(null,void 0,n(50190))),ke=new W.TranslatedString("change countdown to bar close visibility",r.t(null,void 0,n(58108))),Ce=new W.TranslatedString("scale price chart only",r.t(null,void 0,n(99042))),Me=new W.TranslatedString("change symbol last value visibility",r.t(null,void 0,n(53150))),_e=new W.TranslatedString("change high and low price labels visibility",r.t(null,void 0,n(66805))),Ae=new W.TranslatedString("change average close price label visibility",r.t(null,void 0,n(39402))),Ie=new W.TranslatedString("change indicators and financials value labels visibility",r.t(null,void 0,n(90512))),He=new W.TranslatedString("change indicators and financials name labels visibility",r.t(null,void 0,n(59820))),Le=new W.TranslatedString("change high and low price lines visibility",r.t(null,void 0,n(92556))),Te=new W.TranslatedString("change average close price line visibility",r.t(null,void 0,n(98866))),De=new W.TranslatedString("change symbol labels visibility",r.t(null,void 0,n(9402))),Ne=(new W.TranslatedString("change pre/post market price label visibility",r.t(null,void 0,n(49889))),new W.TranslatedString("change symbol previous close value visibility",r.t(null,void 0,n(12707))),new W.TranslatedString("change previous close price line visibility",r.t(null,void 0,n(59883)))),Pe=(new W.TranslatedString("change bid and ask labels visibility",r.t(null,void 0,n(5100))),new W.TranslatedString("change bid and ask lines visibility",r.t(null,void 0,n(32311))),new W.TranslatedString("change pre/post market price lines visibility",r.t(null,void 0,n(50393))),new W.TranslatedString("change price line visibility",r.t(null,void 0,n(67761)))),Oe=new W.TranslatedString("change session breaks visibility",r.t(null,void 0,n(15403))),Re=(new W.TranslatedString("change ideas visibility on chart",r.t(null,void 0,n(65558))),new W.TranslatedString("show all ideas",r.t(null,void 0,n(13622))),new W.TranslatedString("show ideas of followed users",r.t(null,void 0,n(26267))),new W.TranslatedString("show my ideas only",r.t(null,void 0,n(40061))),new W.TranslatedString("change events visibility on chart",r.t(null,void 0,n(79574))),new W.TranslatedString("change earnings visibility",r.t(null,void 0,n(88217))),new W.TranslatedString("change dividends visibility",r.t(null,void 0,n(84944))),new W.TranslatedString("change splits visibility",r.t(null,void 0,n(74488))),{0:r.t(null,void 0,n(97559)),
1:r.t(null,void 0,n(86771)),9:r.t(null,void 0,n(51383)),2:r.t(null,void 0,n(87691)),14:r.t(null,void 0,n(14956)),15:r.t(null,void 0,n(59393)),3:r.t(null,void 0,n(45290)),16:r.t(null,void 0,n(41412)),4:r.t(null,void 0,n(91664)),7:r.t(null,void 0,n(470)),5:r.t(null,void 0,n(90599)),6:r.t(null,void 0,n(59491)),8:r.t(null,void 0,n(20424)),10:r.t(null,void 0,n(18779)),11:r.t(null,void 0,n(38385)),12:r.t(null,void 0,n(28381)),13:r.t(null,void 0,n(20788)),17:r.t(null,void 0,n(82838))});async function ze(e){var t,o,s,l,i,a,c,d;const u=[],[v,m]=await Promise.all([ee(e),Promise.resolve(null)]),p=(e=>{const t=[],{stayInDrawingMode:n,drawOnAllCharts:o,drawOnAllChartsMode:s}=te.properties().childs();t.push(new G({label:he,checkable:!0,checked:n.value(),id:"ToggleStayInDrawingMode",category:"settingsActions",onExecute:()=>{e.model().setProperty(n,!n.value(),re)}})),t.push(new G({label:de,checkable:!0,id:"ToggleSyncDrawings",category:"settingsActions",checked:o.value(),disabled:!e.isMultipleLayout().value(),onExecute:()=>{e.model().setProperty(o,!o.value(),ce)}}));const l=te.lockDrawings();t.push(new G({label:ue,checkable:!0,id:"ToggleLockDrawings",category:"settingsActions",checked:l.value(),onExecute:()=>{te.lockDrawings().setValue(!te.lockDrawings().value())}}));const i=(0,le.getSavedHideMode)();t.push(...Array.from((0,le.getHideOptions)()).map((([e,t])=>new G({label:t.tooltip.inactive,checkable:!0,id:ae[e],category:"settingsActions",checked:i===e&&(0,le.getHideModeStateValue)(e),onExecute:()=>(0,le.toggleHideMode)(e)}))));const{magnet:a,magnetMode:r}=te.properties().childs();return t.push(new G({label:ve,checkable:!0,id:"WeakMagnet",category:"functionActions",checked:a.value()&&r.value()===oe.MagnetMode.WeakMagnet,icon:se.drawingToolsIcons.magnet,onExecute:()=>{a.value()&&r.value()===oe.MagnetMode.WeakMagnet?(0,ne.setIsMagnetEnabled)(!1):(0,ne.setMagnetMode)(oe.MagnetMode.WeakMagnet)}})),t.push(new G({label:me,checkable:!0,id:"StrongMagnet",category:"functionActions",checked:a.value()&&r.value()===oe.MagnetMode.StrongMagnet,icon:se.drawingToolsIcons.strongMagnet,onExecute:()=>{a.value()&&r.value()===oe.MagnetMode.StrongMagnet?(0,ne.setIsMagnetEnabled)(!1):(0,ne.setMagnetMode)(oe.MagnetMode.StrongMagnet)}})),t})(e),g=function(e){const t=[];return F.enabled("header_widget")&&F.enabled("header_compare")&&t.push(new G({icon:n(1393),label:(0,V.appendEllipsis)(r.t(null,void 0,n(90069))),id:"Compare",category:"functionActions",onExecute:()=>e.toggleCompareOrAdd()})),F.enabled("header_widget")&&F.enabled("header_indicators")&&t.push(new G({icon:n(39681),label:(0,V.appendEllipsis)(r.t(null,void 0,n(64642))),id:"InsertIndicator",category:"functionActions",onExecute:()=>{e.showIndicators([])},shortcutHint:e.options().indicatorsDialogShortcutEnabled?(0,h.humanReadableHash)(47):void 0})),F.enabled("show_object_tree")&&t.push(new G({icon:n(19908),label:r.t(null,void 0,n(55149)),id:"OpenObjectsTreeInRightPanel",category:"functionActions",onExecute:()=>e.showObjectsTreePanelOrDialog()})),
F.enabled("header_widget")&&F.enabled("header_settings")&&t.push(new G({label:(0,V.appendEllipsis)(r.t(null,void 0,n(89517))),icon:n(51983),id:"ChartProperties",category:"functionActions",onExecute:()=>{e.showGeneralChartProperties()}})),F.enabled("header_widget")&&F.enabled("header_symbol_search")&&t.push(new G({icon:n(69859),label:(0,V.appendEllipsis)(r.t(null,void 0,n(28089))),id:"ChangeSymbol",category:"functionActions",onExecute:()=>{(0,be.showDefaultSearchDialog)({defaultValue:"",trackResultsOptions:void 0})}})),F.enabled("symbol_info")&&t.push(new G({label:(0,V.appendEllipsis)(r.t(null,void 0,n(12014))),icon:n(37924),id:"SymbolInfo",category:"functionActions",onExecute:()=>{{const t=e.model().model(),n=t.mainSeries().symbolInfo(),o=t.availableUnits(),s={symbolInfo:n,showUnit:t.unitConversionEnabled(),unitDescription:e=>e?o.description(e):"",dateFormatter:t.dateFormatter()};return void(0,pe.showSymbolInfoDialog)(s)}}})),e.options().goToDateEnabled&&t.push(new G({label:(0,V.appendEllipsis)(r.t(null,void 0,n(40803))),icon:n(90752),id:"GoToDate",category:"functionActions",onExecute:()=>{(0,ge.showGoToDateDialog)(e)},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+71)})),t}(e);u.push(...v,...p,...g),m&&u.push(...m);const b=e.model().mainSeries(),w=b.priceScale(),f=b.properties().childs(),S=null===(o=(t=e.model()).paneForSource)||void 0===o?void 0:o.call(t,b);u.push(new G({id:"ResetPriceScale",category:"functionActions",label:r.t(null,void 0,n(45417)),icon:n(39267),onExecute:()=>{S&&e.model().resetPriceScale(S,w)},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+82)})),u.push(new we(e.model())),u.push(new fe(e.model())),u.push(new Se(e.model()));const y=w.isLockScale(),E=6===f.style.value();u.push(new G({label:r.t(null,void 0,n(31273)),checkable:!0,id:"SetRegularSessionId",category:"functionActions",disabled:Boolean("regular"===(null===(s=b.symbolInfo())||void 0===s?void 0:s.subsession_id)),onExecute:()=>{e.model().setProperty(f.sessionId,"regular",Ee)},checked:Boolean("regular"===(null===(l=b.symbolInfo())||void 0===l?void 0:l.subsession_id))})),u.push(new G({label:r.t(null,void 0,n(25790)),checkable:!0,id:"SetExtendedSessionId",category:"functionActions",disabled:!(null===(a=null===(i=b.symbolInfo())||void 0===i?void 0:i.subsessions)||void 0===a?void 0:a.some((e=>!e.private&&"extended"===e.id))),onExecute:()=>{var t;const n="extended"===(null===(t=b.symbolInfo())||void 0===t?void 0:t.subsession_id)?"regular":"extended";e.model().setProperty(f.sessionId,n,Ee)},checked:Boolean("extended"===(null===(c=b.symbolInfo())||void 0===c?void 0:c.subsession_id))})),u.push(new G({label:r.t(null,void 0,n(95667)),checkable:!0,id:"ToggleLockScale",category:"settingsActions",onExecute:()=>{e.model().togglePriceScaleLockScaleMode(e.model().mainSeries().priceScale())},checked:w.isLockScale()})),u.push(new G({label:r.t(null,void 0,n(20062)),checkable:!0,id:"ToggleIndexedTo100",category:"settingsActions",onExecute:()=>{
w.isIndexedTo100()?e.model().setPriceScaleRegularScaleMode(e.model().mainSeries().priceScale()):e.model().togglePriceScaleIndexedTo100ScaleMode(e.model().mainSeries().priceScale())},disabled:y||E,checked:w.isIndexedTo100()})),u.push(new G({id:"AutoFitsToScreen",category:"settingsActions",label:r.t(null,void 0,n(28020)),checkable:!0,onExecute:()=>{e.model().togglePriceScaleAutoScaleMode(w)},checked:w.isAutoScale(),disabled:w.properties().childs().autoScaleDisabled.value()})),u.push(new G({label:r.t(null,{context:"scale_menu"},n(72116)),checkable:!0,id:"ToggleRegularScale",category:"settingsActions",onExecute:()=>{e.model().setPriceScaleRegularScaleMode(w)},disabled:y||E||w.isRegular(),checked:w.isRegular()}));const x=e.model().model().priceScaleSlotsCount(),k=0===x.left;u.push(new G({label:k?r.t(null,void 0,n(19567)):r.t(null,void 0,n(76300)),id:"MoveScaleToSide",category:"functionActions",disabled:x.left+x.right!==1,onExecute:()=>{e.model().mergeAllScales(k?"left":"right")}})),u.push(new G({label:r.t(null,void 0,n(78633)),id:"MergeAllScalesToLeft",category:"functionActions",disabled:x.left+x.right===1,onExecute:()=>{e.model().mergeAllScales("left")}})),u.push(new G({label:r.t(null,void 0,n(308)),id:"MergeAllScalesToRight",category:"functionActions",disabled:x.left+x.right===1,onExecute:()=>{e.model().mergeAllScales("right")}})),u.push(new G({label:r.t(null,void 0,n(4037)),checkable:!0,checked:X.addPlusButtonProperty.value(),id:"ToggleAddOrderPlusButton",category:"settingsActions",onExecute:()=>{e.model().setProperty(X.addPlusButtonProperty,!X.addPlusButtonProperty.value(),xe)}}));const C=e.properties().childs().scalesProperties.childs(),M=f.showCountdown;u.push(new G({label:r.t(null,void 0,n(18511)),checkable:!0,id:"ToggleCountdown",category:"settingsActions",checked:M.value(),onExecute:()=>{e.model().setProperty(M,!M.value(),ke)}}));const _=C.scaleSeriesOnly;u.push(new G({label:r.t(null,void 0,n(35264)),checkable:!0,id:"ScalePriceChartOnly",category:"settingsActions",checked:_.value(),onExecute:()=>{e.model().setProperty(_,!_.value(),Ce)}}));const A=C.showSeriesLastValue;u.push(new G({label:r.t(null,void 0,n(78001)),checkable:!0,id:"ToggleSymbolLastValue",category:"settingsActions",checked:A.value(),onExecute:()=>{e.model().setProperty(A,!A.value(),Me)}}));const I=f.highLowAvgPrice.childs();u.push(new G({label:r.t(null,void 0,n(60259)),checkable:!0,id:"ToggleHighLowPriceLabels",category:"settingsActions",checked:I.highLowPriceLabelsVisible.value(),onExecute:()=>{e.model().setProperty(I.highLowPriceLabelsVisible,!I.highLowPriceLabelsVisible.value(),_e)}})),ye&&u.push(new G({label:r.t(null,void 0,n(8975)),checkable:!0,id:"ToggleAverageClosePriceLabel",category:"settingsActions",checked:I.averageClosePriceLabelVisible.value(),onExecute:()=>{const t=!I.averageClosePriceLabelVisible.value();e.model().setProperty(I.averageClosePriceLabelVisible,t,Ae)}}));const H=C.showSymbolLabels;u.push(new G({label:r.t(null,void 0,n(79791)),checkable:!0,id:"ToggleSymbolLabels",category:"settingsActions",checked:H.value(),onExecute:()=>{
e.model().setProperty(H,!H.value(),De)}}));const L=(0,J.combineProperty)(((e,t)=>e||t),C.showStudyLastValue.weakReference(),C.showFundamentalLastValue.weakReference());u.push(new G({label:r.t(null,void 0,n(81584)),checkable:!0,id:"ToggleStudyLastValue",category:"settingsActions",checked:L.value(),onExecute:()=>{const t=!L.value();e.model().beginUndoMacro(Ie),e.model().setProperty(C.showStudyLastValue,t,null),e.model().setProperty(C.showFundamentalLastValue,t,null),e.model().endUndoMacro()},onDestroy:()=>{L.destroy()}}));const T=(0,J.combineProperty)(((e,t)=>e||t),C.showStudyPlotLabels.weakReference(),C.showFundamentalNameLabel.weakReference());u.push(new G({label:r.t(null,void 0,n(31485)),checkable:!0,id:"ToggleIndicatorsLabels",category:"settingsActions",checked:T.value(),onExecute:()=>{e.model().beginUndoMacro(He);const t=!T.value();e.model().setProperty(C.showStudyPlotLabels,t,null),e.model().setProperty(C.showFundamentalNameLabel,t,null),e.model().endUndoMacro()},onDestroy:()=>{T.destroy()}})),u.push(new G({label:r.t(null,void 0,n(21803)),checkable:!0,id:"ToggleHighLowPriceLines",category:"settingsActions",checked:I.highLowPriceLinesVisible.value(),onExecute:()=>{e.model().setProperty(I.highLowPriceLinesVisible,!I.highLowPriceLinesVisible.value(),Le)}})),ye&&u.push(new G({label:r.t(null,void 0,n(87899)),checkable:!0,id:"ToggleAverageClosePriceLine",category:"settingsActions",checked:I.averageClosePriceLineVisible.value(),onExecute:()=>{const t=!I.averageClosePriceLineVisible.value();e.model().setProperty(I.averageClosePriceLineVisible,t,Te)}}));const D=f.showPriceLine;u.push(new G({label:r.t(null,void 0,n(99530)),checkable:!0,id:"TogglePriceLine",category:"settingsActions",checked:D.value(),onExecute:()=>{e.model().setProperty(D,!D.value(),Pe)}}));const N=f.showPrevClosePriceLine;u.push(new G({label:r.t(null,void 0,n(96032)),checkable:!0,id:"ToggleSymbolPrevCloseLine",disabled:e.model().mainSeries().isDWM(),category:"settingsActions",checked:N.value(),onExecute:()=>{e.model().setProperty(N,!N.value(),Ne)}})),u.push(new G({label:r.t(null,void 0,n(75521)),icon:n(39267),id:"ResetTimeScale",category:"functionActions",onExecute:()=>{e.model().resetTimeScale()},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Mod+h.Modifiers.Alt+81)}));const P=e.model().model().sessions().properties().childs().graphics.childs().vertlines.childs().sessBreaks.childs().visible;if(u.push(new G({label:r.t(null,void 0,n(90417)),checkable:!0,id:"ToggleSessionBreaks",category:"settingsActions",disabled:e.model().mainSeries().isDWM(),checked:P.value(),onExecute:()=>{e.model().setProperty(P,!P.value(),Oe)}})),u.push(new G({label:r.t(null,void 0,n(34465)),icon:n(39267),id:"ResetChart",category:"functionActions",onExecute:()=>e.GUIResetScales(),shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+82)})),u.push(new G({icon:n(35149),label:r.t(null,void 0,n(20378)),id:"RemoveAllIndicators",category:"functionActions",onExecute:()=>e.removeAllStudies()})),u.push(new G({icon:n(35149),label:r.t(null,void 0,n(76091)),id:"RemoveAllDrawingTools",
category:"functionActions",onExecute:()=>e.removeAllDrawingTools()})),u.push(new G({icon:n(35149),label:r.t(null,void 0,n(57869)),id:"RemoveAllIndicatorsAndDrawingTools",category:"functionActions",onExecute:()=>e.removeAllStudiesDrawingTools()})),u.push(new G({label:r.t(null,void 0,n(95480)),id:"ApplyIndicatorsToAllCharts",category:"functionActions",disabled:!e.applyIndicatorsToAllChartsAvailable(),onExecute:()=>{e.chartWidgetCollection().applyIndicatorsToAllCharts(e)}})),F.enabled("header_widget")&&F.enabled("header_undo_redo")&&(u.push(new G({id:"Undo",category:"functionActions",icon:n(77665),label:r.t(null,void 0,n(81320)),onExecute:()=>{e.model().undoHistory().undo()},disabled:e.model().undoHistory().undoStack().isEmpty(),shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Mod+90)})),u.push(new G({id:"Redo",category:"functionActions",icon:n(96052),label:r.t(null,void 0,n(41615)),onExecute:()=>{e.model().undoHistory().redo()},disabled:e.model().undoHistory().redoStack().isEmpty(),shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Mod+89)}))),u.push(new G({label:r.t(null,void 0,n(22221)),id:"MoveChartRight",category:"functionActions",disabled:!e.chartWidgetCollection().activeChartCanBeMoved(),onExecute:()=>{e.chartWidgetCollection().moveActiveChartWithUndo(!1)}})),u.push(new G({label:r.t(null,void 0,n(56854)),id:"MoveChartLeft",category:"functionActions",disabled:!e.chartWidgetCollection().activeChartCanBeMoved(),onExecute:()=>{e.chartWidgetCollection().moveActiveChartWithUndo(!0)}})),F.enabled("header_widget")&&F.enabled("header_chart_type")){const t=(0,K.allChartStyles)();for(const n of t)u.push(new G({id:`ChartStyle_${n}`,category:"functionActions",disabled:!(null===(d=R.linking.supportedChartStyles.value())||void 0===d?void 0:d.includes(n)),onExecute:()=>{e.chartWidgetCollection().setChartStyleToWidget(n)},icon:j.SERIES_ICONS[n],label:Re[n]}))}return F.enabled("header_widget")&&F.enabled("header_fullscreen_button")&&u.push(new G({label:r.t(null,void 0,n(11682)),id:"Fullscreen mode",icon:n(49697),category:"functionActions",checkable:!0,checked:e.chartWidgetCollection().fullscreen().value(),disabled:!e.chartWidgetCollection().fullscreenable().value(),onExecute:()=>{const t=e.chartWidgetCollection();t.fullscreen().value()?t.exitFullscreen():t.startFullscreen()},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Shift+70)})),u}n(50151);var Ve=n(71810),Be=n(78036),Fe=n(80982);function Ze(){return Fe.lineToolsFlat.map((e=>function(e){var t;const o=Be.lineToolsInfo[e],s=null===(t=o.selectHotkey)||void 0===t?void 0:t.hash,l={id:e,category:"drawingsActions",label:o.localizedName,icon:o.icon,shortcutHint:s?(0,h.humanReadableHash)(s):void 0,payload:e,onExecute:()=>te.tool.setValue(e),favourite:Ve.LinetoolsFavoritesStore.isFavorite(e),onFavouriteClick:t=>{t.preventDefault(),Ve.LinetoolsFavoritesStore.isFavorite(e)?Ve.LinetoolsFavoritesStore.removeFavorite(e):Ve.LinetoolsFavoritesStore.addFavorite(e)}};return e.toLowerCase().includes("fib")&&(l.aliases=[r.t(null,void 0,n(22305))]),new G(l)}(e.name)))}var Ge=n(37404);n(3343)
;function Ue(e,t){const n=e.getState().category,o=t.getState().category;return n===o?0:"drawingsActions"===o?1:"drawingsActions"===n||"functionActions"===n?-1:1}var We=n(16216);class Xe extends i.DialogRenderer{constructor(){super(),this._actions=[],this.show=e=>{(async function(e){const t=[],[o,s,l,i,a]=await Promise.all([ze(e),Promise.resolve(null),Promise.resolve(null),Promise.resolve(null),Promise.resolve(null)]);t.push(...o);const c=e.chartWidgetCollection();if(F.enabled("header_widget")&&F.enabled("header_resolutions")){const o={label:(0,V.appendEllipsis)(r.t(null,void 0,n(8353))),id:"ChangeInterval",category:"functionActions",onExecute:()=>{(0,z.showChangeIntervalDialogAsync)({initVal:R.linking.interval.value(),selectOnInit:!0})}};!F.enabled("show_interval_dialog_on_key_press")||e.readOnly()||e.options().hideSymbolSearch||(o.shortcutHint=(0,h.humanReadableHash)(188)),t.push(new G(o))}if(F.enabled("header_widget")&&F.enabled("header_saveload")){const o=new B.LoadChartService(c);t.push(new G({id:"LoadChartLayout",category:"functionActions",label:(0,V.appendEllipsis)(r.t(null,void 0,n(75687))),onExecute:()=>{o.showLoadDialog()},shortcutHint:r.t(null,{context:"hotkey"},n(14229))}));const s=e.getSaveChartService();s&&(t.push(new G({id:"RenameChartLayout",category:"functionActions",label:(0,V.appendEllipsis)(r.t(null,void 0,n(4142))),onExecute:()=>{s.renameChart()}})),t.push(new G({id:"SaveChartLayout",category:"functionActions",icon:n(53707),label:(0,V.appendEllipsis)(r.t(null,void 0,n(62571))),disabled:!s.hasChanges(),onExecute:()=>{s.saveChartOrShowTitleDialog()},shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Mod+83)})))}return t.push(new G({id:"TakeSnapshot",category:"functionActions",icon:n(72644),label:r.t(null,void 0,n(15803)),onExecute:()=>c.takeServerScreenshot(),shortcutHint:(0,h.humanReadableHash)(h.Modifiers.Alt+83)})),t})(this._activeChartWidget).then((t=>{this._actions=t.concat((()=>{const e=new G({id:"ManageLayoutDrawings",category:"functionActions",icon:n(81111),label:(0,V.appendEllipsis)(r.t(null,void 0,n(72357))),onExecute:()=>(0,Ge.showManageDrawingsDialog)()});return F.enabled("left_toolbar")?[...Ze(),e]:[]})()).sort(Ue),s.render(o.createElement(O,{shouldReturnFocus:null==e?void 0:e.shouldReturnFocus,dialogId:"globalSearch",items:this._actions,onClose:this.hide}),this._container),this._setVisibility(!0)}))},this.hide=()=>{s.unmountComponentAtNode(this._container),this._setVisibility(!1);for(const e of this._actions)e.destroy()};const e=(0,We.service)(l.CHART_WIDGET_COLLECTION_SERVICE);this._activeChartWidget=e.activeChartWidget.value()}}},63651:(e,t,n)=>{"use strict";n.d(t,{useKeyboardNavigation:()=>l});var o=n(50959),s=n(68335);function l(e,t,n,l="keydown"){const[i,a]=(0,o.useState)(-1);return(0,o.useEffect)((()=>{if(!e)return;const n=e=>{switch((0,s.hashFromEvent)(e)){case 40:if(i===t.length-1)break;e.preventDefault(),a(i+1);break;case 38:if(i<=0)break;e.preventDefault(),a(i-1);break}};return e.addEventListener("keydown",n),()=>{e.removeEventListener("keydown",n)}}),[e,i,t]),(0,
o.useEffect)((()=>{if(!e||!n)return;const o=e=>{var o;e.repeat||13===(0,s.hashFromEvent)(e)&&n(null!==(o=t[i])&&void 0!==o?o:null,e)};return e.addEventListener(l,o),()=>{e.removeEventListener(l,o)}}),[e,i,t,n,l]),{activeIdx:i,setActiveIdx:a}}},32470:(e,t,n)=>{"use strict";n.d(t,{useResetActiveIdx:()=>s});var o=n(50959);function s(e,t=[]){(0,o.useEffect)((()=>{e(-1)}),[...t])}},98715:(e,t,n)=>{"use strict";n.d(t,{useScrollToRef:()=>s});var o=n(50959);function s(e,t){(0,o.useEffect)((()=>{var n;t>=0&&(null===(n=e.current)||void 0===n||n.scrollIntoView({block:"nearest"}))}),[t])}},37404:(e,t,n)=>{"use strict";n.d(t,{showManageDrawingsDialog:()=>s});let o=null;function s(e){return Promise.all([n.e(4781),n.e(9465),n.e(7413),n.e(3263),n.e(1702)]).then(n.bind(n,41662)).then((t=>{const n=new(0,t.ManageDrawingsDialogRenderer)(e);return null!==o&&o.hide(),n.show(),o=n,n}))}},51826:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>o,dialogsOpenerManager:()=>s});class o{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const s=new o},10772:(e,t,n)=>{"use strict";n.d(t,{ContextMenuAction:()=>y});var o=n(50959),s=n(50151),l=n(91561),i=n(59064),a=n(51768),r=n(38223);var c=n(83021),h=n(97754),d=n.n(h),u=n(26996),v=n(50267),m=n(51331);function p(e){const{size:t="normal"}=e;return o.createElement(v.ContextMenuItem,{size:t,jsxLabel:o.createElement("div",{className:d()(m.loaderWrap,m[t])},o.createElement(u.Loader,{className:m.loader})),noInteractive:!0,onMouseOver:e.onMouseOver})}var g=n(3343),b=n(50238),w=n(16838),f=n(44445);const S=(0,o.forwardRef)((function(e,t){const{className:n,...s}=e,[l,i]=(0,b.useRovingTabindexElement)(t);return o.createElement(v.ContextMenuItem,{...s,className:d()(w.PLATFORM_ACCESSIBILITY_ENABLED&&f.accessible,n),reference:l,tabIndex:i,onKeyDown:function(e){if(!w.PLATFORM_ACCESSIBILITY_ENABLED||e.target!==e.currentTarget)return;const t=(0,g.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),l.current instanceof HTMLElement&&l.current.click())},"data-role":w.PLATFORM_ACCESSIBILITY_ENABLED?"menuitem":void 0,"aria-disabled":w.PLATFORM_ACCESSIBILITY_ENABLED&&e.disabled||void 0})}));class y extends o.PureComponent{constructor(e){super(e),this._itemRef=null,this._menuElementRef=o.createRef(),this._menuRef=null,this._handleClick=e=>{e.isDefaultPrevented()||this.state.disabled||(this._hasSubItems()?this._showSubMenu():(this.state.doNotCloseOnClick||(0,i.globalCloseMenu)(),this.props.action.execute(),this._trackEvent(),this.props.onExecute&&this.props.onExecute(this.props.action)))},this._handleClickToolbox=()=>{(0,i.globalCloseMenu)()},this._handleItemMouseOver=()=>{this._showSubMenu(),this._setCurrentContextValue()},this._handleMenuMouseOver=()=>{this._setCurrentContextValue()},this._showSubMenu=()=>{this.props.onShowSubMenu(this.props.action)},this._calcSubMenuPos=e=>function(e,t,n={x:0,y:10}){if(t){
const{left:n,right:o,top:s}=t.getBoundingClientRect(),l=document.documentElement.clientWidth,i={x:n-e,y:s},a={x:o,y:s};return(0,r.isRtl)()?n<=e?a:i:l-o>=e?a:i}return n}(e.contentWidth,this._itemRef),this._updateState=e=>{this.setState(e.getState())},this._setItemRef=e=>{this._itemRef=e},this._handleMenuRef=e=>{this._menuRef=e},this._registerSubmenu=()=>{var e;return null===(e=this.context)||void 0===e?void 0:e.registerSubmenu(this.props.action.id,(e=>(0,s.ensureNotNull)(this._itemRef).contains(e)||null!==this._menuElementRef.current&&this._menuElementRef.current.contains(e)))},this.state={...this.props.action.getState()}}componentDidMount(){this.props.action.onUpdate().subscribe(this,this._updateState),this.state.subItems.length&&(this._unsubscribe=this._registerSubmenu()),this.props.reference&&(this._itemRef=this.props.reference.current)}componentDidUpdate(e,t){var n,o,s;t.loading!==this.state.loading&&(null===(o=(n=this.props).onRequestUpdate)||void 0===o||o.call(n)),0===t.subItems.length&&this.state.subItems.length>0&&(this._unsubscribe=this._registerSubmenu()),t.subItems.length>0&&0===this.state.subItems.length&&(null===(s=this._unsubscribe)||void 0===s||s.call(this)),t.subItems!==this.state.subItems&&null!==this._menuRef&&this._menuRef.update()}componentWillUnmount(){this.props.action.onUpdate().unsubscribe(this,this._updateState),this._unsubscribe&&this._unsubscribe()}render(){var e,t;const n=(null===(e=this.context)||void 0===e?void 0:e.current)?this.context.current===this.props.action.id:this.props.isSubMenuOpened;return this.state.loading?o.createElement(p,{size:this.state.size}):o.createElement(S,{theme:this.props.theme,ref:null!==(t=this.props.reference)&&void 0!==t?t:this._setItemRef,onClick:this._handleClick,onClickToolbox:this._handleClickToolbox,onMouseOver:this._handleItemMouseOver,hovered:n,hasSubItems:this._hasSubItems(),actionName:this.state.name,checkboxInput:this.props.checkboxInput,selected:this.props.selected,...this.state},o.createElement(l.ContextMenu,{isOpened:n,items:this.state.subItems,position:this._calcSubMenuPos,menuStatName:this.props.menuStatName,parentStatName:this._getStatName(),menuElementReference:this._menuElementRef,onMouseOver:this.state.subItems.length?this._handleMenuMouseOver:void 0,ref:this._handleMenuRef}))}_setCurrentContextValue(){var e;this.state.subItems.length&&(null===(e=this.context)||void 0===e||e.setCurrent(this.props.action.id))}_hasSubItems(){return this.state.subItems.length>0}_trackEvent(){const e=this._getStatName();(0,a.trackEvent)("ContextMenuClick",this.props.menuStatName||"",e)}_getStatName(){return[this.props.parentStatName,this.state.statName].filter((e=>Boolean(e))).join(".")}}y.contextType=c.SubmenuContext},50267:(e,t,n)=>{"use strict";n.d(t,{ContextMenuItem:()=>f,DEFAUL_CONTEXT_MENU_ITEM_THEME:()=>b});var o=n(50959),s=n(97754),l=n(70673),i=n(49483),a=n(32563);var r=n(96040),c=n(36189),h=n(99025),d=n(25812),u=n(14483),v=n(90186),m=n(80802),p=n(14665),g=n(22436);const b=g,w=u.enabled("items_favoriting");class f extends o.PureComponent{constructor(){
super(...arguments),this._handleMouseOver=e=>{(function(e){const t=e.sourceCapabilities;let n=t&&t.firesTouchEvents;return void 0===n&&(n=a.touch),n})(e.nativeEvent)||this.props.onMouseOver&&this.props.onMouseOver()},this._handleClickToolbox=e=>{e.stopPropagation(),this.props.onClickToolbox&&this.props.onClickToolbox()}}render(){const{hasSubItems:e,shortcutHint:t,hint:n,invisibleHotkey:l,favourite:a,theme:r=g,size:d="normal",onKeyDown:u,label:m,jsxLabel:b,styledLabel:f}=this.props,S=this.props.checkable&&this.props.checkboxInput?"label":"div";return o.createElement(o.Fragment,null,o.createElement("tr",{...(0,v.filterDataProps)(this.props),...(0,v.filterAriaProps)(this.props),className:s(this.props.className,r.item,!this.props.noInteractive&&r.interactive,this.props.hovered&&r.hovered,this.props.disabled&&r.disabled,this.props.active&&r.active,this.props.selected&&r.selected,r[d]),onClick:this.props.onClick,onMouseOver:this._handleMouseOver,ref:this.props.reference,"data-action-name":this.props.actionName,tabIndex:this.props.tabIndex,onKeyDown:u},w&&void 0!==a&&o.createElement("td",null,o.createElement(c.FavoriteButton,{className:r.favourite,isFilled:a,onClick:this.props.onFavouriteClick})),o.createElement("td",{className:s(r.iconCell),"data-icon-cell":!0},this._icon(r)),o.createElement("td",{className:r.contentCell},o.createElement(S,{className:r.content},o.createElement("span",{className:s(r.label,this.props.checked&&r.checked),"data-label":!0},!b&&f?f.map((({text:e,...t},n)=>o.createElement("span",{key:n,style:t},e))):null!=b?b:m),this._toolbox(r),e&&o.createElement("span",{className:r.arrowIcon,dangerouslySetInnerHTML:{__html:p},"data-submenu-arrow":!0}),!e&&t&&!i.CheckMobile.any()&&o.createElement(h.Hint,{className:s(l&&r.invisibleHotkey),text:t}),!e&&!t&&n&&o.createElement(h.Hint,{text:n})))),o.createElement("tr",{className:r.subMenu},o.createElement("td",null,this.props.children)))}_icon(e){if(this.props.checkable){if(this.props.checkboxInput)return o.createElement(l.CheckboxInput,{className:s(e.icon,e.checkboxInput),checked:this.props.checked});if(this.props.checked){const t=!this.props.icon&&!this.props.iconChecked,n=this.props.iconChecked||this.props.icon||m;return o.createElement("span",{className:s(e.icon,t&&e.checkmark),dangerouslySetInnerHTML:{__html:n},"data-icon-checkmark":t})}return this.props.icon?o.createElement("span",{className:e.icon,dangerouslySetInnerHTML:{__html:this.props.icon}}):o.createElement("span",{className:e.icon})}return this.props.icon?o.createElement("span",{className:e.icon,dangerouslySetInnerHTML:{__html:this.props.icon}}):null}_toolbox(e){return this.props.toolbox?o.createElement("span",{className:s(e.toolbox,this.props.showToolboxOnHover&&e.showToolboxOnHover),onClick:this._handleClickToolbox,"data-toolbox":!0},this._renderToolboxContent()):null}_renderToolboxContent(){return this.props.toolbox&&this.props.toolbox.type===d.ToolboxType.Delete?o.createElement(r.RemoveButton,{onClick:this.props.toolbox.action}):null}}},91561:(e,t,n)=>{"use strict";n.d(t,{ContextMenu:()=>M,
OverlapContextMenu:()=>_});var o=n(50959),s=n(97754),l=n.n(s),i=n(86431),a=n(27317),r=n(52778);class c extends o.PureComponent{constructor(){super(...arguments),this._handleKeyDown=e=>{e.keyCode===this.props.keyCode&&this.props.handler(e)}}componentDidMount(){document.addEventListener(this.props.eventType||"keydown",this._handleKeyDown,!1)}componentWillUnmount(){document.removeEventListener(this.props.eventType||"keydown",this._handleKeyDown,!1)}render(){return null}}var h=n(28127),d=n(37558),u=n(90692),v=n(33927);function m(e){return o.createElement("li",{className:v.separator})}var p=n(23829),g=n(41590),b=n(59064);function w(e){var t;const n=null!==(t=e.action.custom())&&void 0!==t?t:e.action,[s,l]=(0,o.useState)((()=>n.getState())),[i,a]=(0,o.useState)(!1),r=!!s.subItems.length,c=r&&i;return(0,o.useEffect)((()=>{const e=()=>l(n.getState());return n.onUpdate().subscribe(null,e),()=>{n.onUpdate().unsubscribe(null,e)}}),[]),o.createElement(p.ContextMenuItem,{...s,onClick:function(e){if(s.disabled||e.defaultPrevented)return;if(r)return void a(!0);s.doNotCloseOnClick||(0,b.globalCloseMenu)();n.execute()},isLoading:s.loading,isHovered:c},c&&o.createElement(g.Drawer,{onClose:h},o.createElement(y,{items:s.subItems,parentAction:n,closeNested:h})));function h(e){e&&e.preventDefault(),a(!1)}}var f=n(54627),S=n(66493);function y(e){const{items:t,parentAction:n,closeNested:s}=e,l=!Boolean(n)&&t.every((e=>!Boolean("separator"!==e.type&&(e.getState().icon||e.getState().checkable))));return o.createElement(f.EmptyIconsContext.Provider,{value:l},o.createElement("ul",null,n&&o.createElement(o.Fragment,null,o.createElement(p.ContextMenuItem,{label:n.getState().label,isTitle:!0,active:!1,disabled:!1,subItems:[],checkable:!1,checked:!1,doNotCloseOnClick:!1,icon:S,onClick:s}),o.createElement(m,null)),t.map((e=>{switch(e.type){case"action":return o.createElement(w,{key:e.id,action:e});case"separator":return o.createElement(m,{key:e.id})}}))))}const E=o.createContext(null);var x=n(81261),k=n(16838),C=n(36002);class M extends o.PureComponent{constructor(e){super(e),this._menuRef=o.createRef(),this._handleRequestUpdate=()=>{this.update()},this._handleClose=()=>{this.props.onClose&&this.props.onClose()},this._handleOutsideClickClose=e=>{const{doNotCloseOn:t,onClose:n}=this.props;!n||void 0!==t&&t.contains(e.target)||n()},this._handleFocusOnOpen=()=>{var e,t;(null===(e=this.props.menuElementReference)||void 0===e?void 0:e.current)&&this.props.takeFocus&&(null===(t=this.props.menuElementReference)||void 0===t||t.current.focus({preventScroll:!0}))},this._handleFocus=e=>{this.props.isKeyboardEvent&&e.target&&k.PLATFORM_ACCESSIBILITY_ENABLED&&(0,x.focusFirstMenuItem)(e.target)},this.state={}}render(){const{isOpened:e,onClose:t,items:n,doNotCloseOn:s,menuStatName:i,parentStatName:v,takeFocus:m,...p}=this.props;return e?o.createElement(d.DrawerManager,null,o.createElement(c,{keyCode:27,eventType:"keyup",handler:this._handleClose}),o.createElement(u.MatchMedia,{rule:"screen and (max-width: 430px)"},(t=>this._isDrawer(t)?o.createElement(E.Provider,{
value:{type:"drawer"}},o.createElement(g.Drawer,{onClose:this._handleClose,position:"Bottom","data-name":p["data-name"]},o.createElement(y,{items:n}))):o.createElement(E.Provider,{value:{type:"menu"}},o.createElement(r.OutsideEvent,{handler:this._handleOutsideClickClose,mouseDown:!0,touchStart:!0,reference:this.props.menuElementReference},(t=>o.createElement(a.Menu,{...p,reference:t,className:l()(C.menu,"context-menu"),onClose:this._handleClose,noMomentumBasedScroll:!0,ref:this._menuRef,tabIndex:m?-1:void 0,onOpen:this._handleFocusOnOpen,onFocus:this._handleFocus,onKeyDown:x.handleAccessibleMenuKeyDown},o.createElement(h.ActionsTable,{items:n,menuStatName:i,parentStatName:v,parentIsOpened:e,onRequestUpdate:this._handleRequestUpdate})))))))):null}update(){var e;this._menuRef.current&&this._menuRef.current.update(),this.props.isKeyboardEvent&&(null===(e=this.props.menuElementReference)||void 0===e?void 0:e.current)&&document.activeElement===this.props.menuElementReference.current&&(0,x.focusFirstMenuItem)(this.props.menuElementReference.current)}_isDrawer(e){return void 0===this.props.mode?e:"drawer"===this.props.mode}}const _=(0,i.makeOverlapable)(M)},99025:(e,t,n)=>{"use strict";n.d(t,{Hint:()=>a});var o=n(50959),s=n(97754),l=n.n(s),i=n(22436);function a(e){const{text:t="",className:n}=e;return o.createElement("span",{className:l()(i.shortcut,n)},t)}},23829:(e,t,n)=>{"use strict";n.d(t,{ContextMenuItem:()=>m});var o=n(50959),s=n(97754),l=n.n(s),i=n(9745),a=n(26996),r=n(54627),c=n(99025),h=n(39750),d=n(79978),u=n(69311),v=n(29122);function m(e){const{className:t,isTitle:n,isLoading:s,isHovered:m,active:p,checkable:g,disabled:b,checked:w,icon:f,iconChecked:S,hint:y,subItems:E,label:x,styledLabel:k,onClick:C,children:M,toolbox:_,jsxLabel:A,size:I="normal"}=e,H=(0,o.useContext)(r.EmptyIconsContext),L=!!E.length;return s?o.createElement("li",{className:l()(t,v.item,v.loading,v[I])},o.createElement(a.Loader,null)):o.createElement("li",{className:l()(t,v.item,v.interactive,n&&v.title,b&&v.disabled,m&&v.hovered,p&&v.active,H&&v.emptyIcons,v[I]),onClick:C},o.createElement(i.Icon,{className:l()(v.icon),icon:function(){if(g&&w)return S||f||h;return f}()}),o.createElement("span",{className:l()(v.label)},!A&&k?k.map((({text:e,...t},n)=>o.createElement("span",{key:n,style:t},e))):null!=A?A:x),!!_&&o.createElement(i.Icon,{onClick:function(){_&&_.action()},className:v.remove,icon:u}),!L&&y&&o.createElement(c.Hint,{className:v.shortcut,text:y}),L&&o.createElement(i.Icon,{className:v.nested,icon:d}),M)}},54627:(e,t,n)=>{"use strict";n.d(t,{EmptyIconsContext:()=>o});const o=n(50959).createContext(!1)},37558:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>i,DrawerManager:()=>l});var o=n(50959),s=n(99054);class l extends o.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,
s.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,s.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,s.setFixedBodyState)(!1)}render(){return o.createElement(i.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const i=o.createContext(null)},41590:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>u});var o=n(50959),s=n(50151),l=n(97754),i=n(36174),a=n(42842),r=n(37558),c=n(29197),h=n(86656),d=n(66076);function u(e){const{position:t="Bottom",onClose:n,children:h,className:u,theme:m=d}=e,p=(0,s.ensureNotNull)((0,o.useContext)(r.DrawerContext)),[g]=(0,o.useState)((()=>(0,i.randomHash)())),b=(0,o.useRef)(null),w=(0,o.useContext)(c.CloseDelegateContext);return(0,o.useLayoutEffect)((()=>((0,s.ensureNotNull)(b.current).focus({preventScroll:!0}),w.subscribe(p,n),p.addDrawer(g),()=>{p.removeDrawer(g),w.unsubscribe(p,n)})),[]),o.createElement(a.Portal,null,o.createElement("div",{className:l(d.wrap,d[`position${t}`])},g===p.currentDrawer&&o.createElement("div",{className:d.backdrop,onClick:n}),o.createElement(v,{className:l(m.drawer,d[`position${t}`],u),ref:b,"data-name":e["data-name"]},h)))}const v=(0,o.forwardRef)(((e,t)=>{const{className:n,...s}=e;return o.createElement(h.TouchScrollContainer,{className:l(d.drawer,n),tabIndex:-1,ref:t,...s})}))},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var o=n(11542),s=n(50959),l=n(97754),i=n(9745),a=n(33765),r=n(27306);function c(e){const{className:t,isActive:c,onClick:h,onMouseDown:d,title:u,hidden:v,"data-name":m="remove-button",...p}=e;return s.createElement(i.Icon,{...p,"data-name":m,className:l(r.button,"apply-common-tooltip",c&&r.active,v&&r.hidden,t),icon:a,onClick:h,onMouseDown:d,title:u||o.t(null,void 0,n(34596))})}},40173:(e,t,n)=>{"use strict";function o(e,t,n={}){return Object.assign({},e,function(e,t,n={}){const o=Object.assign({},t);for(const s of Object.keys(t)){const l=n[s]||s;l in e&&(o[s]=[e[l],t[s]].join(" "))}return o}(e,t,n))}n.d(t,{mergeThemes:()=>o})},81261:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>c,handleAccessibleMenuFocus:()=>a,handleAccessibleMenuKeyDown:()=>r,queryMenuElements:()=>u});var o=n(16838),s=n(71468),l=n(68335);const i=[37,39,38,40];function a(e,t){e.target&&o.PLATFORM_ACCESSIBILITY_ENABLED&&e.relatedTarget===t.current&&c(e.target)}function r(e){var t;if(!o.PLATFORM_ACCESSIBILITY_ENABLED)return;if(e.defaultPrevented)return;const n=(0,l.hashFromEvent)(e);if(!i.includes(n))return;const a=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const r=u(e.currentTarget).sort(o.navigationOrderComparator);if(0===r.length)return;const c=document.activeElement.closest('[data-role="menuitem"]')||(null===(t=document.activeElement.parentElement)||void 0===t?void 0:t.querySelector('[data-role="menuitem"]'));if(!(c instanceof HTMLElement))return
;const m=r.indexOf(c);if(-1===m)return;const p=v(c),g=p.indexOf(document.activeElement),b=-1!==g,w=e=>{a&&(0,s.becomeSecondaryElement)(a),(0,s.becomeMainElement)(e),e.focus()};switch((0,o.mapKeyCodeToDirection)(n)){case"inlinePrev":if(!p.length)return;e.preventDefault(),w(0===g?r[m]:b?h(p,g,-1):p[p.length-1]);break;case"inlineNext":if(!p.length)return;e.preventDefault(),g===p.length-1?w(r[m]):w(b?h(p,g,1):p[0]);break;case"blockPrev":{e.preventDefault();const t=h(r,m,-1);if(b){const e=d(t,g);w(e||t);break}w(t);break}case"blockNext":{e.preventDefault();const t=h(r,m,1);if(b){const e=d(t,g);w(e||t);break}w(t)}}}function c(e){const[t]=u(e);t&&((0,s.becomeMainElement)(t),t.focus())}function h(e,t,n){return e[(t+e.length+n)%e.length]}function d(e,t){const n=v(e);return n.length?n[(t+n.length)%n.length]:null}function u(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,o.createScopedVisibleElementFilter)(e))}function v(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,o.createScopedVisibleElementFilter)(e))}},28127:(e,t,n)=>{"use strict";n.d(t,{ActionsTable:()=>a});var o=n(50959),s=n(86838);function l(e){return o.createElement("tr",{className:s.row},o.createElement("td",null,o.createElement("div",{className:s.line})),o.createElement("td",null,o.createElement("div",{className:s.line}),e.hint?o.createElement("div",{className:s.hint},e.hint):null))}var i=n(10772);class a extends o.PureComponent{constructor(e){super(e),this._handleShowSubMenu=e=>{const t=e.getState();this.setState({showSubMenuOf:t.subItems.length?e:void 0})},this.state={}}render(){return o.createElement("table",null,o.createElement("tbody",null,this.props.items.map((e=>this._item(e)))))}static getDerivedStateFromProps(e,t){return!e.parentIsOpened&&t.showSubMenuOf?{showSubMenuOf:void 0}:null}_item(e){var t;switch(e.type){case"separator":return o.createElement(l,{key:e.id,hint:e.getHint()});case"action":const n=null!==(t=e.custom())&&void 0!==t?t:e;return o.createElement(i.ContextMenuAction,{key:n.id,action:n,onShowSubMenu:this._handleShowSubMenu,isSubMenuOpened:this.state.showSubMenuOf===n,menuStatName:this.props.menuStatName,parentStatName:this.props.parentStatName,onRequestUpdate:this.props.onRequestUpdate})}}}},45876:(e,t,n)=>{"use strict";n.r(t),n.d(t,{SERIES_ICONS:()=>p});var o=n(94670),s=n(32162),l=n(39956),i=n(14083),a=n(45504),r=n(52867),c=n(41473),h=n(31246),d=n(15726),u=n(24464),v=n(3904),m=n(9450);const p={3:o,16:s,0:l,1:i,8:a,9:r,2:c,14:h,15:d,10:u,12:v,13:m}},65890:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11 9" width="11" height="9" fill="none"><path stroke-width="2" d="M0.999878 4L3.99988 7L9.99988 1"/></svg>'},66493:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.5 20L11 14.5 16.5 9"/></svg>'},79978:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},94670:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="m25.35 5.35-9.5 9.5-.35.36-.35-.36-4.65-4.64-8.15 8.14-.7-.7 8.5-8.5.35-.36.35.36 4.65 4.64 9.15-9.14.7.7ZM2 21h1v1H2v-1Zm2-1H3v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1V9h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1H6v1H5v1H4v1Zm1 0v1H4v-1h1Zm1 0H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0H7v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0H9v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1h1v1Zm1 0v1h-1v-1h1Zm0-1v-1h-1v1h1Zm0 0v1h1v1h1v-1h-1v-1h-1Zm6 2v-1h1v1h-1Zm2 0v1h-1v-1h1Zm0-1h-1v-1h1v1Zm1 0h-1v1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h1v1Zm1 0h-1v1h1v-1Zm0-1h1v1h-1v-1Zm0-1h1v-1h-1v1Zm0 0v1h-1v-1h1Zm-4 3v1h-1v-1h1Z"/></svg>'},39956:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="none" stroke="currentColor" stroke-linecap="square"><path d="M10.5 7.5v15M7.5 20.5H10M13.5 11.5H11M19.5 6.5v15M16.5 9.5H19M22.5 16.5H20"/></g></svg>'},24464:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m10.49 7.55-.42.7-2.1 3.5.86.5 1.68-2.8 1.8 2.82.84-.54-2.23-3.5-.43-.68Zm12.32 4.72-.84-.54 2.61-4 .84.54-2.61 4Zm-5.3 6.3 1.2-1.84.84.54-1.63 2.5-.43.65-.41-.65-1.6-2.5.85-.54 1.17 1.85ZM4.96 16.75l.86.52-2.4 4-.86-.52 2.4-4ZM3 14v1h1v-1H3Zm2 0h1v1H5v-1Zm2 0v1h1v-1H7Zm2 0h1v1H9v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Z"/></svg>'},14083:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v12h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v3.5h-1zm0 16.5h1V24h-1z"/></svg>'},53707:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><g fill="none"><path stroke="currentColor" d="M11 20.5H7.5a5 5 0 1 1 .42-9.98 7.5 7.5 0 0 1 14.57 2.1 4 4 0 0 1-1 7.877H18"/><path stroke="currentColor" d="M14.5 24V12.5M11 16l3.5-3.5L18 16"/></g></svg>'},9450:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12 7v14h5V7h-5Zm4 1h-3v12h3V8ZM19 15v6h5v-6h-5Zm4 1h-3v4h3v-4ZM5 12h5v9H5v-9Zm1 1h3v7H6v-7Z"/></svg>'},1393:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17zM4 14.5a9.5 9.5 0 1 1 19 0 9.5 9.5 0 0 1-19 0z"/><path fill="currentColor" d="M9 14h4v-4h1v4h4v1h-4v4h-1v-4H9v-1z"/></svg>'},49697:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g fill="currentColor"><path d="M21 7v4h1V6h-5v1z"/><path d="M16.854 11.854l5-5-.708-.708-5 5zM7 7v4H6V6h5v1z"/><path d="M11.146 11.854l-5-5 .708-.708 5 5zM21 21v-4h1v5h-5v-1z"/><path d="M16.854 16.146l5 5-.708.708-5-5z"/><g><path d="M7 21v-4H6v5h5v-1z"/><path d="M11.146 16.146l-5 5 .708.708 5-5z"/></g></g></svg>'},45504:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M9 8v12h3V8H9zm-1-.502C8 7.223 8.215 7 8.498 7h4.004c.275 0 .498.22.498.498v13.004a.493.493 0 0 1-.498.498H8.498A.496.496 0 0 1 8 20.502V7.498z"/><path d="M10 4h1v3.5h-1z"/><path d="M17 6v6h3V6h-3zm-1-.5c0-.276.215-.5.498-.5h4.004c.275 0 .498.23.498.5v7c0 .276-.215.5-.498.5h-4.004a.503.503 0 0 1-.498-.5v-7z"/><path d="M18 2h1v3.5h-1z"/></svg>'},3904:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7.5 7H7v14h5V7H7.5zM8 20V8h3v12H8zm7.5-11H15v10h5V9h-4.5zm.5 9v-8h3v8h-3z"/></svg>'},32162:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="M22 3h1v1h-1V3Zm0 2V4h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1V9h-1V8h-1V7h-1V6h-1V5h-1v1H9v1H8v1H7v1H6v1H5v1H4v1h1v1H4v1h1v-1h1v-1h1v-1h1v-1h1V9h1V8h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1V9h1V8h1V7h1V6h1V5h-1Zm-1 1V5h1v1h-1Zm-1 1V6h1v1h-1Zm-1 1V7h1v1h-1Zm-1 1V8h1v1h-1Zm-1 1V9h1v1h-1Zm-1 1v-1h1v1h-1Zm-1 0v-1h-1V9h-1V8h-1V7h-1V6h-1v1H9v1H8v1H7v1H6v1H5v1h1v-1h1v-1h1V9h1V8h1V7h1v1h1v1h1v1h1v1h1Zm0 0h1v1h-1v-1Zm.84 6.37 7.5-7-.68-.74-7.15 6.67-4.66-4.65-.33-.34-.36.32-5.5 5 .68.74 5.14-4.68 4.67 **********.35-.33ZM6 23H5v1h1v-1Zm0-1H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0v1H7v-1h1Zm0-1H7v-1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0v1H9v-1h1Zm0-1H9v-1h1v1Zm1 0h-1v1h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1h1v1Zm0 0h1v1h-1v-1Zm2 2v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1Zm0 0v-1h-1v1h1Z"/></svg>'},52867:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v11h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-12a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v5h-1zm0 14h1v5h-1zM8.5 9H10v1H8.5zM11 9h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11z"/></svg>'},39681:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M20 17l-5 5M15 17l5 5M9 11.5h7M17.5 8a2.5 2.5 0 0 0-5 0v11a2.5 2.5 0 0 1-5 0"/></svg>'},31246:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="m18.43 15.91 6.96-8.6-.78-.62-6.96 8.6a2.49 2.49 0 0 0-2.63.2l-2.21-2.02A2.5 2.5 0 0 0 10.5 10a2.5 2.5 0 1 0 1.73 4.3l2.12 1.92a2.5 2.5 0 1 0 4.08-.31ZM10.5 14a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm7.5 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/><path d="M8.37 13.8c.17.3.4.54.68.74l-5.67 6.78-.76-.64 5.75-6.88Z"/></svg>'},41473:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m25.39 7.31-8.83 10.92-6.02-5.47-7.16 8.56-.76-.64 7.82-9.36 6 5.45L24.61 6.7l.78.62Z"/></svg>'},96052:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18.293 13l-2.647 2.646.707.708 3.854-3.854-3.854-3.854-.707.708L18.293 12H12.5A5.5 5.5 0 0 0 7 17.5V19h1v-1.5a4.5 4.5 0 0 1 4.5-4.5h5.793z"/></svg>'},72644:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.118 6a.5.5 0 0 0-.447.276L9.809 8H5.5A1.5 1.5 0 0 0 4 9.5v10A1.5 1.5 0 0 0 5.5 21h16a1.5 1.5 0 0 0 1.5-1.5v-10A1.5 1.5 0 0 0 21.5 8h-4.309l-.862-1.724A.5.5 0 0 0 15.882 6h-4.764zm-1.342-.17A1.5 1.5 0 0 1 11.118 5h4.764a1.5 1.5 0 0 1 1.342.83L17.809 7H21.5A2.5 2.5 0 0 1 24 9.5v10a2.5 2.5 0 0 1-2.5 2.5h-16A2.5 2.5 0 0 1 3 19.5v-10A2.5 2.5 0 0 1 5.5 7h3.691l.585-1.17z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5 18a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zm0 1a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/></svg>'},15726:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M19 5h5v1h-4v13h-6v-7h-4v12H5v-1h4V11h6v7h4V5Z"/></svg>'},77665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.707 13l2.647 2.646-.707.708L6.792 12.5l3.853-3.854.708.708L8.707 12H14.5a5.5 5.5 0 0 1 5.5 5.5V19h-1v-1.5a4.5 4.5 0 0 0-4.5-4.5H8.707z"/></svg>'},80802:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 14" width="18" height="14"><path fill="currentColor" d="M6 11.17l-4.17-4.17-1.42 1.41 5.59 5.59 12-12-1.41-1.41-10.59 10.58z"/></svg>'},39750:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 15l5 5L23 9"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},69311:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9.7 9l4.65-4.65-.7-.7L9 8.29 4.35 3.65l-.7.7L8.29 9l-4.64 4.65.7.7L9 9.71l4.65 4.64.7-.7L9.71 9z"/></svg>'},90752:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13v-2.5m8.5 11h6.5a2 2 0 0 0 2-2v-9m-17 0v-2c0-1.1.9-2 2-2h13a2 2 0 0 1 2 2v2m-17 0h17"/><path fill="currentColor" d="M10 4h1v4h-1V4zM17 4h1v4h-1V4z"/><path stroke="currentColor" d="M4 18.5h7.5m0 0L8 22m3.5-3.5L8 15"/></svg>'},81111:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4 6.5C4 5.67 4.67 5 5.5 5h4.2l.15.15L11.71 7h8.79c.83 0 1.5.67 1.5 1.5V11H5V20.5c0 .*********.5H9v1H5.5A1.5 1.5 0 0 1 4 20.5V6.5zM5 10h16V8.5a.5.5 0 0 0-.5-.5h-9.2l-.15-.15L9.29 6H5.5a.5.5 0 0 0-.5.5V10z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.85 16.85l3.5-3.5-.7-.7-3.5 3.5a1.5 1.5 0 1 0 0 2.7l1.64 1.65-1.64 1.65a1.5 1.5 0 1 0 .7.7l1.65-1.64 1.65 1.64a1.5 1.5 0 1 0 2.7 0l3.5-3.5-.7-.7-3.5 3.5a1.5 1.5 0 0 0-1.3 0l-1.64-1.65 4.14-4.15-.7-.7-4.15 4.14-1.65-1.64a1.5 1.5 0 0 0 0-1.3zm-.85.65a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm6 6a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm-6.5.5a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z"/></svg>'},19908:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.39 3.84a1 1 0 0 1 1.22 0l8.19 6.37a1 1 0 0 1 0 1.58l-8.19 6.37a1 1 0 0 1-1.22 0L5.2 11.79a1 1 0 0 1 0-1.58l8.19-6.37zm.61.8L5.81 11 14 17.37 22.19 11 14 4.63zM5.3 13.6l8.7 6.76 8.7-*********-8.69 6.77a1 1 0 0 1-1.22 0l-8.7-6.77.62-.78zm8.09 10.55l-8.7-6.77.62-.78L14 23.37l8.7-*********-8.69 6.77a1 1 0 0 1-1.22 0z"/></svg>'}}]);