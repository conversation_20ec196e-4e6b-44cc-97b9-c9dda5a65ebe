(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2578],{22353:e=>{e.exports="(O + H + L + C)/4"},94884:e=>{e.exports="(H + L + C)/3"},10591:e=>{e.exports="(H + L)/2"},63243:e=>{e.exports="Color bars based on previous close"},15857:e=>{e.exports="Close line"},9994:e=>{e.exports="Adjust data for dividends"},10989:e=>{e.exports="Adjust for contract changes"},70816:e=>{e.exports="Average close"},50430:e=>{e.exports=["Bottom Line"]},83760:e=>{e.exports="Body"},72269:e=>{e.exports=["Περιθώρια"]},7445:e=>{e.exports=["Base Level"]},47586:e=>{e.exports="Bid and ask"},39667:e=>{e.exports="Down bars"},87151:e=>{e.exports="Down color"},81285:e=>{e.exports="Data modification"},4329:e=>{e.exports=["Προεπιλογή"]},86846:e=>{e.exports="Fill"},58747:e=>{e.exports=["Fill Top Area"]},11157:e=>{e.exports=["Fill Bottom Area"]},86953:e=>{e.exports="HLC bars"},39292:e=>{e.exports="High and low"},83678:e=>{e.exports="High line"},75310:e=>{e.exports="Low line"},15107:e=>{e.exports="Last"},6350:e=>{e.exports="Pre/post market"},62521:e=>{e.exports="Pre/post market hours background"},73947:e=>{e.exports=["Ακρίβεια"]},8094:e=>{e.exports="Previous day close"},77986:e=>{e.exports="Price lines"},24248:e=>{e.exports="Price source"},94089:e=>{e.exports="Projection up bars"},80293:e=>{e.exports="Projection candles"},5704:e=>{e.exports="Projection down bars"},29881:e=>{e.exports="Real prices on price scale (instead of Heikin-Ashi price)"},57417:e=>{e.exports=["Top Line"]},55314:e=>{e.exports=["Thin Bars"]},87492:e=>{e.exports="Timezone"},5536:e=>{e.exports="Up color"},83610:e=>{e.exports="Up bars"},23500:e=>{e.exports="Use settlement as close on daily interval"},35612:e=>{e.exports="Use volume weighted bars"},30792:e=>{e.exports="candle"},55740:e=>{e.exports="change HLC bars"},68927:e=>{e.exports="change average close price line width"},30385:e=>{e.exports="change average close price line color"},97008:e=>{e.exports="change area fill color"},6610:e=>{e.exports="change area line width"},661:e=>{e.exports="change area line color"},1316:e=>{e.exports="change area price source"},29180:e=>{e.exports="change ask line color"},31547:e=>{e.exports="change base level"},4164:e=>{e.exports="change baseline bottom line color"},38990:e=>{e.exports="change baseline bottom line width"},73163:e=>{e.exports="change baseline fill bottom area color"},12673:e=>{e.exports="change baseline fill top area color"},56819:e=>{e.exports="change baseline price source"},68621:e=>{e.exports="change baseline top line color"},35339:e=>{e.exports="change baseline top line width"},76804:e=>{e.exports="change bar up color"},71816:e=>{e.exports="change bar down color"},36703:e=>{e.exports="change bid line color"},29353:e=>{e.exports="change color bars based on previous close"},85709:e=>{e.exports="change column up color"},12155:e=>{e.exports="change column down color"},66890:e=>{e.exports="change column price source"},71809:e=>{e.exports="change decimal places"},31317:e=>{e.exports="change extended hours color"},60944:e=>{
e.exports="change high and low price line color"},83708:e=>{e.exports="change high and low price line width"},81080:e=>{e.exports="change high-low body color"},30033:e=>{e.exports="change high-low body visibility"},76885:e=>{e.exports="change high-low border color"},79236:e=>{e.exports="change high-low borders visibility"},42981:e=>{e.exports="change high-low labels visibility"},31937:e=>{e.exports="change high-low labels color"},87828:e=>{e.exports="change line color"},17119:e=>{e.exports="change line price source"},69125:e=>{e.exports="change line width"},49973:e=>{e.exports="change post market color"},5969:e=>{e.exports="change post market line color"},50393:e=>{e.exports="change pre/post market price lines visibility"},46257:e=>{e.exports="change pre market color"},60852:e=>{e.exports="change pre market line color"},91183:e=>{e.exports="change previous close price line color"},87631:e=>{e.exports="change previous close price line width"},77640:e=>{e.exports="change price line color"},97322:e=>{e.exports="change price line width"},35116:e=>{e.exports="change range bars style"},28143:e=>{e.exports="change range thin bars"},75986:e=>{e.exports="change renko wick down color"},7747:e=>{e.exports="change renko wick up color"},9473:e=>{e.exports="change renko wick visibility"},39783:e=>{e.exports="change the display of real prices on price scale (instead of Heiken-Ashi price)"},72886:e=>{e.exports="change thin bars"},95108:e=>{e.exports="change use volume weighted bars"},5464:e=>{e.exports="change {candleType} up border color"},61118:e=>{e.exports="change {candleType} up color"},60164:e=>{e.exports="change {candleType} wick down color"},45543:e=>{e.exports="change {candleType} wick up color"},39987:e=>{e.exports="change {candleType} wick visibility"},47202:e=>{e.exports="change {candleType} body visibility"},23986:e=>{e.exports="change {candleType} border visibility"},92330:e=>{e.exports="change {candleType} down border color"},36320:e=>{e.exports="change {candleType} down color"},79088:e=>{e.exports="change {chartType} border bar down color"},11107:e=>{e.exports="change {chartType} border bar up color"},85503:e=>{e.exports="change {chartType} down color"},61250:e=>{e.exports="change {chartType} projection border bar up color"},18465:e=>{e.exports="change {chartType} projection bar down color"},50453:e=>{e.exports="change {chartType} projection bar up color"},59414:e=>{e.exports="change {chartType} up color"},21547:e=>{e.exports="change {inputName} property"},42390:e=>{e.exports="adjust data for dividends"},99511:e=>{e.exports="adjust for contract changes"},75165:e=>{e.exports=["Hollow Candles"]},18995:e=>{e.exports="range"},47500:e=>{e.exports=["Renko"]},98402:e=>{e.exports="use settlement as close on daily interval"}}]);