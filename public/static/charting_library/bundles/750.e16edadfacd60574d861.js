(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[750],{66783:e=>{"use strict";var t=Object.prototype.hasOwnProperty;function r(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}e.exports=function(e,n){if(r(e,n))return!0;if("object"!=typeof e||null===e||"object"!=typeof n||null===n)return!1;var o=Object.keys(e),a=Object.keys(n);if(o.length!==a.length)return!1;for(var l=0;l<o.length;l++)if(!t.call(n,o[l])||!r(e[o[l]],n[o[l]]))return!1;return!0}},58222:e=>{e.exports={"light-button":"light-button-bYDQcOkp",link:"link-bYDQcOkp",content:"content-bYDQcOkp","visually-hidden":"visually-hidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp","ellipsis-container":"ellipsis-container-bYDQcOkp","text-wrap-container":"text-wrap-container-bYDQcOkp","text-wrap-with-ellipsis":"text-wrap-with-ellipsis-bYDQcOkp",icon:"icon-bYDQcOkp","force-direction-ltr":"force-direction-ltr-bYDQcOkp","force-direction-rtl":"force-direction-rtl-bYDQcOkp","with-grouped":"with-grouped-bYDQcOkp","variant-quiet-primary":"variant-quiet-primary-bYDQcOkp",selected:"selected-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp","size-xsmall":"size-xsmall-bYDQcOkp","with-start-icon":"with-start-icon-bYDQcOkp","with-end-icon":"with-end-icon-bYDQcOkp","no-content":"no-content-bYDQcOkp",wrap:"wrap-bYDQcOkp","size-small":"size-small-bYDQcOkp","size-medium":"size-medium-bYDQcOkp","variant-primary":"variant-primary-bYDQcOkp","color-gray":"color-gray-bYDQcOkp",caret:"caret-bYDQcOkp",grouped:"grouped-bYDQcOkp",pills:"pills-bYDQcOkp",active:"active-bYDQcOkp","disable-active-on-touch":"disable-active-on-touch-bYDQcOkp","disable-active-state-styles":"disable-active-state-styles-bYDQcOkp","color-green":"color-green-bYDQcOkp","color-red":"color-red-bYDQcOkp","variant-secondary":"variant-secondary-bYDQcOkp","variant-ghost":"variant-ghost-bYDQcOkp"}},88803:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},57240:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},40281:e=>{e.exports={container:"container-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},52597:e=>{e.exports={actions:"actions-rarsm4ka",actionButton:"actionButton-rarsm4ka"}},54257:e=>{e.exports={logo:"logo-d0vVmGvT"}},39339:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",itemRow:"itemRow-oRSs8UQo",multiLine:"multiLine-oRSs8UQo",cell:"cell-oRSs8UQo",itemInfoCell:"itemInfoCell-oRSs8UQo",description:"description-oRSs8UQo",symbolDescription:"symbolDescription-oRSs8UQo",flag:"flag-oRSs8UQo",
exchangeDescription:"exchangeDescription-oRSs8UQo",marketType:"marketType-oRSs8UQo",exchangeName:"exchangeName-oRSs8UQo",actionHandleWrap:"actionHandleWrap-oRSs8UQo",source:"source-oRSs8UQo",hover:"hover-oRSs8UQo",selected:"selected-oRSs8UQo",active:"active-oRSs8UQo",highlighted:"highlighted-oRSs8UQo",light:"light-oRSs8UQo","highlight-animation-theme-light":"highlight-animation-theme-light-oRSs8UQo",dark:"dark-oRSs8UQo","highlight-animation-theme-dark":"highlight-animation-theme-dark-oRSs8UQo",markedFlag:"markedFlag-oRSs8UQo",offset:"offset-oRSs8UQo",descriptionCell:"descriptionCell-oRSs8UQo",addition:"addition-oRSs8UQo",exchangeCell:"exchangeCell-oRSs8UQo",fixedWidth:"fixedWidth-oRSs8UQo",expandHandle:"expandHandle-oRSs8UQo",expanded:"expanded-oRSs8UQo",symbolTitle:"symbolTitle-oRSs8UQo",invalid:"invalid-oRSs8UQo",noDescription:"noDescription-oRSs8UQo",highlightedText:"highlightedText-oRSs8UQo",icon:"icon-oRSs8UQo",narrow:"narrow-oRSs8UQo",wide:"wide-oRSs8UQo",dataMode:"dataMode-oRSs8UQo",actionsCell:"actionsCell-oRSs8UQo",action:"action-oRSs8UQo",targetAction:"targetAction-oRSs8UQo",removeAction:"removeAction-oRSs8UQo",addAction:"addAction-oRSs8UQo",markedFlagWrap:"markedFlagWrap-oRSs8UQo",markedFlagMobile:"markedFlagMobile-oRSs8UQo",logo:"logo-oRSs8UQo",isExpandable:"isExpandable-oRSs8UQo",primaryIcon:"primaryIcon-oRSs8UQo"}},33172:e=>{e.exports={icon:"icon-OJpk_CAQ"}},50674:e=>{e.exports={wrap:"wrap-IxKZEhmO",libAllSelected:"libAllSelected-IxKZEhmO",container:"container-IxKZEhmO",iconWrap:"iconWrap-IxKZEhmO",icon:"icon-IxKZEhmO",title:"title-IxKZEhmO",highlighted:"highlighted-IxKZEhmO",description:"description-IxKZEhmO",mobile:"mobile-IxKZEhmO",allSelected:"allSelected-IxKZEhmO",desktop:"desktop-IxKZEhmO",allSelectedIcon:"allSelectedIcon-IxKZEhmO",selected:"selected-IxKZEhmO",focused:"focused-IxKZEhmO",titleWithoutDesc:"titleWithoutDesc-IxKZEhmO",textBlock:"textBlock-IxKZEhmO",bordered:"bordered-IxKZEhmO"}},70699:e=>{e.exports={container:"container-dfKL9A7t",contentList:"contentList-dfKL9A7t",contentListDesktop:"contentListDesktop-dfKL9A7t",searchSourceItemsContainer:"searchSourceItemsContainer-dfKL9A7t",oneColumn:"oneColumn-dfKL9A7t",searchSourceItemsContainerDesktop:"searchSourceItemsContainerDesktop-dfKL9A7t",groupTitleDesktop:"groupTitleDesktop-dfKL9A7t",column:"column-dfKL9A7t",emptyText:"emptyText-dfKL9A7t",emptyIcon:"emptyIcon-dfKL9A7t",noResultsDesktop:"noResultsDesktop-dfKL9A7t"}},37796:e=>{e.exports={wrap:"wrap-gjrLBBL3",item:"item-gjrLBBL3",small:"small-gjrLBBL3",newStyles:"newStyles-gjrLBBL3",mobile:"mobile-gjrLBBL3",text:"text-gjrLBBL3",exchange:"exchange-gjrLBBL3",filterItem:"filterItem-gjrLBBL3",brokerWrap:"brokerWrap-gjrLBBL3"}},52662:e=>{e.exports={wrap:"wrap-dlewR1s1",watchlist:"watchlist-dlewR1s1",noFeed:"noFeed-dlewR1s1",newStyles:"newStyles-dlewR1s1",scrollContainer:"scrollContainer-dlewR1s1",listContainer:"listContainer-dlewR1s1",multiLineItemsContainer:"multiLineItemsContainer-dlewR1s1",withSpinner:"withSpinner-dlewR1s1",spinnerContainer:"spinnerContainer-dlewR1s1",
largeSpinner:"largeSpinner-dlewR1s1"}},85544:e=>{e.exports={search:"search-ZXzPWcCf",upperCase:"upperCase-ZXzPWcCf",withFilters:"withFilters-ZXzPWcCf",withButton:"withButton-ZXzPWcCf",symbolType:"symbolType-ZXzPWcCf",spinnerWrap:"spinnerWrap-ZXzPWcCf",emptyText:"emptyText-ZXzPWcCf",emptyIcon:"emptyIcon-ZXzPWcCf",noResultsDesktop:"noResultsDesktop-ZXzPWcCf",brokerButtonWrap:"brokerButtonWrap-ZXzPWcCf",brokerButton:"brokerButton-ZXzPWcCf"}},14444:e=>{e.exports={flagWrap:"flagWrap-QKnxaZOG",icon:"icon-QKnxaZOG",caret:"caret-QKnxaZOG",title:"title-QKnxaZOG",button:"button-QKnxaZOG",withFlag:"withFlag-QKnxaZOG",buttonContent:"buttonContent-QKnxaZOG"}},62393:e=>{e.exports={dialog:"dialog-u2dP3kv1",tabletDialog:"tabletDialog-u2dP3kv1",desktopDialog:"desktopDialog-u2dP3kv1",backButton:"backButton-u2dP3kv1"}},54638:e=>{e.exports={childrenWrapper:"childrenWrapper-_RhDhmVQ",container:"container-_RhDhmVQ"}},88389:e=>{e.exports={bubbles:"bubbles-Ie7o2cas",multiLine:"multiLine-Ie7o2cas",bubble:"bubble-Ie7o2cas"}},61371:e=>{e.exports={bubble:"bubble-zcjhaZ_y",animated:"animated-zcjhaZ_y",content:"content-zcjhaZ_y","appearance-default":"appearance-default-zcjhaZ_y",active:"active-zcjhaZ_y",gray:"gray-zcjhaZ_y",red:"red-zcjhaZ_y",blue:"blue-zcjhaZ_y",green:"green-zcjhaZ_y",orange:"orange-zcjhaZ_y",purple:"purple-zcjhaZ_y",cyan:"cyan-zcjhaZ_y",pink:"pink-zcjhaZ_y","appearance-text":"appearance-text-zcjhaZ_y","fontSize-s":"fontSize-s-zcjhaZ_y","fontSize-m":"fontSize-m-zcjhaZ_y","size-m":"size-m-zcjhaZ_y","size-l":"size-l-zcjhaZ_y"}},82112:e=>{e.exports={}},45300:e=>{e.exports={}},75623:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},34587:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},18429:(e,t,r)=>{"use strict";r.d(t,{SEPARATOR_PREFIX:()=>n,isSeparatorItem:()=>o});const n="###";function o(e){return e.startsWith(n)}},48199:(e,t,r)=>{"use strict";r.d(t,{BackButton:()=>v});var n=r(50959),o=r(64388),a=r(95694),l=r(49498),s=r(60176),i=r(35369),c=r(58478),u=r(73063),d=r(14127),m=r(18073),p=r(99243),h=r(42576);function g(e="large",t="1.2"){switch(e){case"large":return"1.2"===t?a:u;case"medium":return"1.2"===t?l:d;case"small":return"1.2"===t?s:m;case"xsmall":return"1.2"===t?i:p;case"xxsmall":return"1.2"===t?c:h;default:return l}}const v=n.forwardRef(((e,t)=>{const{"aria-label":r,flipIconOnRtl:a,...l}=e;return n.createElement(o.NavButton,{...l,"aria-label":r,ref:t,icon:g(e.size,e.iconStrokeWidth),flipIconOnRtl:a})}))},27011:(e,t,r)=>{"use strict";function n(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}r.d(t,{isIconOnly:()=>n})},14543:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>n.LightButton});r(8025);var n=r(15893);r(50959),r(21593)},8025:(e,t,r)=>{"use strict";r.d(t,{LightButtonContent:()=>g,useLightButtonClasses:()=>h});var n=r(50959),o=r(97754),a=r(34094),l=r(9745),s=r(17946),i=r(27011),c=r(86332);const u=n.createContext({isInButtonGroup:!1,isGroupPrimary:!1});var d=r(2948),m=r(58222),p=r.n(m);const h=(e,t)=>{const r=(0,n.useContext)(s.CustomBehaviourContext),a=(0,
n.useContext)(c.ControlGroupContext),{isInButtonGroup:l,isGroupPrimary:d}=(0,n.useContext)(u),{className:m,isSelected:h,children:g,startIcon:v,showCaret:f,endIcon:y,forceDirection:b,iconOnly:S,color:x="gray",variant:w="primary",size:k="medium",enableActiveStateStyles:C=r.enableActiveStateStyles,typography:E,isLink:I=!1,textWrap:L,isPills:T,isActive:R}=e,N=p()[`typography-${((e,t,r)=>{if(r){const e=r.replace(/^\D+/g,"");return t?`semibold${e}`:r}return"xsmall"===e?t?"semibold14px":"regular14px":"small"===e||"medium"===e?t?"semibold16px":"regular16px":""})(k,h||T,E||void 0)}`];return o(m,p()["light-button"],I&&p().link,R&&p().active,h&&p().selected,(0,i.isIconOnly)(g,S)&&p()["no-content"],v&&p()["with-start-icon"],(f||y)&&p()["with-end-icon"],t&&p()["with-grouped"],b&&p()[`force-direction-${b}`],p()[`variant-${d?"primary":w}`],p()[`color-${d?"gray":x}`],p()[`size-${k}`],N,!C&&p()["disable-active-state-styles"],a.isGrouped&&p().grouped,L&&p().wrap,l&&p()["disable-active-on-touch"],T&&p().pills)};function g(e){const{startIcon:t,endIcon:r,showCaret:s,iconOnly:c,ellipsis:u=!0,textWrap:m,tooltipText:h,children:g}=e;return n.createElement(n.Fragment,null,t&&n.createElement(l.Icon,{className:p().icon,icon:t}),!(0,i.isIconOnly)(g,c)&&n.createElement("span",{className:o(p().content,!m&&p().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":null!=h?h:(0,a.getTextForTooltip)(g)},m||u?n.createElement(n.Fragment,null,n.createElement("span",{className:o(!m&&u&&p()["ellipsis-container"],m&&p()["text-wrap-container"],m&&u&&p()["text-wrap-with-ellipsis"])},g),n.createElement("span",{className:p()["visually-hidden"],"aria-hidden":!0},g)):n.createElement(n.Fragment,null,g,n.createElement("span",{className:p()["visually-hidden"],"aria-hidden":!0},g))),(r||s)&&(e=>n.createElement(l.Icon,{className:o(p().icon,e.showCaret&&p().caret),icon:e.showCaret?d:e.endIcon}))(e))}},15893:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>l});var n=r(50959),o=r(86332),a=r(8025);function l(e){const{isGrouped:t}=n.useContext(o.ControlGroupContext),{reference:r,className:l,isSelected:s,children:i,startIcon:c,iconOnly:u,ellipsis:d,showCaret:m,forceDirection:p,endIcon:h,color:g,variant:v,size:f,enableActiveStateStyles:y,typography:b,textWrap:S=!1,maxLines:x,style:w={},isPills:k,isActive:C,tooltipText:E,...I}=e,L=S?null!=x?x:2:1,T=L>0?{...w,"--ui-lib-light-button-content-max-lines":L}:w;return n.createElement("button",{...I,className:(0,a.useLightButtonClasses)({className:l,isSelected:s,children:i,startIcon:c,iconOnly:u,showCaret:m,forceDirection:p,endIcon:h,color:g,variant:v,size:f,enableActiveStateStyles:y,typography:b,textWrap:S,isPills:k,isActive:C},t),ref:r,style:T},n.createElement(a.LightButtonContent,{showCaret:m,startIcon:c,endIcon:h,iconOnly:u,ellipsis:d,textWrap:S,tooltipText:E},i))}},86332:(e,t,r)=>{"use strict";r.d(t,{ControlGroupContext:()=>n});const n=r(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},
17946:(e,t,r)=>{"use strict";r.d(t,{CustomBehaviourContext:()=>n});const n=(0,r(50959).createContext)({enableActiveStateStyles:!0});n.displayName="CustomBehaviourContext"},125:(e,t,r)=>{"use strict";r.d(t,{useForceUpdate:()=>o});var n=r(50959);const o=()=>{const[,e]=(0,n.useReducer)((e=>e+1),0);return e}},39416:(e,t,r)=>{"use strict";r.d(t,{useFunctionalRefObject:()=>a});var n=r(50959),o=r(43010);function a(e){const t=(0,n.useMemo)((()=>function(e){const t=r=>{e(r),t.current=r};return t.current=null,t}((e=>{s.current(e)}))),[]),r=(0,n.useRef)(null),a=t=>{if(null===t)return l(r.current,t),void(r.current=null);r.current!==e&&(r.current=e,l(r.current,t))},s=(0,n.useRef)(a);return s.current=a,(0,o.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return s.current(t.current),()=>s.current(null)}),[e]),t}function l(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},43010:(e,t,r)=>{"use strict";r.d(t,{useIsomorphicLayoutEffect:()=>o});var n=r(50959);function o(e,t){("undefined"==typeof window?n.useEffect:n.useLayoutEffect)(e,t)}},86781:(e,t,r)=>{"use strict";r.d(t,{useMatchMedia:()=>a,useSafeMatchMedia:()=>o});var n=r(50959);function o(e,t=!1){const[r,o]=(0,n.useState)(t);return(0,n.useEffect)((()=>{const t=window.matchMedia(e);function r(){o(t.matches)}return r(),t.addListener(r),()=>{t.removeListener(r)}}),[e]),r}function a(e){const t=(0,n.useMemo)((()=>window.matchMedia(e).matches),[]);return o(e,t)}},27267:(e,t,r)=>{"use strict";function n(e,t,r,n,o){function a(o){if(e>o.timeStamp)return;const a=o.target;void 0!==r&&null!==t&&null!==a&&a.ownerDocument===n&&(t.contains(a)||r(o))}return o.click&&n.addEventListener("click",a,!1),o.mouseDown&&n.addEventListener("mousedown",a,!1),o.touchEnd&&n.addEventListener("touchend",a,!1),o.touchStart&&n.addEventListener("touchstart",a,!1),()=>{n.removeEventListener("click",a,!1),n.removeEventListener("mousedown",a,!1),n.removeEventListener("touchend",a,!1),n.removeEventListener("touchstart",a,!1)}}r.d(t,{addOutsideEventListener:()=>n})},67842:(e,t,r)=>{"use strict";r.d(t,{useResizeObserver:()=>s});var n=r(50959),o=r(59255),a=r(43010),l=r(39416);function s(e,t=[]){const{callback:r,ref:s=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),i=(0,n.useRef)(null),c=(0,n.useRef)(r);c.current=r;const u=(0,l.useFunctionalRefObject)(s),d=(0,n.useCallback)((e=>{u(e),null!==i.current&&(i.current.disconnect(),null!==e&&i.current.observe(e))}),[u,i]);return(0,a.useIsomorphicLayoutEffect)((()=>(i.current=new o.default(((e,t)=>{c.current(e,t)})),u.current&&d(u.current),()=>{var e;null===(e=i.current)||void 0===e||e.disconnect()})),[u,...t]),d}},90186:(e,t,r)=>{"use strict";function n(e){return a(e,l)}function o(e){return a(e,s)}function a(e,t){const r=Object.entries(e).filter(t),n={};for(const[e,t]of r)n[e]=t;return n}function l(e){const[t,r]=e;return 0===t.indexOf("data-")&&"string"==typeof r}function s(e){return 0===e[0].indexOf("aria-")}r.d(t,{filterAriaProps:()=>o,filterDataProps:()=>n,filterProps:()=>a,isAriaAttribute:()=>s,isDataAttribute:()=>l})},34094:(e,t,r)=>{
"use strict";r.d(t,{getTextForTooltip:()=>l});var n=r(50959);const o=e=>(0,n.isValidElement)(e)&&Boolean(e.props.children),a=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",l=e=>Array.isArray(e)||(0,n.isValidElement)(e)?n.Children.toArray(e).reduce(((e,t)=>{let r="";return r=(0,n.isValidElement)(t)&&o(t)?l(t.props.children):(0,n.isValidElement)(t)&&!o(t)?"":a(t),e.concat(r)}),"").trim():a(e)},76460:(e,t,r)=>{"use strict";function n(e){return 0===e.detail}r.d(t,{isKeyboardClick:()=>n})},3685:(e,t,r)=>{"use strict";function n(){var e,t,r;return null!==(r=null===(t=null===(e=window.configurationData)||void 0===e?void 0:e.exchanges)||void 0===t?void 0:t.map((e=>({...e,country:"",providerId:"",flag:""}))))&&void 0!==r?r:[]}r.d(t,{getExchanges:()=>n})},36279:(e,t,r)=>{"use strict";var n;r.d(t,{LogoSize:()=>n,getLogoUrlResolver:()=>l}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(n||(n={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}}let a;function l(){return a||(a=new o),a}},24437:(e,t,r)=>{"use strict";r.d(t,{DialogBreakpoints:()=>o});var n=r(88803);const o={SmallHeight:n["small-height-breakpoint"],TabletSmall:n["tablet-small-breakpoint"],TabletNormal:n["tablet-normal-breakpoint"]}},69654:(e,t,r)=>{"use strict";r.d(t,{DialogSearch:()=>u});var n=r(50959),o=r(97754),a=r.n(o),l=r(11542),s=r(9745),i=r(69859),c=r(40281);function u(e){const{children:t,renderInput:o,onCancel:u,containerClassName:m,inputContainerClassName:p,iconClassName:h,...g}=e;return n.createElement("div",{className:a()(c.container,m)},n.createElement("div",{className:a()(c.inputContainer,p,u&&c.withCancel)},o||n.createElement(d,{...g})),t,n.createElement(s.Icon,{className:a()(c.icon,h),icon:i}),u&&n.createElement("div",{className:c.cancel,onClick:u},l.t(null,void 0,r(20036))))}function d(e){const{className:t,reference:r,value:o,onChange:l,onFocus:s,onBlur:i,onKeyDown:u,onSelect:d,placeholder:m,...p}=e;return n.createElement("input",{...p,ref:r,type:"text",className:a()(t,c.input),autoComplete:"off","data-role":"search",placeholder:m,value:o,onChange:l,onFocus:s,onBlur:i,onSelect:d,onKeyDown:u})}},17531:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchDialogContentItem:()=>B});var n=r(50959),o=r(97754),a=r.n(o),l=(r(11542),r(50151)),s=r(9745),i=r(14483),c=r(24637),u=r(19785),d=r(84524),m=r(24633),p=r(77975),h=r(45345),g=r(32563),v=r(94474),f=r(93251),y=r(36279),b=r(44747);r(82112);var S=r(76068),x=r(58492),w=r(12767),k=r(43010),C=r(57240);function E(e){const{className:t,placeholderLetter:r,url1:o,url2:l,size:s="xxxsmall"}=e,i=(0,n.useRef)(null),c=(0,n.useRef)(null),u=(0,n.useRef)(null),d=(0,n.useRef)(null),m=(0,n.useRef)(null),p=(0,n.useRef)(null);return(0,k.useIsomorphicLayoutEffect)((()=>{const e=void 0===o?[]:void 0===l?[o]:[o,l],t=p.current=(r=e,Promise.all(r.map((e=>(0,w.getImage)(`symbol_logo_${e}`,e,L).then((e=>e.cloneNode()))))));var r;t.then((e=>{var r,n,o,a,l,s,h,g,v;if(t===p.current)switch(e.length){
case 0:null===(r=u.current)||void 0===r||r.classList.add(C.hidden),null===(n=c.current)||void 0===n||n.classList.add(S.hiddenCircleLogoClass),null===(o=i.current)||void 0===o||o.classList.remove(S.hiddenCircleLogoClass);break;case 1:I(c.current,e[0]),null===(a=u.current)||void 0===a||a.classList.add(C.hidden),null===(l=c.current)||void 0===l||l.classList.remove(S.hiddenCircleLogoClass),null===(s=i.current)||void 0===s||s.classList.add(S.hiddenCircleLogoClass);break;case 2:I(d.current,e[0]),I(m.current,e[1]),null===(h=u.current)||void 0===h||h.classList.remove(C.hidden),null===(g=c.current)||void 0===g||g.classList.add(S.hiddenCircleLogoClass),null===(v=i.current)||void 0===v||v.classList.add(S.hiddenCircleLogoClass)}}))}),[o,l]),n.createElement("span",{className:a()(t,C.container)},n.createElement("span",{ref:u,className:a()(C.pairContainer,C.hidden)},n.createElement("span",{className:(0,b.getBlockStyleClasses)(s)},n.createElement("span",{ref:m,className:a()(C.logo,(0,b.getLogoStyleClasses)(s))}),n.createElement("span",{ref:d,className:a()(C.logo,(0,b.getLogoStyleClasses)(s))}))),n.createElement("span",{ref:c,className:a()(C.logo,S.hiddenCircleLogoClass,(0,x.getStyleClasses)(s))}),n.createElement("span",{ref:i,className:a()(C.logo,(0,x.getStyleClasses)(s))},n.createElement(S.CircleLogo,{size:s,placeholderLetter:r})))}function I(e,t){e&&(e.innerHTML="",e.appendChild(t))}function L(e){e.crossOrigin="",e.decoding="async"}var T=r(54257);function R(e){const{logoId:t,baseCurrencyLogoId:r,currencyLogoId:o,placeholder:l,className:s,size:i="xsmall"}=e,c=(0,n.useMemo)((()=>{const e={logoid:t,"currency-logoid":o,"base-currency-logoid":r};return(0,f.removeUsdFromCryptoPairLogos)((0,f.resolveLogoUrls)(e,y.LogoSize.Medium))}),[t,o,r]);return n.createElement(E,{key:i,className:a()(T.logo,s),url1:c[0],url2:c[1],placeholderLetter:l,size:i})}var N=r(29562),D=r(69533),_=r(39339);function B(e){var t,r;const{dangerousTitleHTML:o,title:f,dangerousDescriptionHTML:y,description:b,searchToken:S,exchangeName:x,marketType:w,onClick:k,isSelected:C,isEod:E=!1,isActive:I=!1,isOffset:L=!1,invalid:T=!1,isHighlighted:B=!1,hideExchange:M=!1,hideMarkedListFlag:O=!1,onExpandClick:Q,isExpanded:F,hoverComponent:A,country:P,providerId:U,source:z,source2:V,type:W,flag:Z,itemRef:K,onMouseOut:$,onMouseOver:j,className:G,actions:q,reference:Y,fullSymbolName:H,logoId:X,currencyLogoId:J,baseCurrencyLogoId:ee,shortName:te,hideLogo:re=!1,exchangeTooltip:ne,hideMarketType:oe,isPrimary:ae}=e,{isSmallWidth:le,isMobile:se}=(0,l.ensureNotNull)((0,n.useContext)(d.SymbolSearchItemsDialogContext)),ie=Boolean(A),ce=!T&&!M&&(se||!ie),ue=(0,p.useWatchedValueReadonly)({watchedValue:h.watchedTheme})===m.StdTheme.Dark?_.dark:_.light,de=A,me=i.enabled("show_symbol_logos"),pe=i.enabled("show_exchange_logos"),he=me||!1,ge=null!==(t=null==V?void 0:V.description)&&void 0!==t?t:z,ve=null!==(r=null==V?void 0:V.name)&&void 0!==r?r:z;return n.createElement("div",{
className:a()(_.itemRow,le&&_.multiLine,B&&_.highlighted,B&&ue,C&&_.selected,I&&_.active,T&&_.invalid,!se&&g.mobiletouch&&ie&&_.hover,G),onClick:function(e){if(!k||e.defaultPrevented)return;e.preventDefault(),k(e)},"data-role":e["data-role"]||"list-item","data-active":I,"data-type":w,"data-name":"symbol-search-dialog-content-item",onMouseOut:$,onMouseOver:j,ref:Y},n.createElement("div",{ref:K,className:a()(_.itemInfoCell,_.cell,L&&_.offset)},n.createElement("div",{className:a()(_.actionHandleWrap,!he&&_.fixedWidth)},n.createElement(n.Fragment,null,!1,Q&&n.createElement("div",{onClick:function(e){if(!Q||e.defaultPrevented)return;e.preventDefault(),Q(e)}},n.createElement(s.Icon,{className:a()(_.expandHandle,F&&_.expanded,C&&_.selected),icon:D})),he&&!L&&n.createElement("div",{className:a()(_.logo,Boolean(Q)&&_.isExpandable)},n.createElement(R,{key:H,logoId:X,currencyLogoId:J,baseCurrencyLogoId:ee,placeholder:te?te[0]:void 0})))),n.createElement("div",{className:a()(_.description,he&&L&&_.offset)},f&&n.createElement("div",{className:a()(_.symbolTitle,I&&_.active,T&&_.invalid,!Boolean(y)&&_.noDescription),"data-name":"list-item-title"},"string"==typeof f&&S?n.createElement(c.HighlightedText,{className:_.highlightedText,text:f,queryString:S,rules:(0,u.createRegExpList)(S)}):f,E&&n.createElement("span",{className:_.dataMode},"E")),!f&&o&&n.createElement("div",{className:a()(_.symbolTitle,I&&_.active,T&&_.invalid),"data-name":"list-item-title"},n.createElement("span",{dangerouslySetInnerHTML:{__html:o}}),E&&n.createElement("span",{className:_.dataMode},"E")),le&&fe())),!le&&n.createElement("div",{className:a()(_.cell,_.descriptionCell,Boolean(de)&&_.addition)},fe(),de?n.createElement(de,{...e,className:_.actions,onMouseOver:void 0,onMouseOut:void 0}):null),le&&de?n.createElement(de,{...e,className:_.cell,onMouseOver:void 0,onMouseOut:void 0}):null,ce&&n.createElement("div",{className:a()(_.exchangeCell,_.cell)},n.createElement("div",{className:a()(_.exchangeDescription)},!oe&&n.createElement("div",{className:a()(_.marketType,I&&_.active)},w),n.createElement("div",{className:_.source},!1,"economic"===W&&ge&&ve?n.createElement("div",{className:a()(_.exchangeName,I&&_.active,"apply-common-tooltip",_.narrow,oe&&_.wide),title:ge},ve):n.createElement("div",{className:a()(_.exchangeName,I&&_.active,ne&&"apply-common-tooltip"),title:ne},x))),pe&&n.createElement("div",{className:_.flag},n.createElement(N.SymbolSearchFlag,{key:pe?`${H}_exchange`:`${P}_${U}_${null==V?void 0:V.id}_${W}_${Z}`,className:_.icon,country:P,providerId:U,sourceId:"economic"===W&&V?V.id:void 0}))),n.createElement("div",{className:a()(_.cell,Boolean(q)&&_.actionsCell)},q));function fe(){if(T)return null;const e=a()(_.symbolDescription,I&&_.active,!g.mobiletouch&&"apply-overflow-tooltip apply-overflow-tooltip--allow-text");return b?n.createElement("div",{className:e},S?n.createElement(c.HighlightedText,{className:_.highlightedText,text:b,queryString:S,rules:(0,u.createRegExpList)(S)}):b):y?n.createElement("div",{"data-overflow-tooltip-text":(0,v.removeTags)(y),
className:e,dangerouslySetInnerHTML:{__html:y}}):null}}},29562:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchFlag:()=>v});var n=r(50959),o=r(97754),a=r.n(o),l=r(24633),s=r(36279);const i=r.p+"mock-dark.16b5f3a431f502b03ae3.svg",c=r.p+"mock-light.d201313017eb2c1b989f.svg";function u(e){return e===l.StdTheme.Dark?i:c}var d=r(77975),m=r(45345),p=r(50151);const h=s.LogoSize.Medium;var g=r(33172);function v(e){const{country:t,providerId:r,sourceId:o,className:l}=e,i=(0,d.useWatchedValueReadonly)({watchedValue:m.watchedTheme}),[c,v]=(0,n.useState)(function({country:e,providerId:t,sourceId:r}){const n=(0,s.getLogoUrlResolver)();return o=>{const a=e=>n.getProviderLogoUrl(e,h),l=[{value:r,resolve:a},{value:e,resolve:e=>n.getCountryFlagUrl(e.toUpperCase(),h)},{value:t,resolve:a}].find((({value:e})=>void 0!==e&&e.length>0));return void 0!==l?l.resolve((0,p.ensureDefined)(l.value)):u(o)}}({country:t,providerId:r,sourceId:o})(i));return n.createElement("img",{className:a()(l,g.icon),crossOrigin:"",src:c,onError:function(){v(u(i))}})}},58442:(e,t,r)=>{"use strict";r.d(t,{QualifiedSources:()=>n,qualifyProName:()=>l});var n,o=r(50151),a=r(14483);r(81319);function l(e){return e}!function(e){function t(e){return e.pro_name}function r(e){{const t=a.enabled("pay_attention_to_ticker_not_symbol")?e.ticker:e.full_name;return(0,o.ensureDefined)(t)}}e.fromQuotesSnapshot=function(e){return"error"===e.status?e.symbolname:e.values.pro_name},e.fromQuotesResponse=function(e){const{values:r,symbolname:n,status:o}=e;return"error"===o&&n?n:t(r)},e.fromQuotes=t,e.fromSymbolSearchResult=function(e,t){{const{ticker:r,full_name:n}=null!=t?t:e;return a.enabled("pay_attention_to_ticker_not_symbol")?(0,o.ensureDefined)(null!=r?r:n):(0,o.ensureDefined)(n)}},e.fromSymbolInfo=r,e.fromSymbolMessage=function(e,t){return"symbol_resolved"===t.method?r(t.params[1]):e}}(n||(n={}))},20882:(e,t,r)=>{"use strict";r.d(t,{createSearchSources:()=>s,filterSearchSources:()=>a,isAllSearchSourcesSelected:()=>o,splitSearchSourcesByGroup:()=>l});const n=[];function o(e){return""===e.value()}function a(e,t){return e.filter((e=>e.includes(t)))}function l(e){const t=new Map;e.forEach((e=>{t.has(e.group())?t.get(e.group()).push(e):t.set(e.group(),[e])}));for(const e of t.values()){e[0].group()!==ExchangeGroup.NorthAmerica&&e.sort(((e,t)=>e.name().toLowerCase()>t.name().toLowerCase()?1:-1))}return new Map([...t.entries()].sort((([e],[t])=>n.indexOf(e)-n.indexOf(t))))}function s(e,t){return t.map((t=>new e(t)))}},70613:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchDialogBodyContext:()=>n});const n=r(50959).createContext(null)},84524:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialogContext:()=>n});const n=r(50959).createContext(null)},22350:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialog:()=>je});var n=r(50959),o=r(97754),a=r.n(o),l=r(11542),s=r(15983);const i=["futures","forex","bond","economic"];var c=r(84877),u=r(14483),d=r(24437),m=r(35057),p=r(9745),h=r(86240),g=r(86781),v=r(84524),f=r(69654),y=r(3343),b=r(16838);function S(e,t,r){return`source-item-${e}-${t}-${r}`}
var x=r(20882),w=r(54638);function k(e){const{children:t,className:r}=e;return n.createElement("div",{className:a()(w.container,r)},n.createElement("div",{className:w.childrenWrapper},t))}var C=r(50151),E=r(77762),I=r(24637),L=r(19785),T=r(81319),R=r(91540),N=r(50674);function D(e){const{searchSource:t,onClick:r,queryString:o,isFocused:l,id:s}=e,{symbolSearchContent:i,isAllSearchSourcesSelected:c,allSearchSourcesTitle:u,isMobile:d}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),m=i.currentSelectedSearchSource,h=(0,C.ensureNotNull)(m).value(),g=c(t),f=t.value()===h,y=(0,n.useMemo)((()=>(0,L.createRegExpList)(o)),[o]),b=t.description(),S=b&&!g,x=T.isSeparateSymbolSearchTabs&&g&&u?u:t.name(),w=a()(N.container,d?N.mobile:N.desktop,f&&N.selected,l&&N.focused,g&&N.allSelected,g&&N.libAllSelected,!g&&d&&N.bordered);return n.createElement("div",{className:a()(!d&&N.wrap,g&&N.libAllSelected),onClick:r,id:s},n.createElement("div",{className:w},n.createElement("div",{className:N.iconWrap},!!g&&n.createElement(p.Icon,{className:a()(N.icon,N.allSelectedIcon),icon:R})),n.createElement("div",{className:N.textBlock},n.createElement("div",{className:a()(N.title,!S&&!d&&N.titleWithoutDesc)},n.createElement(I.HighlightedText,{className:a()(f&&N.highlighted),queryString:o,text:x,rules:y})),S&&n.createElement("div",{className:a()(N.description,"apply-overflow-tooltip")},n.createElement(I.HighlightedText,{className:N.highlighted,queryString:o,rules:y,text:b})))))}var _=r(77975),B=r(45345),M=r(26843),O=r(70613),Q=r(66619),F=r(67562),A=r(70699);const P={emptyTextClassName:A.emptyText};function U(e){const{searchSources:t}=e,{setSelectedIndex:o,setSelectedSearchSource:s,setMode:i,isMobile:c,emptyState:u,autofocus:d}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),m=(0,_.useWatchedValueReadonly)({watchedValue:B.watchedTheme})===M.StdTheme.Dark?Q:F,w=(0,g.useMatchMedia)(h["media-phone-vertical"]),[C,I]=(0,n.useState)(""),L=(0,n.useMemo)((()=>[{group:null,sources:(0,T.createGroupColumns)((0,x.filterSearchSources)(t,C),w?1:2)}]),[t,C,w]),R=(0,n.useRef)(null),N=(0,n.useRef)(null),{focusedItem:U,activeDescendant:z,handleKeyDown:V,resetFocusedItem:W}=function(e,t,r){const[o,a]=(0,n.useState)(null),[l,s]=(0,n.useState)("");function i(t){const r=e[t.groupIndex].sources[t.col].length-1;if(t.row===r){const e=d(t.groupIndex+1);if(null===e)return;return t.col>0&&!u({...t,groupIndex:e,row:0})?void a({groupIndex:e,col:0,row:0}):void a({...t,groupIndex:e,row:0})}a({...t,row:t.row+1})}function c(t){var r,n;if(0===t.row){const o=d(t.groupIndex-1,-1);if(null===o)return;const l=null!==(n=null===(r=e[o].sources[t.col])||void 0===r?void 0:r.length)&&void 0!==n?n:0;return 0===l?void a({groupIndex:o,col:0,row:0}):void a({...t,groupIndex:o,row:l-1})}a({...t,row:t.row-1})}function u(t){var r,n;return Boolean(null===(n=null===(r=e[t.groupIndex])||void 0===r?void 0:r.sources[t.col])||void 0===n?void 0:n[t.row])}function d(t=0,r=1){const n=e.length;let o=(t+n)%n;for(;!u({groupIndex:o,col:0,row:0});)if(o=(o+r+n)%n,o===t)return null;return o}return(0,
n.useEffect)((()=>{if(!r.current)return;if(!o)return void s("");const e=S(o.groupIndex,o.col,o.row),t=r.current.querySelector(`#${e}`);null==t||t.scrollIntoView({block:"nearest"}),s(e)}),[o]),(0,n.useEffect)((()=>{a(null)}),[t]),{focusedItem:o,activeDescendant:l,handleKeyDown:function(n){if(!r.current)return;const l=(0,y.hashFromEvent)(n);if(32!==l&&13!==l)switch((0,b.mapKeyCodeToDirection)(l)){case"blockNext":if(n.preventDefault(),!o){const e=d();if(null===e)break;a({groupIndex:e,col:0,row:0});break}i(o);break;case"blockPrev":if(n.preventDefault(),!o)break;c(o);break;case"inlineNext":{if(!o||t)break;n.preventDefault();const r=e[o.groupIndex].sources.length;if(o.col===r-1||!u({...o,col:o.col+1})){i({...o,col:0});break}a({...o,col:o.col+1});break}case"inlinePrev":{if(!o||t)break;n.preventDefault();const r=e[o.groupIndex].sources.length;if(0===o.col){if(0!==o.row){c({...o,col:r-1});break}const t=d(o.groupIndex-1,-1);if(null===t)break;const n=e[t].sources.length,l=e[t].sources[0].length;if(!u({groupIndex:t,col:n-1,row:l-1})){c(o);break}a({groupIndex:t,col:n-1,row:l-1});break}a({...o,col:o.col-1});break}}else{if(!o)return;n.preventDefault();const e=r.current.querySelector(`#${S(o.groupIndex,o.col,o.row)}`);e instanceof HTMLElement&&e.click()}},resetFocusedItem:()=>a(null)}}(L,w,N);(0,n.useLayoutEffect)((()=>{var e;d&&(null===(e=null==R?void 0:R.current)||void 0===e||e.focus())}),[]);const Z=u?n.createElement(u,null):n.createElement(k,{className:A.noResultsDesktop},n.createElement(p.Icon,{icon:m,className:A.emptyIcon}),n.createElement("div",{className:A.emptyText},l.t(null,void 0,r(29673)))),K=!(L.length&&L.every((e=>0===e.sources.length)));return n.createElement(O.SymbolSearchDialogBodyContext.Provider,{value:P},n.createElement(f.DialogSearch,{placeholder:l.t(null,void 0,r(52298)),onChange:function(e){W(),I(e.target.value),N&&N.current&&(N.current.scrollTop=0)},reference:R,onKeyDown:V,onBlur:W,"aria-activedescendant":z}),K?n.createElement("div",{ref:N,className:a()(A.contentList,!c&&A.contentListDesktop),onTouchStart:function(){var e;null===(e=R.current)||void 0===e||e.blur()}},L.map(((e,t)=>{const{group:r,sources:o}=e;return 0===o.length?n.createElement(n.Fragment,{key:r}):n.createElement(n.Fragment,{key:r},!1,n.createElement("div",{className:a()(A.searchSourceItemsContainer,!c&&A.searchSourceItemsContainerDesktop,w&&A.oneColumn)},o.map(((e,r)=>n.createElement("div",{key:`${t}-${r}`,className:A.column},e.map(((e,o)=>n.createElement(D,{id:S(t,r,o),isFocused:!!U&&(U.groupIndex===t&&U.col===r&&U.row===o),key:e.value(),searchSource:e,queryString:C,onClick:$.bind(null,e)}))))))))}))):Z);function $(e){s(e),i("symbolSearch"),o(-1)}}var z=r(962),V=r(45884);r(76861),r(69798);function W(e){return e.hasOwnProperty("exchange")}async function Z(e){{const t=await async function(e){return new Promise((t=>{window.ChartApiInstance.searchSymbols(e.text||"",e.exchange||"",e.type||"","",!1,!0,"",!0,"",(e=>{t(e)}))}))}(e);return{symbols:t,symbols_remaining:0}}}new Map([].map((({value:e,search_type:t})=>[e,t])))
;var K=r(78136),$=r(51768),j=r(68335),G=r(31409),q=r(44254),Y=r(486),H=r(81574),X=r(35119),J=r(32617),ee=r(69135),te=r(63861),re=r(52597);function ne(e){var t;const{state:r,update:o}=e,{searchRef:a,forceUpdate:l,upperCaseEnabled:i}=(0,C.ensureNotNull)((0,n.useContext)(v.SymbolSearchItemsDialogContext)),c=(0,q.tokenize)(null===(t=a.current)||void 0===t?void 0:t.value),d=(0,s.validate)(c);let m=[{icon:Y,insert:"/",type:"binaryOp",name:"division"},{icon:H,insert:"-",type:"binaryOp",name:"subtraction"},{icon:X,insert:"+",type:"binaryOp",name:"addition"},{icon:J,insert:"*",type:"binaryOp",name:"multiplication"}];return u.enabled("hide_exponentiation_spread_operator")||(m=m.concat([{icon:ee,insert:"^",type:"binaryOp",name:"exponentiation"}])),u.enabled("hide_reciprocal_spread_operator")||(m=m.concat([{icon:te,type:"complete",name:"1/x",callback:()=>{!a.current||d.errors.length||d.warnings.length||(a.current.value=(0,s.stringifyTokens)((0,s.flip)(c)),l())}}])),n.createElement("div",{className:re.actions},m.map((e=>n.createElement(G.ToolWidgetButton,{className:re.actionButton,icon:e.icon,key:e.name,isDisabled:oe(e,d),onClick:()=>function(e){var t;if(!oe(e,d)){if(e.insert&&a.current){const t=a.current.value+e.insert;a.current.value=t,a.current.setSelectionRange(t.length,t.length);const[n,,c]=(0,s.getCurrentTokenParamsFromInput)(a.current,i);r.current&&(r.current.selectedIndexValue=-1,r.current.searchSpreadsValue=(0,s.isSpread)(c),r.current.searchTokenValue=n),l(),o()}e.callback&&e.callback(),null===(t=a.current)||void 0===t||t.focus(),(0,$.trackEvent)("GUI","SS",e.name)}}(e)}))))}function oe(e,t){let r=!1;if(!t.errors.length)switch(e.type){case"binaryOp":r="var"===t.currentState;break;case"openBrace":r="var"!==t.currentState;break;case"closeBrace":r="var"===t.currentState&&t.braceBalance>0;break;case"complete":r=!t.errors.length&&!t.warnings.length}return!r}var ae=r(90186),le=r(61371);function se(e){const{title:t,isActive:r,isAnimated:n,activeColor:o,size:l="m",appearance:s="default",fontSize:i="m",grayStyles:c,className:u}=e;return a()(le.bubble,r&&le.active,o&&le[o],t&&"apply-common-tooltip",l&&le[`size-${l}`],i&&le[`fontSize-${i}`],s&&le[`appearance-${s}`],n&&le.animated,c&&le.gray,u)}function ie(e){const{id:t,title:r,tabIndex:o,role:l,contentClassName:s,children:i,onClick:c,onMouseDown:u,reference:d,grayStyles:m,...p}=e;return n.createElement("span",{...(0,ae.filterAriaProps)(p),...(0,ae.filterDataProps)(p),id:t,title:r,tabIndex:o,role:l,className:se(e),onClick:c,onMouseDown:u,ref:d},n.createElement("span",{className:a()(le.content,s)},i))}var ce=r(88389);function ue(e){const{className:t,itemClassName:r,itemContentClassName:a,items:l,getItemTitle:s,getItemTooltip:i,getItemKey:c,checkItemIsActive:u,getItemColor:d,onBubbleClick:m,multiline:p,children:h,BubbleComponent:g=ie,reference:v,fontSize:f,grayStyles:y}=e;return n.createElement("div",{className:o(t,ce.bubbles,p&&ce.multiLine),ref:v},l.map(((e,t)=>n.createElement(g,{key:c?c(e):t,id:c?c(e):t.toString(),className:o(ce.bubble,r),contentClassName:a,onClick:function(){m(e)
},onMouseDown:function(e){e.preventDefault()},isActive:!!u&&u(e),activeColor:d?d(e):void 0,fontSize:f,title:i?i(e):void 0,grayStyles:y},s(e)))),h)}var de=r(63932),me=r(20037),pe=r(29006),he=r(14543),ge=r(10381),ve=r(52019),fe=r(14444);const ye=(0,T.getDefaultSearchSource)();function be(e){const{mode:t,setMode:o,searchRef:s,cachedInputValue:i,setSelectedIndex:c,setSelectedSearchSource:u,isAllSearchSourcesSelected:d,allSearchSourcesTitle:m,upperCaseEnabled:h,symbolSearchContent:g}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),f=g.currentSelectedSearchSource,y=(0,C.ensureNotNull)(f),b="symbolSearch"===t,S=d(y),x=T.isSeparateSymbolSearchTabs&&S&&m?m:y.name(),w=(0,n.useCallback)((()=>{var e;if(T.isSeparateSymbolSearchTabs&&!S&&ye)return u(ye),c(-1),void(null===(e=s.current)||void 0===e||e.focus());s.current&&(i.current=h?s.current.value.toUpperCase():s.current.value),o("exchange")}),[S,s,h,o,u]);return T.isSeparateSymbolSearchTabs?b?n.createElement(he.LightButton,{onClick:w,isPills:!S,size:"xsmall",variant:S?"ghost":"quiet-primary",showCaret:S,endIcon:S?void 0:ve,enableActiveStateStyles:!1,className:a()(fe.button,!S&&fe.withFlag),tabIndex:-1,"data-name":"sources-button"},n.createElement("div",{className:fe.buttonContent},null,n.createElement("span",null,x))):null:b?n.createElement("div",{className:a()(fe.flagWrap,"apply-common-tooltip",!S&&fe.withFlag),title:l.t(null,void 0,r(13269)),onClick:w,"data-name":"sources-button"},S&&n.createElement(p.Icon,{className:fe.icon,icon:R}),null,n.createElement("div",{className:a()(fe.title)},x),n.createElement(ge.ToolWidgetCaret,{className:fe.caret,dropped:!1})):null}var Se=r(37796);function xe(e){const{brokerButton:t=null}=e,{isSmallWidth:o,selectedFilterValues:s,setSelectedFilterValues:i,setSelectedIndex:c,isMobile:u,searchRef:d,symbolSearchContent:m}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),p=m.tabSelectFilters;return T.isSeparateSymbolSearchTabs?n.createElement("div",{className:a()(Se.wrap,Se.small,Se.newStyles,u&&Se.mobile)},t&&n.createElement("div",{className:Se.brokerWrap},t),m.canChangeExchange&&n.createElement("div",{className:Se.filterItem},n.createElement(be,null)),p&&p.map((e=>{const{id:t,options:r,label:o}=e,a=r.find((e=>e.value===FILTER_DEFAULT_VALUE));if(!a)throw new Error("There must be default filter value in filter definition");const l=r.find((e=>{var r;return e.value===(null===(r=s[m.currentSymbolType])||void 0===r?void 0:r[t])}))||a;return n.createElement("div",{key:t,className:Se.filterItem},n.createElement(SymbolSearchSelectFilter,{selectedOption:l,defaultOption:a,options:r,onSelect:e=>{var r;i(m.currentSymbolType,{[t]:e.value}),trackEvent("New SS",m.currentSymbolType,null===e.value?e.analyticsLabel:e.value),c(-1),null===(r=d.current)||void 0===r||r.focus()},label:o,isMobile:u,"data-name":t}))}))):n.createElement("div",{className:a()(Se.wrap,o&&Se.small)},n.createElement("div",{className:Se.item},n.createElement("div",{className:Se.text},o?l.t(null,void 0,r(48490)):l.t(null,void 0,r(89053)))),n.createElement("div",{className:Se.item
},!o&&n.createElement("div",{className:Se.text},l.t(null,void 0,r(29601))),m.canChangeExchange&&n.createElement("div",{className:Se.exchange},n.createElement(be,null))))}var we=r(38223),ke=r(52662);function Ce(e){const{onTouchMove:t,listRef:r,className:o,listWrapRef:l,virtualListKey:s,items:i,getItemSize:c,hideFeed:u,canLoadMore:d,onLoadMoreSymbols:m}=e,{mode:p,isSmallWidth:h,handleListWidth:g}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),[f,y]=(0,n.useState)(null),b=(0,pe.useResizeObserver)((function([e]){y(e.contentRect.height),g(e.contentRect.width)})),S=(0,n.useCallback)((e=>{const{index:t,style:r}=e;return n.createElement("div",{style:r},i[t])}),[i]),x=(0,n.useCallback)((e=>(0,C.ensure)(i[e].key)),[i]),w="watchlist"===p&&null!==f;return n.createElement("div",{className:a()(ke.wrap,w&&ke.watchlist,u&&ke.noFeed,u&&T.isSeparateSymbolSearchTabs&&ke.newStyles,o),onTouchMove:t,ref:b},n.createElement("div",{ref:l,className:a()(ke.scrollContainer,u&&ke.noFeed)},w?n.createElement(me.VariableSizeList,{key:s,ref:r,className:ke.listContainer,width:"100%",height:(0,C.ensureNotNull)(f),itemCount:i.length,itemSize:c,children:S,itemKey:x,overscanCount:20,direction:(0,we.isRtl)()?"rtl":"ltr"}):n.createElement(n.Fragment,null,n.createElement("div",{className:a()(ke.listContainer,h&&ke.multiLineItemsContainer)},!T.isSeparateSymbolSearchTabs&&n.createElement(xe,null),...i,!1))))}var Ee=r(17531),Ie=r(85544);const Le=u.enabled("hide_image_invalid_symbol");function Te(e){const{otherSymbolsCount:t,onChangeSymbolTypeFilter:r,onResetFilters:a,onListTouchMove:l,brokerTitle:s,brokerLogoInfo:i,isBrokerActive:c,onBrokerToggle:u,listRef:d,listWrapRef:m,onLoadMoreSymbols:p,canLoadMore:h}=e,{mode:g,isMobile:f,selectedSymbolType:y,symbolTypes:b,feedItems:S,contentItem:x,emptyState:w=Re,symbolSearchContent:k,symbolSearchState:C}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),I=s?n.createElement(BrokerButton,{brokerTitle:s,isActive:c,onToggle:u,logoInfo:i}):null,L="symbolSearch"===g&&["good","loadingWithPaginated"].includes(C),R=null!=x?x:Ee.SymbolSearchDialogContentItem,N=(0,n.useMemo)((()=>S.map((e=>n.createElement(R,{...e,searchToken:k.token})))),[S]);return n.createElement(n.Fragment,null,"symbolSearch"===g&&n.createElement(n.Fragment,null,b.length>0&&n.createElement(ue,{className:o(T.isSeparateSymbolSearchTabs&&(k.withFilters||f&&I)&&Ie.withFilters,!f&&I&&Ie.withButton),itemClassName:Ie.symbolType,items:b,getItemTitle:e=>e.name,getItemKey:e=>e.value,checkItemIsActive:e=>e.value===y,onBubbleClick:r,multiline:!f,grayStyles:!0},!f&&n.createElement("div",{className:Ie.brokerButton},I)),!T.isSeparateSymbolSearchTabs&&f&&b.length>0&&s&&n.createElement("div",{className:Ie.brokerButtonWrap},I),T.isSeparateSymbolSearchTabs&&n.createElement(xe,{brokerButton:f?I:void 0})),n.createElement(Ce,{listRef:d,listWrapRef:m,onTouchMove:l,items:N,getItemSize:()=>De,onLoadMoreSymbols:p,canLoadMore:h,hideFeed:!L}),"loading"===C&&n.createElement("div",{className:Ie.spinnerWrap
},n.createElement(de.Spinner,null)),"symbolSearch"===g&&n.createElement(n.Fragment,null,!1,"empty"===C&&n.createElement(w,null)))}function Re(e){const t=(0,_.useWatchedValueReadonly)({watchedValue:B.watchedTheme})===M.StdTheme.Dark?Q:F;return n.createElement(k,{className:Ie.noResultsDesktop},!Le&&n.createElement(p.Icon,{icon:t,className:Ie.emptyIcon}),n.createElement("div",{className:Ie.emptyText},l.t(null,void 0,r(41379))))}const Ne=(0,T.getDefaultSearchSource)(),De=52;function _e(e){const{mode:t,setMode:o,setSelectedIndex:i,isMobile:c,selectedSearchSource:d,setSelectedSearchSource:m,isAllSearchSourcesSelected:p,selectedSymbolType:h,setSelectedSymbolType:g,symbolSearchContent:y,setSymbolSearchContent:b,searchRef:S,setSearchSpreads:x,showSpreadActions:w,selectedItem:k,forceUpdate:C,placeholder:I,initialScreen:L,footer:R,searchInput:N,upperCaseEnabled:D,externalInput:_,handleKeyDown:B,customSearchSymbols:M,filterDefinitions:Q,filterQueryParams:F,searchSources:A,symbolSearchState:P,setSymbolSearchState:U,onEmptyResults:G}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext),q=(0,n.useRef)(t);q.current=t;const Y=(0,n.useRef)(new AbortController),[H,X]=(0,n.useState)(0),J=(0,n.useRef)(0),[ee,te]=(0,n.useState)(y.token),re=(0,n.useRef)(null),oe=(0,n.useRef)(null),ae=(0,n.useRef)({selectedIndexValue:-1,searchTokenValue:"",searchSpreadsValue:!0}),le=(0,n.useRef)(null),se=(0,n.useRef)(null),ie=(0,n.useRef)(null),{broker:ce=null,brokerId:ue,brokerTitle:de,brokerLogoInfo:me,isBrokerChecked:pe=!1,setIsBrokerChecked:he=(()=>{}),unhideSymbolSearchGroups:ge=""}={brokerId:void 0,brokerTitle:void 0,brokerLogoInfo:void 0};(0,n.useEffect)((()=>()=>{Y.current.abort(),Be(),Me()}),[]),(0,n.useEffect)((()=>{(null==S?void 0:S.current)&&te(S.current.value)}),[]),(0,n.useEffect)((()=>{const e=S.current;if(e)return e.addEventListener("input",we),e.addEventListener("focus",De),e.addEventListener("select",xe),e.addEventListener("click",xe),e.addEventListener("keyup",_e),_&&B&&e.addEventListener("keydown",B),()=>{e&&(e.removeEventListener("input",we),e.removeEventListener("focus",De),e.removeEventListener("select",xe),e.removeEventListener("click",xe),e.removeEventListener("keyup",_e),_&&B&&e.removeEventListener("keydown",B))}}),[B]),(0,n.useEffect)((()=>{Boolean(L)&&""===ee.trim()||(b((e=>({...e,symbolStartIndex:0}))),Ce(ee,h,d).then((()=>{re.current&&(re.current.scrollTop=0)})))}),[ee,h,d,pe,L,F]),(0,n.useEffect)((()=>{var e;if(!k||!S.current)return;if(!u.enabled("show_spread_operators"))return S.current.value=k.symbol,void C();const t=W(k)?k.exchange:k.parent.exchange;let r;r="contracts"in k&&(null===(e=k.contracts)||void 0===e?void 0:e.length)?k.contracts[0]:k;const n={name:r.symbol,exchange:t,prefix:r.prefix,fullName:r.full_name},[o,a]=(0,s.getNextSymbolInputValueAndPosition)(S.current,n,D);S.current.value=o,S.current.setSelectionRange(a,a),C()}),[k]);const ve=null!=L?L:"div",fe=Boolean(L)&&"symbolSearch"!==t,ye=null!=N?N:f.DialogSearch,be=(0,n.useMemo)((()=>({listRef:oe,resetRecommends:Re,updateRecommends:Ce,searchToken:ee,
emptyTextClassName:Ie.emptyText,isBrokerChecked:pe,symbolSearchState:P,currentMode:q})),[oe,ee,pe,P,q,F]);return n.createElement(O.SymbolSearchDialogBodyContext.Provider,{value:be},!(_&&"symbolSearch"===t)&&n.createElement(ye,{reference:S,className:a()(Ie.search,D&&Ie.upperCase),placeholder:I||l.t(null,void 0,r(52298))},w&&n.createElement(ne,{state:ae,update:ke})),fe?n.createElement(ve,null):n.createElement(Te,{otherSymbolsCount:H,onListTouchMove:function(){var e;null===(e=S.current)||void 0===e||e.blur()},onChangeSymbolTypeFilter:function(e){const{value:t}=e;g(t),i(-1)},onResetFilters:function(){var e;T.isSeparateSymbolSearchTabs?"resetFilter"===P?g((0,T.getAllSymbolTypesValue)()):Ne&&m(Ne):(g((0,T.getAllSymbolTypesValue)()),Ne&&m(Ne));he(!1),c||null===(e=S.current)||void 0===e||e.focus()},brokerTitle:de,brokerLogoInfo:me,isBrokerActive:pe,onBrokerToggle:he,listRef:oe,listWrapRef:re,onLoadMoreSymbols:void 0,canLoadMore:void 0}),R);function Se(){if(!S.current)return;const[e,t,r]=(0,s.getCurrentTokenParamsFromInput)(S.current,D);J.current=t,ae.current={selectedIndexValue:-1,searchSpreadsValue:(0,s.isSpread)(r),searchTokenValue:e},le.current||(le.current=setTimeout(ke,0))}function xe(){if(!S.current)return;const[,e]=(0,s.getCurrentTokenParamsFromInput)(S.current,D);e!==J.current&&Se()}function we(){u.enabled("show_spread_operators")?Se():S.current&&(ae.current={selectedIndexValue:-1,searchSpreadsValue:!1,searchTokenValue:S.current.value},le.current||(le.current=setTimeout(ke,0)))}function ke(){const{selectedIndexValue:e,searchTokenValue:t,searchSpreadsValue:r}=ae.current;le.current=null,(0,z.unstable_batchedUpdates)((()=>{x(r),i(e),te(D?t.toUpperCase():t)}))}async function Ce(e,t,r,n){var o,a,l;try{"noop"===P?U("loading"):n?U("loadingWithPaginated"):(Be(),Me(),se.current=setTimeout((()=>{b({token:e,canChangeExchange:Boolean(d&&A.length>1&&!(0,T.exchangeSelectDisabled)(t)),tabSelectFilters:null==Q?void 0:Q[t],withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:A,renderSymbolSearchList:[],symbolsRemaining:0,symbolStartIndex:0}),U("loading")}),500)),Oe();(0,T.getAllSymbolTypesValue)();const i=!1;let c;if(pe&&ce){c=(await(0,V.respectAbort)(Y.current.signal,ce.accountMetainfo())).prefix}const m=u.enabled("show_spread_operators")?null!==(a=null!==(o=(0,s.getExchange)(e))&&void 0!==o?o:c)&&void 0!==a?a:null==r?void 0:r.getRequestExchangeValue():null==d?void 0:d.getRequestExchangeValue(),p=(0,s.getExchange)(e)||null===(l=r||d)||void 0===l?void 0:l.getRequestCountryValue(),[h,g]=await Promise.all([Le(Y.current.signal,e,t,r,m,p,n),i&&!n?getRecent():Promise.resolve([])]),v=g.filter((e=>{var t,r;return m?(null===(t=e.exchange)||void 0===t?void 0:t.toLowerCase())===m.toLowerCase():!p||(null===(r=e.country)||void 0===r?void 0:r.toLowerCase())===p.toLowerCase()})),f=new Set(v.map((e=>`${e.exchange}_${e.symbol}`))),S=h.symbols.filter((e=>!f.has(`${e.exchange}_${e.symbol}`)));let x=function(e,t=window.ChartApiInstance.symbolsGrouping()){var r;const n={},o=[];for(let a=0;a<e.length;++a){
const l=e[a];if(l.prefix||Array.isArray(l.contracts))return e;const s=t[l.type];if(void 0===s){o.push(l);continue}const i=s.exec(l.symbol);if(i){const e=i[1];let t;n.hasOwnProperty(e)?t=n[e]:(t=o.length,n[e]=t,o.push({type:l.type,symbol:e,exchange:l.exchange,description:l.description,full_name:l.exchange+":"+e,contracts:[]})),null===(r=o[t].contracts)||void 0===r||r.push(l)}else o.push(l)}return o}([...v,...S]);if(n&&(x=[...y.renderSymbolSearchList,...x]),!x.length)return b((r=>({...r,canChangeExchange:Boolean(d&&A.length>1&&!(0,T.exchangeSelectDisabled)(t)),tabSelectFilters:null==Q?void 0:Q[t],token:e,symbolsRemaining:0,withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:A}))),Be(),U("empty"),void Ee();Be(),b((r=>({...r,canChangeExchange:Boolean(d&&A.length>1&&!(0,T.exchangeSelectDisabled)(t)),tabSelectFilters:null==Q?void 0:Q[t],renderSymbolSearchList:x,token:e,symbolsRemaining:h.symbols_remaining,withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:A,symbolStartIndex:r.symbolStartIndex+h.symbols.length}))),U("good")}catch(e){(0,V.skipAbortError)(e)}}function Ee(){G&&(ie.current=setTimeout((()=>G()),1e3))}async function Le(e,t,r,n,o,a,l){var i;const c={serverHighlight:!1,text:u.enabled("show_spread_operators")?(0,s.shortName)(t):null===(i=S.current)||void 0===i?void 0:i.value,exchange:o,country:a,type:r,lang:window.language||"",sortByCountry:void 0,brokerId:ue,onlyTradable:Boolean(ue)&&pe,unhideSymbolSearchGroups:ge,signal:e,start:l,filterQueryParams:F},d=(0,K.getSearchRequestDelay)();return void 0!==d&&await(0,V.delay)(e,d),M?M(c):Z(c)}function Re(){Oe(),U("empty"),te(""),x(!1),b((e=>({...e,symbolStartIndex:0}))),Be()}function De(){"watchlist"===q.current&&(o("symbolSearch"),(0,$.trackEvent)("Watchlist","Mobile SS","Go to SS page"))}function _e(e){switch((0,j.hashFromEvent)(e)){case 37:case 39:xe()}}function Be(){se.current&&clearTimeout(se.current)}function Me(){ie.current&&clearTimeout(ie.current)}function Oe(){Y.current.abort(),Y.current=new AbortController}}var Be=r(48199),Me=r(24658),Oe=r(58442),Qe=r(56840);function Fe(e){const[t,r]=(0,n.useState)((()=>{const{defaultSearchSource:t,searchSources:r}=e,n=Qe.getValue("symboledit.exchangefilter","");return r.find((e=>e.value()===n))||t}));return[t,(0,n.useCallback)((e=>{var t;r(e),t=e,Qe.setValue("symboledit.exchangefilter",t.value())}),[])]}function Ae(e){const[t,r]=(0,n.useState)((()=>{if(1===e.types.length)return e.types[0].value;const t=Qe.getValue("symboledit.filter",(0,T.getAllSymbolTypesValue)());return e.types.find((e=>e.value===t))?t:(0,T.getAllSymbolTypesValue)()}));return[t,(0,n.useCallback)((e=>{var t;r(e),t=e,Qe.setValue("symboledit.filter",t)}),[])]}var Pe=r(36947),Ue=r(82708),ze=r(88145),Ve=r(76460),We=r(62393);const Ze=(0,T.getAvailableSearchSources)(),Ke=(0,T.getDefaultSearchSource)(),$e=u.enabled("uppercase_instrument_names");function je(e){var t
;const{onClose:o,initialMode:a,defaultValue:m="",showSpreadActions:p,hideMarkedListFlag:h,selectSearchOnInit:g=!0,onSearchComplete:f,dialogTitle:y=l.t(null,void 0,r(99983)),placeholder:S,fullscreen:w,initialScreen:k,wrapper:C,dialog:E,contentItem:I,footer:L,searchInput:R,emptyState:N,autofocus:D,dialogWidth:_,onKeyDown:B,searchSourcesScreen:M,customSearchSymbols:O,isDisableFiltering:Q,disableRecents:F,shouldReturnFocus:A,onSymbolFiltersParamsChange:P,onEmptyResults:z}=e,V=(0,n.useMemo)((()=>Q?[]:e.symbolTypes?e.symbolTypes:(0,T.getAvailableSymbolTypes)()),[]),K=void 0!==e.input,$=Q?[]:Ze,[G,q]=(0,n.useState)(a),Y=(0,n.useRef)(m),[H,X]=Fe({searchSources:$,defaultSearchSource:Ke}),[J,ee]=[],[te,re]=Ae({types:V}),[ne,oe]=[{},()=>{}],[ae,le]=(0,n.useState)(!1),[se,ie]=(0,n.useState)(-1),[ce,ue]=(0,n.useState)("noop"),de=T.isSeparateSymbolSearchTabs?TAB_SELECT_FILTER_MAP:void 0,me=T.isSeparateSymbolSearchTabs?(null==J?void 0:J[te])||Ke:H,pe=(0,n.useMemo)((()=>{if(!T.isSeparateSymbolSearchTabs)return $;return $.filter((e=>{const t=TAB_SOURCE_FILTER_MAP[te];if(!t)return!1;if(!te)return!0;const r=e.group();return r===ExchangeGroup.AllExchanges||r&&t.value.includes(r)}))}),[$,te]),[he,ge]=(0,n.useState)((()=>({canChangeExchange:Boolean(H&&Ze.length>1&&!(0,T.exchangeSelectDisabled)(te)),tabSelectFilters:null==de?void 0:de[te],withFilters:!!te,renderSymbolSearchList:[],token:Y.current,symbolsRemaining:0,currentSymbolType:te,currentSelectedSearchSource:me,currentTabAvailableSearchSources:pe,symbolStartIndex:0}))),ve=(0,n.useCallback)((e=>{trackEvent("New SS",te,"Change sources"),null==ee||ee(te,e),ge((t=>({...t,currentSelectedSearchSource:e})))}),[te,ge]),fe=(0,n.useRef)(null!==(t=e.input)&&void 0!==t?t:null),[ye,be]=(0,n.useState)(!1),Se=(0,Pe.useForceUpdate)(),[xe,ke]=(0,n.useState)(new Set),{broker:Ce=null,brokerId:Ee,unhideSymbolSearchGroups:Ie="",displayBrokerSymbol:Le=!1}={brokerId:void 0};(0,n.useLayoutEffect)((()=>{var e;!(null==fe?void 0:fe.current)||!K&&Boolean(null===(e=fe.current)||void 0===e?void 0:e.value)||(K||"compare"===G||(fe.current.value=Y.current),!D||K&&"symbolSearch"!==G||fe.current.focus())}),[G]),(0,n.useEffect)((()=>{(null==fe?void 0:fe.current)&&g&&D&&fe.current.select()}),[]);const Te=(0,n.useMemo)((()=>he.renderSymbolSearchList.reduce(((e,t)=>{const r=Ye(t),n=xe.has(r);return e.push(t),n&&t.contracts&&e.push(...t.contracts.map((e=>({...e,parent:t})))),e}),[])),[he.renderSymbolSearchList,xe]),Re=(0,n.useRef)(null);(0,n.useEffect)((()=>{var e;-1!==se&&(null===(e=Re.current)||void 0===e||e.scrollIntoView({block:"nearest"}))}),[se,Re]);const Ne=i.includes(te),De=(0,n.useMemo)((()=>Te.map(((e,t)=>{var r,n,o,a;if(W(e)){const o=Ye(e),a=e.contracts?xe.has(o):void 0,l=t===se,s=he.renderSymbolSearchList.findIndex((t=>t.symbol===e.symbol&&t.exchange===e.exchange))+1;return{key:t,numberInList:s,id:o,title:qe(e,Le),description:e.description,isOffset:!1,onClick:lt.bind(null,e,s),providerId:e.provider_id,source:e.source,source2:e.source2,country:null===(r=e.country)||void 0===r?void 0:r.toLocaleLowerCase(),
type:e.type,exchangeName:null===e.exchange?void 0:e.exchange,exchangeTooltip:"",prefix:e.prefix||void 0,marketType:(0,Me.marketType)(e.type,e.typespecs,!1),hideMarketType:Ne,isEod:(null===(n=e.params)||void 0===n?void 0:n.includes("eod"))&&"economic"!==e.type,isYield:(0,ze.isYield)(e),isExpanded:a,onExpandClick:e.contracts?st.bind(null,o):void 0,fullSymbolName:e.contracts?Oe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Oe.QualifiedSources.fromSymbolSearchResult(e),itemRef:l?Re:void 0,isSelected:t===se,hideMarkedListFlag:h,item:e,logoId:e.logoid,currencyLogoId:e["currency-logoid"],baseCurrencyLogoId:e["base-currency-logoid"],shortName:(0,Ue.safeShortName)(Oe.QualifiedSources.fromSymbolSearchResult(e)),currencyCode:e.currency_code,isPrimary:e.is_primary_listing}}{const{parent:r}=e,n=Ye(r),l=t===se,s=he.renderSymbolSearchList.findIndex((e=>e.symbol===r.symbol&&e.exchange===r.exchange))+1;return{key:t,numberInList:s,id:n+e.symbol,dangerousTitleHTML:qe(e,Le),dangerousDescriptionHTML:`${r.description}`+(e.description?` (${e.description})`:""),isOffset:!0,isEod:null===(o=e.params)||void 0===o?void 0:o.includes("eod"),isYield:(0,ze.isYield)(e),onClick:it.bind(null,e.parent,e,s),providerId:r.provider_id,country:null===(a=r.country)||void 0===a?void 0:a.toLowerCase(),type:r.type,exchangeName:null===r.exchange?void 0:r.exchange,exchangeTooltip:"",marketType:(0,Me.marketType)(r.type,e.typespecs,!1),hideMarketType:Ne,fullSymbolName:Oe.QualifiedSources.fromSymbolSearchResult(e.parent,e),itemRef:l?Re:void 0,isSelected:l,hideMarkedListFlag:h,item:e}}}))),[he.renderSymbolSearchList,xe,G,se,B]),Qe=(0,n.useMemo)((()=>function(e,t,r){const n=null==t?void 0:t[e],o=new Map(null==n?void 0:n.map((e=>[e.id,e.urlParam]))),a=r[e];let l;if(a){l={};for(const[e,t]of Object.entries(a)){const r=o.get(e);r&&(l[r]=t)}}return l}(te,de,ne)),[te,de,ne]),je=(0,n.useMemo)((()=>he.renderSymbolSearchList.slice(0,20).map((e=>e.contracts?Oe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Oe.QualifiedSources.fromSymbolSearchResult(e)))),[he.renderSymbolSearchList]);(0,n.useEffect)((()=>{var e,t,r;if(!P)return;const n=["resetFilter","resetTabFilter","empty"].includes(ce)?[]:je,o={...Qe,result_list:n};if(o.search_type||(o.search_type="bitcoin,crypto"===te?"crypto":te),!T.isSeparateSymbolSearchTabs)return o.exchange=null!==(e=null==me?void 0:me.getRequestCountryValue())&&void 0!==e?e:null,void P(o);if(te){const e=null!==(t=null==me?void 0:me.getRequestCountryValue())&&void 0!==t?t:null;e&&(o.country=e);const n=null!==(r=null==me?void 0:me.getRequestExchangeValue())&&void 0!==r?r:null;n&&(o.exchange=n)}P(o)}),[te,Qe,je,me,ce]);const Je=null!=E?E:Xe,et=Je!==Xe&&!K,tt=(e,t)=>{var r;return{mode:G,setMode:q,selectedSearchSource:me,setSelectedSearchSource:T.isSeparateSymbolSearchTabs?ve:X,isAllSearchSourcesSelected:x.isAllSearchSourcesSelected,allSearchSourcesTitle:T.isSeparateSymbolSearchTabs?null===(r=TAB_SOURCE_FILTER_MAP[he.currentSymbolType])||void 0===r?void 0:r.allSearchSourcesTitle:void 0,selectedSymbolType:te,setSelectedSymbolType:re,
selectedIndex:se,setSelectedIndex:ie,onClose:o,setSymbolSearchContent:ge,symbolSearchContent:he,searchRef:fe,cachedInputValue:Y,searchSpreads:ae,setSearchSpreads:le,handleListWidth:ct,isSmallWidth:ye,feedItems:De,isMobile:e,showSpreadActions:p,selectSearchOnInit:g,isTablet:t,selectedItem:Te[se],forceUpdate:Se,placeholder:S,initialScreen:k,toggleExpand:st,openedItems:xe,onSubmit:mt,onSearchComplete:f,footer:L,symbolTypes:V,contentItem:I,searchInput:R,emptyState:N,autofocus:D,upperCaseEnabled:$e,externalInput:K,handleKeyDown:et?void 0:dt,customSearchSymbols:O,searchSources:pe,filterDefinitions:de,selectedFilterValues:ne,setSelectedFilterValues:oe,filterQueryParams:Qe,symbolSearchState:ce,setSymbolSearchState:ue,onEmptyResults:void 0}},rt=null!=M?M:U,nt="exchange"===G,ot=nt?{title:l.t(null,void 0,r(19724)),dataName:"exchanges-search",render:()=>n.createElement(rt,{searchSources:he.currentTabAvailableSearchSources}),additionalHeaderElement:n.createElement(Be.BackButton,{onClick:()=>q("symbolSearch"),className:We.backButton,size:"medium","aria-label":l.t(null,{context:"input"},r(16936)),preservePaddings:!0,flipIconOnRtl:(0,we.isRtl)()}),additionalElementPos:"before"}:{title:y,dataName:"symbol-search-items-dialog",render:()=>n.createElement(_e,null),additionalElementPos:"after"},at=null!=C?C:"div";return n.createElement(at,null,n.createElement(c.MatchMediaMap,{rules:d.DialogBreakpoints},(({TabletSmall:e,TabletNormal:t})=>n.createElement(v.SymbolSearchItemsDialogContext.Provider,{value:tt(e,t)},n.createElement(Je,{...ot,shouldReturnFocus:A,fullScreen:w,onClose:o,onClickOutside:o,onKeyDown:et?void 0:dt,isOpened:!0})))));function lt(e,t,r){if(e.contracts)return e.contracts.length?void it(e,e.contracts[0],t,r):void st(Ye(e));it(e,void 0,t,r)}function st(e){const t=new Set(xe);t.has(e)?t.delete(e):t.add(e),ke(t)}function it(e,t,r,n){const a=t||e,{exchange:l}=e;if(u.enabled("show_spread_operators")){const e={name:a.symbol,exchange:l,prefix:a.prefix,fullName:a.full_name};if(ae)return ut(e),void Se();if(fe.current&&fe.current.value.includes(","))return void ut(e)}pt([{resolved:!0,symbol:Oe.QualifiedSources.fromSymbolSearchResult(e,t),result:a}],r,n),o()}function ct(e){be("fixed"===_||e<=640)}function ut(e){if(!fe.current)return;const[t,r]=(0,s.getNextSymbolInputValueAndPosition)(fe.current,e,$e);fe.current.value=t,fe.current.setSelectionRange(r,r),fe.current.focus()}function dt(e){const t=(0,j.hashFromEvent)(e);switch(t){case 13:e.preventDefault(),mt(!0);break;case 27:if(e.preventDefault(),nt)return void q("symbolSearch");o()}switch((0,b.mapKeyCodeToDirection)(t)){case"blockPrev":if(e.preventDefault(),0===se||"good"!==ce)return;if(-1===se)return void ie(0);ie(se-1);break;case"blockNext":if(e.preventDefault(),se===De.length-1||"good"!==ce)return;ie(se+1);break;case"inlinePrev":{if(-1===se)return;const t=De[se],{id:r,isOffset:n,onExpandClick:o}=t;if(!n&&r&&xe.has(r)&&Boolean(o)&&!Boolean(B)&&(e.preventDefault(),st(r)),o)return void(null==B||B(e,!0));break}case"inlineNext":{if(-1===se)return
;const t=De[se],{id:r,isOffset:n,onExpandClick:o}=t;if(n||!r||xe.has(r)||!Boolean(o)||Boolean(B)||(e.preventDefault(),st(r)),o)return void(null==B||B(e,!0));break}}null==B||B(e)}function mt(e){if(!fe.current)return;let t=fe.current.value;if(u.enabled("show_spread_operators")&&ae&&t){const r=De[se];if(r&&void 0!==r.isExpanded&&(r.onClick(),t=fe.current.value),t.includes(",")){return pt(He(t).map(Ge),null),void(e&&o())}return pt([{symbol:$e?t.toUpperCase():t,resolved:!1}],null),void(e&&o())}if(t.includes(","))return pt(He(t).map(Ge),null),void(e&&o());if(-1!==se){De[se].onClick()}else{const r=$e?t.toUpperCase():t;if(r&&""!==r.trim()){const e=He(r);if(void 0!==Ee&&-1===r.indexOf(":"))(function(e){let t=!1;return Promise.all(e.map((e=>-1!==e.indexOf(":")||t?Promise.resolve({symbol:e,resolved:!1}):(t=!0,async function(e){var t;null===(t=await(null==Ce?void 0:Ce.accountMetainfo()))||void 0===t||t.prefix;const r=void 0,n=await Z({strictMatch:!0,serverHighlight:!1,text:e,lang:window.language||"",brokerId:Ee,onlyTradable:!0,unhideSymbolSearchGroups:Ie,exchange:r});if(0!==n.symbols.length){const e=n.symbols[0],{contracts:t}=e,r=t&&t.length>0?t[0]:void 0,o=e.prefix||e.exchange,a=r?r.symbol:e.symbol;if(o&&a)return{symbol:Oe.QualifiedSources.fromSymbolSearchResult(e,r),resolved:!0,result:e}}return{symbol:e,resolved:!1}}(e)))))})(e).then((e=>pt(e,null)));else{pt(e.map(Ge),null)}}e&&o()}}async function pt(e,t,r){var n;const[{result:o,symbol:a,resolved:l}]=e,s=null===(n=fe.current)||void 0===n?void 0:n.value,i=!r||(0,Ve.isKeyboardClick)(r);let c=ae;void 0!==o&&W(o)&&(c="spread"===o.type),f(e,{symbolType:te,isKeyboardEvent:i,numberInList:t,inputValue:s,isSpread:c})}}function Ge(e){return{symbol:$e?e.toUpperCase():e,resolved:!1}}function qe(e,t){const{broker_symbol:r,symbol:n,description:o}=e;return`${"spread"===e.type?o:n}${t&&r?` (${r})`:""}`}function Ye(e){return e.symbol+e.exchange+e.description}function He(e){return e.split(",").map((e=>e.trim())).filter((e=>""!==e))}function Xe(e){const{isMobile:t,isTablet:r}=(0,E.useEnsuredContext)(v.SymbolSearchItemsDialogContext);return n.createElement(m.AdaptivePopupDialog,{...e,className:a()(We.dialog,!t&&(r?We.tabletDialog:We.desktopDialog)),backdrop:!0,draggable:!1})}},15983:(e,t,r)=>{"use strict";r.d(t,{flip:()=>s,getCurrentTokenParamsFromInput:()=>v,getExchange:()=>p,getNextSymbolInputValueAndPosition:()=>g,isSpread:()=>u,shortName:()=>m,stringifyTokens:()=>i,validate:()=>l});var n=r(14483),o=r(44254),a=r(81319);function l(e){const t={braceBalance:0,currentState:"var",warnings:[],errors:[]};if(n.enabled("charting_library_base")&&!n.enabled("show_spread_operators"))return t;let r="init";const o=[];for(let n=0;n<e.length;n++){const a=e[n];if("whitespace"!==a.type){if("incompleteSymbol"===a.type||"incompleteNumber"===a.type){const r=n!==e.length-1,o={status:r?"error":"incomplete",reason:"incomplete_token",offset:a.offset,token:a};if(r?t.errors.push(o):t.warnings.push(o),r)continue}switch(a.type){case"symbol":case"number":if("var"===r){t.errors.push({status:"error",
reason:"unexpected_token",offset:a.offset,token:a});continue}r="var";break;case"plus":case"minus":case"multiply":case"divide":case"power":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}r="operator";break;case"openBrace":if("var"===r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}o.push(a),r="init";break;case"closeBrace":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}o.pop()||t.errors.push({status:"error",reason:"unbalanced_brace",offset:a.offset,token:a}),r="var";break;case"unparsed":t.errors.push({status:"error",reason:"unparsed_entity",offset:a.offset,token:a})}}}for(t.braceBalance=o.length,"var"!==r&&t.warnings.push({status:"incomplete",token:e[e.length-1]});o.length;){const e=o.pop();e&&t.warnings.push({status:"incomplete",reason:"unbalanced_brace",offset:e.offset,token:e})}return t.currentState=r,t}function s(e){const t=function(e){let t,r=0,n=0;for(let o=0;o<e.length;o++){const a=e[o];if("whitespace"!==a.type)switch(r){case 0:if("number"!==a.type||1!=+a.value)return[];r=1;break;case 1:if(1!==r||"divide"!==a.type)return[];r=2,t=o+1;break;case 2:if("openBrace"===a.type)r=3,n=1;else if(c(a.type))return[];break;case 3:"openBrace"===a.type?n++:"closeBrace"===a.type&&(n--,n<=0&&(r=2))}}return e.slice(t)}(e);return t.length?d(t):d((0,o.tokenize)("1/("+i(e)+")"))}function i(e){return e.reduce(((e,t)=>"symbol"===t.type&&o.symbolTokenEscapeRe.test(t.value)?e+`'${t.value}'`:e+t.value),"")}function c(e){return"plus"===e||"minus"===e||"multiply"===e||"divide"===e||"power"===e}function u(e){return e.length>1&&e.some((e=>c(e.type)))}function d(e){e=function(e){const t=[];for(const r of e)"whitespace"!==r.type&&t.push(r);return t}(e);const t=[],r=[];let n;for(let o=0;o<e.length;o++){const a=e[o];switch(a.type){case"plus":case"minus":case"multiply":case"divide":case"power":r.length&&r[r.length-1].minPrecedence>a.precedence&&(r[r.length-1].minPrecedence=a.precedence);break;case"openBrace":n={minPrecedence:1/0,openBraceIndex:o},r.push(n);break;case"closeBrace":{if(n=r.pop(),!n)break;const a=e[n.openBraceIndex-1],l=e[o+1],s=a&&("plus"===a.type||"multiply"===a.type);(!c(null==l?void 0:l.type)||(null==l?void 0:l.precedence)<=n.minPrecedence)&&(!c(null==a?void 0:a.type)||(null==a?void 0:a.precedence)<(null==n?void 0:n.minPrecedence)||(null==a?void 0:a.precedence)===(null==n?void 0:n.minPrecedence)&&s)&&(t.unshift(n.openBraceIndex),t.push(o),r.length&&r[r.length-1].minPrecedence>n.minPrecedence&&(r[r.length-1].minPrecedence=n.minPrecedence))}}}for(let r=t.length;r--;)e.splice(t[r],1);return e}function m(e){return d((0,o.tokenize)(e)).reduce(((e,t)=>{if("symbol"!==t.type)return e+t.value;const[,r]=h(t);return r?e+r:e}),"")}function p(e){const t=function(e){const t=(0,o.tokenize)(e),r=[];return t.forEach((e=>{if("symbol"!==e.type)return;const[t]=h(e);t&&r.push(t)})),r}(e);if(1===t.length)return t[0]}function h(e){const t=/^'?(?:([A-Z0-9_]+):)?(.*?)'?$/i.exec(e.value)
;return null===t?[void 0,void 0]:[t[1],t[2]]}function g(e,t,r){const n=e.value,[l,s]=v(e,r),i=(0,a.getSymbolFullName)(t),c=o.symbolTokenEscapeRe.test(i)?`'${i}'`:i;return[n.substring(0,s)+c+n.substring(s+l.length),s+c.length]}function v(e,t){const{value:r,selectionStart:n}=e,a=(0,o.tokenize)(t?r.toUpperCase():r),l=function(e,t){for(let r=0;r<e.length;r++){const n=e[r],o="symbol"===n.type||"incompleteSymbol"===n.type||"number"===n.type;if(n.offset<=t&&t<=n.offset+n.value.length&&o)return n}return null}(a,n||0);return[(null==l?void 0:l.value)||"",l?l.offset:r.length,a]}},81319:(e,t,r)=>{"use strict";r.d(t,{createGroupColumns:()=>p,exchangeSelectDisabled:()=>m,getAllSymbolTypesValue:()=>d,getAvailableSearchSources:()=>c,getAvailableSymbolTypes:()=>u,getDefaultSearchSource:()=>i,getSymbolFullName:()=>s,isSeparateSymbolSearchTabs:()=>h});var n=r(11542),o=r(20882);class a{constructor(e){this._exchange=e}value(){return this._exchange.value}name(){return(0,o.isAllSearchSourcesSelected)(this)?n.t(null,void 0,r(64498)):this._exchange.name}description(){return this._exchange.desc}country(){return this._exchange.country}providerId(){return this._exchange.providerId}group(){return this._exchange.group}includes(e){return function(e,t){const r=t.toLowerCase(),{name:n,desc:o,searchTerms:a}=e;return n.toLowerCase().includes(r)||o.toLowerCase().includes(r)||void 0!==a&&a.some((e=>e.toLowerCase().includes(r)))}(this._exchange,e)}getRequestExchangeValue(){return this._exchange.value}getRequestCountryValue(){}}var l=r(3685);function s(e){if(e.fullName)return e.fullName;let t;return t=e.prefix||e.exchange?(e.prefix||e.exchange)+":"+e.name:e.name,t.replace(/<\/?[^>]+(>|$)/g,"")}function i(){const e=c();return e.find(o.isAllSearchSourcesSelected)||e[0]||null}function c(){return(0,o.createSearchSources)(a,(0,l.getExchanges)())}function u(){return window.ChartApiInstance.supportedSymbolsTypes()}function d(){return""}function m(e){return!!h&&!TAB_SOURCE_FILTER_MAP[e]}function p(e,t=2){if(0===e.length)return[];if(1===t)return[e];const r=Math.floor(e.length/2)+e.length%2;return[e.slice(0,r),e.slice(r)].filter((e=>e.length>0))}const h=!1},82708:(e,t,r)=>{"use strict";r.d(t,{safeShortName:()=>o});var n=r(79982);function o(e){try{return(0,n.shortName)(e)}catch(t){return e}}},44254:(e,t,r)=>{"use strict";r.d(t,{symbolTokenEscapeRe:()=>l,tokenize:()=>c});var n=r(14483),o=r(18429);const a=n.enabled("charting_library_base")?/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF^])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!:.&])*|'.+?'/:/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!|:.&])*|'.+?'/,l=/[+\-/*]/,s={number:/\d+(?:\.\d*|(?![a-zA-Z0-9_!:.&]))|\.\d+/,incompleteNumber:/\./,symbol:a,incompleteSymbol:/'[^']*/,separatorPrefix:o.SEPARATOR_PREFIX,openBrace:"(",closeBrace:")",plus:"+",minus:"-",multiply:"*",divide:"/",power:"^",whitespace:/[\0-\x20\s]+/,unparsed:null},i=new RegExp(Object.values(s).map((e=>{return null===e?"":`(${"string"==typeof e?(t=e,
t.replace(/[\^$()[\]{}*+?|\\]/g,"\\$&")):e.source})`;var t})).filter((e=>""!==e)).concat(".").join("|"),"g");function c(e){if(!e)return[];const t=[],r=Object.keys(s);let n;for(;n=i.exec(e);){let e=!1;for(let o=r.length;o--;)if(n[o+1]){r[o]&&t.push({value:n[o+1],type:r[o],precedence:0,offset:n.index}),e=!0;break}e||t.push({value:n[0],type:"unparsed",precedence:0,offset:n.index})}return t}},93251:(e,t,r)=>{"use strict";r.d(t,{removeUsdFromCryptoPairLogos:()=>l,resolveLogoUrls:()=>a});var n=r(36279);const o=(0,n.getLogoUrlResolver)();function a(e,t=n.LogoSize.Medium){const r=e.logoid,a=e["base-currency-logoid"],l=e["currency-logoid"],s=r&&o.getSymbolLogoUrl(r,t);if(s)return[s];const i=a&&o.getSymbolLogoUrl(a,t),c=l&&o.getSymbolLogoUrl(l,t);return i&&c?[i,c]:i?[i]:c?[c]:[]}function l(e){return 2!==e.length?e:function(e){return e.some((e=>s(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!s(e)))}(e)?e.filter((e=>!s(e))):e}function s(e){return!1}},44747:(e,t,r)=>{"use strict";r.d(t,{getBlockStyleClasses:()=>o,getLogoStyleClasses:()=>a});var n=r(97754);function o(e,t){return n("tv-circle-logo-pair",`tv-circle-logo-pair--${e}`,t)}function a(e,t){return n("tv-circle-logo-pair__logo",`tv-circle-logo-pair__logo--${e}`,!t&&"tv-circle-logo-pair__logo-empty")}},76068:(e,t,r)=>{"use strict";r.d(t,{CircleLogo:()=>l,hiddenCircleLogoClass:()=>a});var n=r(50959),o=r(58492);r(45300);const a="tv-circle-logo--visually-hidden";function l(e){var t,r;const a=(0,o.getStyleClasses)(e.size,e.className),l=null!==(r=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==r?r:"";return(0,o.isCircleLogoWithUrlProps)(e)?n.createElement("img",{className:a,crossOrigin:"",src:e.logoUrl,alt:l,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):n.createElement("span",{className:a,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},58492:(e,t,r)=>{"use strict";r.d(t,{getStyleClasses:()=>o,isCircleLogoWithUrlProps:()=>a});var n=r(97754);function o(e,t){return n("tv-circle-logo",`tv-circle-logo--${e}`,t)}function a(e){return"logoUrl"in e&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},19785:(e,t,r)=>{"use strict";r.d(t,{createRegExpList:()=>a,getHighlightedChars:()=>l,rankedSearch:()=>o});var n=r(1722);function o(e){const{data:t,rules:r,queryString:o,isPreventedFromFiltering:a,primaryKey:l,secondaryKey:s=l,optionalPrimaryKey:i,tertiaryKey:c}=e;return t.map((e=>{const t=i&&e[i]?e[i]:e[l],a=e[s],u=c&&e[c];let d,m=0;return r.forEach((e=>{var r,l,s,i,c;const{re:p,fullMatch:h}=e;if(p.lastIndex=0,(0,n.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return m=4,void(d=null===(r=t.match(h))||void 0===r?void 0:r.index);if((0,n.isString)(t)&&h.test(t))return m=3,void(d=null===(l=t.match(h))||void 0===l?void 0:l.index);if((0,n.isString)(a)&&h.test(a))return m=2,void(d=null===(s=a.match(h))||void 0===s?void 0:s.index);if((0,n.isString)(a)&&p.test(a))return m=2,void(d=null===(i=a.match(p))||void 0===i?void 0:i.index)
;if(Array.isArray(u))for(const e of u)if(h.test(e))return m=1,void(d=null===(c=e.match(h))||void 0===c?void 0:c.index)})),{matchPriority:m,matchIndex:d,item:e}})).filter((e=>a||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function a(e,t){const r=[],n=e.toLowerCase(),o=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${s(e)}`:s(e)})`)).join("(.*?)")+"(.*)";return r.push({fullMatch:new RegExp(`(${s(e)})`,"i"),re:new RegExp(`^${o}`,"i"),reserveRe:new RegExp(o,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(n)&&r.push({fullMatch:t[n],re:t[n],fuzzyHighlight:!1}),r}function l(e,t,r){const n=[];return e&&r?(r.forEach((e=>{const{fullMatch:r,re:o,reserveRe:a}=e;r.lastIndex=0,o.lastIndex=0;const l=r.exec(t),s=l||o.exec(t)||a&&a.exec(t);if(e.fuzzyHighlight=!l,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const r=s[t],o=s[t].length;if(t%2){const t=r.startsWith(" ")||r.startsWith("/")||r.startsWith("-");n[t?e+1:e]=!0}e+=o}}else for(let e=0;e<s[0].length;e++)n[s.index+e]=!0})),n):n}function s(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}},24637:(e,t,r)=>{"use strict";r.d(t,{HighlightedText:()=>s});var n=r(50959),o=r(97754),a=r(19785),l=r(75623);function s(e){const{queryString:t,rules:r,text:s,className:i}=e,c=(0,n.useMemo)((()=>(0,a.getHighlightedChars)(t,s,r)),[t,r,s]);return n.createElement(n.Fragment,null,c.length?s.split("").map(((e,t)=>n.createElement(n.Fragment,{key:t},c[t]?n.createElement("span",{className:o(l.highlighted,i)},e):n.createElement("span",null,e)))):s)}},77762:(e,t,r)=>{"use strict";r.d(t,{useEnsuredContext:()=>a});var n=r(50959),o=r(50151);function a(e){return(0,o.ensureNotNull)((0,n.useContext)(e))}},36947:(e,t,r)=>{"use strict";r.d(t,{useForceUpdate:()=>n.useForceUpdate});var n=r(125)},29006:(e,t,r)=>{"use strict";r.d(t,{useResizeObserver:()=>n.useResizeObserver});var n=r(67842)},77975:(e,t,r)=>{"use strict";r.d(t,{useWatchedValueReadonly:()=>o});var n=r(50959);const o=(e,t=!1)=>{const r="watchedValue"in e?e.watchedValue:void 0,o="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[a,l]=(0,n.useState)(r?r.value():o);return(t?n.useLayoutEffect:n.useEffect)((()=>{if(r){l(r.value());const e=e=>l(e);return r.subscribe(e),()=>r.unsubscribe(e)}return()=>{}}),[r]),a}},84877:(e,t,r)=>{"use strict";r.d(t,{MatchMediaMap:()=>l});var n=r(50959),o=r(66783),a=r.n(o);class l extends n.Component{constructor(e){super(e),this._handleMediaChange=()=>{const e=i(this.state.queries,((e,t)=>t.matches));let t=!1;for(const r in e)if(e.hasOwnProperty(r)&&this.state.matches[r]!==e[r]){t=!0;break}t&&this.setState({matches:e})};const{rules:t}=this.props;this.state=s(t)}shouldComponentUpdate(e,t){return!a()(e,this.props)||(!a()(t.rules,this.state.rules)||!a()(t.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}
componentDidUpdate(e,t){a()(e.rules,this.props.rules)||this._migrate(t.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(e,t){if(a()(e.rules,t.rules))return null;const{rules:r}=e;return s(r)}_migrate(e,t){null!==e&&i(e,((e,t)=>{t.removeListener(this._handleMediaChange)})),null!==t&&i(t,((e,t)=>{t.addListener(this._handleMediaChange)}))}}function s(e){const t=i(e,((e,t)=>window.matchMedia(t)));return{queries:t,matches:i(t,((e,t)=>t.matches)),rules:{...e}}}function i(e,t){const r={};for(const n in e)e.hasOwnProperty(n)&&(r[n]=t(n,e[n]));return r}},63932:(e,t,r)=>{"use strict";r.d(t,{Spinner:()=>l});var n=r(50959),o=r(97754),a=r(58096);r(83135);function l(e){const t=o(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${a.spinnerSizeMap[e.size||a.DEFAULT_SIZE]}`);return n.createElement("div",{className:t,style:e.style,role:"progressbar"})}},10381:(e,t,r)=>{"use strict";r.d(t,{ToolWidgetCaret:()=>i});var n=r(50959),o=r(97754),a=r(9745),l=r(34587),s=r(578);function i(e){const{dropped:t,className:r}=e;return n.createElement(a.Icon,{className:o(r,l.icon,{[l.dropped]:t}),icon:s})}},78029:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},31409:(e,t,r)=>{"use strict";r.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>s,ToolWidgetButton:()=>i});var n=r(50959),o=r(97754),a=r(9745),l=r(78029);const s=l,i=n.forwardRef(((e,t)=>{const{tag:r="div",icon:s,endIcon:i,isActive:c,isOpened:u,isDisabled:d,isGrouped:m,isHovered:p,isClicked:h,onClick:g,text:v,textBeforeIcon:f,title:y,theme:b=l,className:S,forceInteractive:x,inactive:w,"data-name":k,"data-tooltip":C,...E}=e,I=o(S,b.button,(y||C)&&"apply-common-tooltip",{[b.isActive]:c,[b.isOpened]:u,[b.isInteractive]:(x||Boolean(g))&&!d&&!w,[b.isDisabled]:Boolean(d||w),[b.isGrouped]:m,[b.hover]:p,[b.clicked]:h}),L=s&&("string"==typeof s?n.createElement(a.Icon,{className:b.icon,icon:s}):n.cloneElement(s,{className:o(b.icon,s.props.className)}));return"button"===r?n.createElement("button",{...E,ref:t,type:"button",className:o(I,b.accessible),disabled:d&&!w,onClick:g,title:y,"data-name":k,"data-tooltip":C},f&&v&&n.createElement("div",{className:o("js-button-text",b.text)},v),L,!f&&v&&n.createElement("div",{className:o("js-button-text",b.text)},v)):n.createElement("div",{...E,ref:t,"data-role":"button",className:I,onClick:d?void 0:g,title:y,"data-name":k,"data-tooltip":C},f&&v&&n.createElement("div",{className:o("js-button-text",b.text)},v),L,!f&&v&&n.createElement("div",{className:o("js-button-text",b.text)},v),i&&n.createElement(a.Icon,{icon:i,className:l.endIcon}))}))},24658:(e,t,r)=>{"use strict";r.d(t,{VISIBLE_TYPESPECS:()=>l,marketType:()=>s});var n=r(11542)
;const o=new Map([["cfd",n.t(null,void 0,r(87592))],["dr",n.t(null,void 0,r(67245))],["index",n.t(null,void 0,r(12754))],["forex",n.t(null,void 0,r(39512))],["right",n.t(null,{context:"symbol_type"},r(9898))],["bond",n.t(null,void 0,r(79852))],["bitcoin",n.t(null,void 0,r(8448))],["crypto",n.t(null,void 0,r(8448))],["economic",n.t(null,void 0,r(88720))],["indices",n.t(null,void 0,r(60804))],["futures",n.t(null,void 0,r(81859))],["stock",n.t(null,void 0,r(36931))],["commodity",n.t(null,void 0,r(12629))]]);r(42053);const a=new Map,l=new Set(["cfd","spreadbet","defi","yield","government","corporate","mutual","money","etf","unit","trust","reit","etn","convertible","closedend","crypto","oracle"]);function s(e,t=[],r=!0){const n=t.filter((e=>l.has(e))),s=`${e}_${n.sort().join("_")}`,i=a.get(s);if(void 0!==i)return i;const c=r?function(e){return o.get(e)||e}(e):e,u=Boolean(t.length)?[c,...n].join(" "):c;return a.set(s,u),u}},2948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},52019:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.35 5.35a.5.5 0 0 0-.7-.7L9 8.29 5.35 4.65a.5.5 0 1 0-.7.7L8.29 9l-3.64 3.65a.5.5 0 0 0 .7.7L9 9.71l3.65 3.64a.5.5 0 0 0 .7-.7L9.71 9l3.64-3.65z"/></svg>'},95694:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},49498:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},60176:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},35369:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},58478:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},73063:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},14127:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},18073:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},99243:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},42576:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},91540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M2.5 14.5c1.68-1.26 3.7-2 6.5-2s4.91.74 6.5 2m-13-11c1.68 1.26 3.7 2 6.5 2s4.91-.74 6.5-2"/><circle stroke="currentColor" cx="9" cy="9" r="8.5"/><path stroke="currentColor" d="M13.5 9c0 2.42-.55 4.58-1.4 6.12-.87 1.56-1.98 2.38-3.1 2.38s-2.23-.82-3.1-2.38c-.85-1.54-1.4-3.7-1.4-6.12s.55-4.58 1.4-6.12C6.77 1.32 7.88.5 9 .5s2.23.82 3.1 2.38c.85 1.54 1.4 3.7 1.4 6.12z"/></svg>'},66619:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#B2B5BE" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},67562:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#131722" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},
69533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},486:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h9"/><circle fill="currentColor" cx="7" cy="3" r="1"/><circle fill="currentColor" cx="7" cy="10" r="1"/></svg>'},63861:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><g fill="none" fill-rule="evenodd" stroke="currentColor"><path stroke-linecap="square" stroke-linejoin="round" d="M3.5 10V2.5L1 5"/><path stroke-linecap="square" d="M1.5 10.5h4"/><path d="M8 12l3-11"/></g></svg>'},81574:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8"/></svg>'},32617:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 10l7-7M3 3l7 7"/></svg>'},35119:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8m-4-4v8"/></svg>'},69135:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 7l3.5-3.5L10 7"/></svg>'},86240:e=>{"use strict";e.exports=JSON.parse('{"size-header-height":"64px","media-phone-vertical":"screen and (max-width: 479px)","media-mf-phone-landscape":"screen and (min-width: 568px)"}')}}]);