(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7550],{1414:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC","icon-only":"icon-only-D4RPB3ZC",link:"link-D4RPB3ZC","color-brand":"color-brand-D4RPB3ZC","variant-primary":"variant-primary-D4RPB3ZC","variant-secondary":"variant-secondary-D4RPB3ZC","color-gray":"color-gray-D4RPB3ZC","color-green":"color-green-D4RPB3ZC","color-red":"color-red-D4RPB3ZC","color-black":"color-black-D4RPB3ZC","color-black-friday":"color-black-friday-D4RPB3ZC","color-cyber-monday":"color-cyber-monday-D4RPB3ZC","size-xsmall":"size-xsmall-D4RPB3ZC","start-icon-wrap":"start-icon-wrap-D4RPB3ZC","end-icon-wrap":"end-icon-wrap-D4RPB3ZC","with-start-icon":"with-start-icon-D4RPB3ZC","with-end-icon":"with-end-icon-D4RPB3ZC","size-small":"size-small-D4RPB3ZC","size-medium":"size-medium-D4RPB3ZC","size-large":"size-large-D4RPB3ZC","size-xlarge":"size-xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC","adjust-position":"adjust-position-D4RPB3ZC","first-row":"first-row-D4RPB3ZC","first-col":"first-col-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC","text-wrap":"text-wrap-D4RPB3ZC","multiline-content":"multiline-content-D4RPB3ZC","secondary-text":"secondary-text-D4RPB3ZC","primary-text":"primary-text-D4RPB3ZC"}},81026:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},7236:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",
icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},30930:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},25650:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},88803:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},20817:e=>{e.exports={autocomplete:"autocomplete-uszkUMOz",caret:"caret-uszkUMOz",icon:"icon-uszkUMOz",suggestions:"suggestions-uszkUMOz",suggestion:"suggestion-uszkUMOz",noResults:"noResults-uszkUMOz",selected:"selected-uszkUMOz",opened:"opened-uszkUMOz"}},34587:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},94720:(e,t,n)=>{"use strict";n.d(t,{Button:()=>b});var s=n(50959),r=n(97754),o=n(95604),i=n(9745),a=n(1414),l=n.n(a);const c="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text";function u(e){const{color:t="brand",size:n="medium",variant:s="primary",stretch:i=!1,icon:a,startIcon:u,endIcon:d,iconOnly:h=!1,className:p,isGrouped:m,cellState:f,disablePositionAdjustment:g=!1,primaryText:v,secondaryText:y,isAnchor:w=!1}=e,_=function(e){let t="";return 0!==e&&(1&e&&(t=r(t,l()["no-corner-top-left"])),2&e&&(t=r(t,l()["no-corner-top-right"])),4&e&&(t=r(t,l()["no-corner-bottom-right"])),8&e&&(t=r(t,l()["no-corner-bottom-left"]))),t}((0,o.getGroupCellRemoveRoundBorders)(f));return r(p,l().button,l()[`size-${n}`],l()[`color-${t}`],l()[`variant-${s}`],i&&l().stretch,(a||u)&&l()["with-start-icon"],d&&l()["with-end-icon"],h&&l()["icon-only"],_,m&&l().grouped,m&&!g&&l()["adjust-position"],m&&f.isTop&&l()["first-row"],m&&f.isLeft&&l()["first-col"],v&&y&&l()["multiline-content"],w&&l().link,c)}function d(e){const{startIcon:t,icon:n,iconOnly:o,children:a,endIcon:u,primaryText:d,secondaryText:h}=e,p=null!=t?t:n,m=!(t||n||u||o)&&!a&&d&&h;return s.createElement(s.Fragment,null,p&&s.createElement(i.Icon,{icon:p,className:l()["start-icon-wrap"]}),a&&s.createElement("span",{className:l().content},a),u&&!o&&s.createElement(i.Icon,{icon:u,className:l()["end-icon-wrap"]}),m&&function(e){return e.primaryText&&e.secondaryText&&s.createElement("div",{className:r(l()["text-wrap"],c)},s.createElement("span",{className:l()["primary-text"]}," ",e.primaryText," "),"string"==typeof e.secondaryText?s.createElement("span",{className:l()["secondary-text"]}," ",e.secondaryText," "):s.createElement("span",{className:l()["secondary-text"]
},s.createElement("span",null,e.secondaryText.firstLine),s.createElement("span",null,e.secondaryText.secondLine)))}(e))}var h=n(34094),p=n(86332),m=n(90186);function f(e){const{className:t,color:n,variant:s,size:r,stretch:o,animated:i,icon:a,iconOnly:l,startIcon:c,endIcon:u,primaryText:d,secondaryText:h,...p}=e;return{...p,...(0,m.filterDataProps)(e),...(0,m.filterAriaProps)(e)}}function g(e){const{reference:t,tooltipText:n,...r}=e,{isGrouped:o,cellState:i,disablePositionAdjustment:a}=(0,s.useContext)(p.ControlGroupContext),l=u({...r,isGrouped:o,cellState:i,disablePositionAdjustment:a});return s.createElement("button",{...f(r),className:l,ref:t,"data-overflow-tooltip-text":null!=n?n:e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,h.getTextForTooltip)(e.children)},s.createElement(d,{...r}))}function v(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function y(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function w(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}function _(e){const{intent:t,size:n,appearance:s,useFullWidth:r,icon:o,...i}=e;return{...i,color:y(t),size:w(n),variant:v(s),stretch:r,startIcon:o}}function b(e){return s.createElement(g,{..._(e)})}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>s});const s=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function s(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>s})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>y,InputClasses:()=>f});var s=n(50959),r=n(97754),o=n(50151),i=n(38528),a=n(90186),l=n(86332),c=n(95604);var u=n(81026),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,s){const{removeRoundBorder:o,className:i,intent:a="default",borderStyle:l="thin",size:u,highlight:p,disabled:m,readonly:f,stretch:g,noReadonlyStyles:v,isFocused:y}=e,w=h(null!=o?o:(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${a}`],d()[`border-${l}`],u&&d()[`size-${u}`],w,p&&d()["with-highlight"],m&&d().disabled,f&&!v&&d().readonly,y&&d().focused,g&&d().stretch,t&&d().grouped,!s&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function m(e,t,n){const{highlight:s,highlightRemoveRoundBorder:o}=e;if(!s)return d().highlight;const i=h(null!=o?o:(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],i)}const f={FontSizeMedium:(0,o.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,o.ensureDefined)(d()["font-size-large"])},g={passive:!1};function v(e,t){
const{style:n,id:r,role:o,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:y,onClick:w,tabIndex:_,startSlot:b,middleSlot:x,endSlot:C,onWheel:D,onWheelNoPassive:R=null,size:E}=e,{isGrouped:P,cellState:S,disablePositionAdjustment:N=!1}=(0,s.useContext)(l.ControlGroupContext),k=function(e,t=null,n){const r=(0,s.useRef)(null),o=(0,s.useRef)(null),i=(0,s.useCallback)((()=>{if(null===r.current||null===o.current)return;const[e,t,n]=o.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),a=(0,s.useCallback)((()=>{if(null===r.current||null===o.current)return;const[e,t,n]=o.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),l=(0,s.useCallback)((e=>{a(),r.current=e,i()}),[]);return(0,s.useEffect)((()=>(o.current=[e,t,n],i(),a)),[e,t,n]),l}("wheel",R,g);return s.createElement("span",{style:n,id:r,role:o,className:p(e,P,S,N),tabIndex:_,ref:(0,i.useMergedRefs)([t,k]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:y,onClick:w,onWheel:D,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},b,x,C,s.createElement("span",{className:m(e,S,E)}))}v.displayName="ControlSkeleton";const y=s.forwardRef(v)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>l,StartSlot:()=>a});var s=n(50959),r=n(97754),o=n(7236),i=n.n(o);function a(e){const{className:t,interactive:n=!0,icon:o=!1,children:a}=e;return s.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,o&&i().icon,t)},a)}function l(e){const{className:t,children:n}=e;return s.createElement("span",{className:r(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:o=!1,children:a}=e;return s.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,o&&i().icon,t)},a)}function u(e){const{className:t,children:n}=e;return s.createElement("span",{className:r(i()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>y});var s=n(50959),r=n(97754),o=n(90186),i=n(47201),a=n(48907),l=n(38528),c=n(48027),u=n(29202),d=n(45812),h=n(67029),p=n(78274),m=n(30930),f=n.n(m);function g(e){return!(0,o.isAriaAttribute)(e)&&!(0,o.isDataAttribute)(e)}function v(e){const{id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:y,autoFocus:w,maxLength:_,min:b,max:x,step:C,pattern:D,inputMode:R,onSelect:E,onFocus:P,onBlur:S,onKeyDown:N,onKeyUp:k,onKeyPress:z,onChange:B,onDragStart:O,size:Z="small",className:W,inputClassName:L,disabled:I,readonly:M,containerTabIndex:T,startSlot:U,endSlot:A,reference:F,containerReference:j,onContainerFocus:V,...q}=e,H=(0,o.filterProps)(q,g),K={...(0,o.filterAriaProps)(q),...(0,o.filterDataProps)(q),id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:y,autoFocus:w,maxLength:_,min:b,max:x,step:C,pattern:D,inputMode:R,onSelect:E,onFocus:P,onBlur:S,onKeyDown:N,onKeyUp:k,onKeyPress:z,onChange:B,onDragStart:O};return s.createElement(h.ControlSkeleton,{...H,disabled:I,
readonly:M,tabIndex:T,className:r(f().container,W),size:Z,ref:j,onFocus:V,startSlot:U,middleSlot:s.createElement(p.MiddleSlot,null,s.createElement("input",{...K,className:r(f().input,f()[`size-${Z}`],L,U&&f()["with-start-slot"],A&&f()["with-end-slot"]),disabled:I,readOnly:M,ref:F})),endSlot:A})}function y(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:o,onBlur:h,reference:p,containerReference:m=null}=e,f=(0,s.useRef)(null),g=(0,s.useRef)(null),[y,w]=(0,u.useFocus)(),_=t?void 0:y?-1:r,b=t?void 0:y?r:-1,{isMouseDown:x,handleMouseDown:C,handleMouseUp:D}=(0,d.useIsMouseDown)(),R=(0,i.createSafeMulticastEventHandler)(w.onFocus,(function(e){n&&!x.current&&(0,a.selectAllContent)(e.currentTarget)}),o),E=(0,i.createSafeMulticastEventHandler)(w.onBlur,h),P=(0,s.useCallback)((e=>{f.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[f,p]);return s.createElement(v,{...e,isFocused:y,containerTabIndex:_,tabIndex:b,onContainerFocus:function(e){g.current===e.target&&null!==f.current&&f.current.focus()},onFocus:R,onBlur:E,reference:P,containerReference:(0,l.useMergedRefs)([g,m]),onMouseDown:C,onMouseUp:D})}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>o});var s=n(47201),r=n(29202);function o(e){const{onFocus:t,onBlur:n,intent:o,highlight:i,disabled:a}=e,[l,c]=(0,r.useFocus)(void 0,a),u=(0,s.createSafeMulticastEventHandler)(a?void 0:c.onFocus,t),d=(0,s.createSafeMulticastEventHandler)(a?void 0:c.onBlur,n);return{...e,intent:o||(l?"primary":"default"),highlight:null!=i?i:l,onFocus:u,onBlur:d}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var s=n(50959);function r(e,t){const[n,r]=(0,s.useState)(!1);(0,s.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const o={onFocus:(0,s.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,s.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,o]}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var s=n(50959);function r(){const e=(0,s.useRef)(!1),t=(0,s.useCallback)((()=>{e.current=!0}),[e]),n=(0,s.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>o});var s=n(50959),r=n(53017);function o(e){return(0,s.useCallback)((0,r.mergeRefs)(e),e)}},27267:(e,t,n)=>{"use strict";function s(e,t,n,s,r){function o(r){if(e>r.timeStamp)return;const o=r.target;void 0!==n&&null!==t&&null!==o&&o.ownerDocument===s&&(t.contains(o)||n(r))}return r.click&&s.addEventListener("click",o,!1),r.mouseDown&&s.addEventListener("mousedown",o,!1),r.touchEnd&&s.addEventListener("touchend",o,!1),r.touchStart&&s.addEventListener("touchstart",o,!1),()=>{s.removeEventListener("click",o,!1),s.removeEventListener("mousedown",o,!1),s.removeEventListener("touchend",o,!1),s.removeEventListener("touchstart",o,!1)}}n.d(t,{addOutsideEventListener:()=>s})},36383:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>o});var s=n(50959),r=n(27267);function o(e){
const{click:t,mouseDown:n,touchEnd:o,touchStart:i,handler:a,reference:l,ownerDocument:c=document}=e,u=(0,s.useRef)(null),d=(0,s.useRef)(new CustomEvent("timestamp").timeStamp);return(0,s.useLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:o,touchStart:i},s=l?l.current:u.current;return(0,r.addOutsideEventListener)(d.current,s,a,c,e)}),[t,n,o,i,a]),l||u}},9745:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>r});var s=n(50959);const r=s.forwardRef(((e,t)=>{const{icon:n="",...r}=e;return s.createElement("span",{...r,ref:t,dangerouslySetInnerHTML:{__html:n}})}))},26996:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>a});var s=n(50959),r=n(97754),o=n(25650),i=n.n(o);function a(e){const{className:t,size:n="medium",staticPosition:o,color:a="black"}=e,l=r(i().item,i()[a],i()[n]);return s.createElement("span",{className:r(i().loader,o&&i().static,t)},s.createElement("span",{className:l}),s.createElement("span",{className:l}),s.createElement("span",{className:l}))}},99663:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>r,SlotContext:()=>o});var s=n(50959);class r extends s.Component{shouldComponentUpdate(){return!1}render(){return s.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const o=s.createContext(null)},90186:(e,t,n)=>{"use strict";function s(e){return o(e,i)}function r(e){return o(e,a)}function o(e,t){const n=Object.entries(e).filter(t),s={};for(const[e,t]of n)s[e]=t;return s}function i(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function a(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>r,filterDataProps:()=>s,filterProps:()=>o,isAriaAttribute:()=>a,isDataAttribute:()=>i})},34094:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>i});var s=n(50959);const r=e=>(0,s.isValidElement)(e)&&Boolean(e.props.children),o=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",i=e=>Array.isArray(e)||(0,s.isValidElement)(e)?s.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,s.isValidElement)(t)&&r(t)?i(t.props.children):(0,s.isValidElement)(t)&&!r(t)?"":o(t),e.concat(n)}),"").trim():o(e)},48907:(e,t,n)=>{"use strict";function s(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>s})},67961:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>o,getRootOverlapManager:()=>a});var s=n(50151);class r{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class o{constructor(e=document){this._storage=new r,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e)
;if(void 0!==n)return n;this.registerWindow(e);const s=this._document.createElement("div");if(s.style.position=t.position,s.style.zIndex=this._index.toString(),s.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(s);else if(t.index<=0)this._container.insertBefore(s,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(s,e)}}else"reverse"===t.direction?this._container.insertBefore(s,this._container.firstChild):this._container.appendChild(s);return this._windows.set(e,s),++this._index,s}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){this.ensureWindow(e).style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const i=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,s.ensureDefined)(i.get(t));{const t=new o(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return i.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}},47201:(e,t,n)=>{"use strict";function s(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>s})},99054:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>c});const s=(()=>{let e;return()=>{var t;if(void 0===e){const n=document.createElement("div"),s=n.style;s.visibility="hidden",s.width="100px",s.msOverflowStyle="scrollbar",document.body.appendChild(n);const r=n.offsetWidth;n.style.overflow="scroll";const o=document.createElement("div");o.style.width="100%",n.appendChild(o);const i=o.offsetWidth;null===(t=n.parentNode)||void 0===t||t.removeChild(n),e=r-i}return e}})();function r(e,t,n){null!==e&&e.style.setProperty(t,n)}function o(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function i(e,t){return parseInt(o(e,t))}let a=0,l=!1;function c(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=o(t,"overflow"),a=i(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(r(n,"right",`${s()}px`),t.style.paddingRight=`${a+s()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){r(n,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=s()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var s=n(88803);const r={SmallHeight:s["small-height-breakpoint"],TabletSmall:s["tablet-small-breakpoint"],TabletNormal:s["tablet-normal-breakpoint"]}},21788:(e,t,n)=>{"use strict";n.d(t,{Autocomplete:()=>m});var s,r=n(11542),o=n(50959),i=n(97754),a=n(10381),l=n(78274),c=n(31261),u=n(52778);!function(e){e[e.Enter=13]="Enter",
e[e.Space=32]="Space",e[e.Backspace=8]="Backspace",e[e.DownArrow=40]="DownArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.LeftArrow=37]="LeftArrow",e[e.Escape=27]="Escape",e[e.Tab=9]="Tab"}(s||(s={}));var d=n(42842),h=n(20817);function p(e,t){return""===e||-1!==t.toLowerCase().indexOf(e.toLowerCase())}class m extends o.PureComponent{constructor(e){if(super(e),this._containerInputElement=null,this._raf=null,this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleMeasure=()=>{if(this.state.isMeasureValid||!this.props.suggestionsInPortal||!this._containerInputElement)return;const{bottom:e,left:t,width:n}=this._containerInputElement.getBoundingClientRect();this.setState({appearingWidth:n,appearingPosition:{x:t,y:e},isMeasureValid:!0})},this._setInputRef=e=>{e&&(this._inputElement=e,this.props.setupHTMLInput&&this.props.setupHTMLInput(e),this._inputElement.addEventListener("keyup",this._handleKeyUpEnter))},this._setContainerInputRef=e=>{this._containerInputElement=e},this._handleCaretClick=()=>{this.state.isOpened?(this._close(),this.props.preventOnFocusOpen&&this._focus()):this.props.preventOnFocusOpen?this._open():this._focus()},this._handleOutsideClick=()=>{const{allowUserDefinedValues:e,value:t,onChange:n}=this.props,{queryValue:s}=this.state;e?n&&s!==t&&n(s):this.setState(this._valueToQuery(t)),this._close()},this._handleFocus=e=>{this.props.preventOnFocusOpen||this._open(),this.props.onFocus&&this.props.onFocus(e)},this._handleChange=e=>{const{preventSearchOnEmptyQuery:t,allowUserDefinedValues:n,onChange:s,onSuggestionsOpen:r,onSuggestionsClose:o}=this.props,i=e.currentTarget.value;if(t&&""===i)this.setState({queryValue:i,isOpened:!1,active:void 0}),o&&o();else{const e=this._suggestions(i),t=Object.keys(e).length>0;this.setState({queryValue:i,isOpened:t,active:n?void 0:this._getActiveKeyByValue(i)}),t&&r&&r()}n&&s&&s(i)},this._handleItemClick=e=>{const t=e.currentTarget.id;this.setState({queryValue:f(this.props.source)[t]}),this.props.onChange&&this.props.onChange(t),this._close()},this._handleKeyDown=e=>{if(-1===[s.DownArrow,s.UpArrow,s.Enter,s.Escape].indexOf(e.which))return;const{allowUserDefinedValues:t,value:n,onChange:r,onSuggestionsOpen:o}=this.props,{active:i,isOpened:a,queryValue:l}=this.state;a&&(e.preventDefault(),e.stopPropagation());const c=this._suggestions(l);switch(e.which){case s.DownArrow:case s.UpArrow:const u=Object.keys(c);if(!a&&u.length&&e.which===s.DownArrow){this.setState({isOpened:!0,active:u[0]}),o&&o();break}let d;if(void 0===i){if(e.which===s.UpArrow){this._close();break}d=0}else d=u.indexOf(i)+(e.which===s.UpArrow?-1:1);d<0&&(d=0),d>u.length-1&&(d=u.length-1);const h=u[d];this.setState({active:h});const p=document.getElementById(h);p&&this._scrollIfNotVisible(p,this._suggestionsElement);break;case s.Escape:this._close(),a||this._blur();break;case s.Enter:let m=i;t&&(a&&m?this.setState(this._valueToQuery(m)):m=l),
void 0!==m&&(this._close(),a||this._blur(),m!==n?r&&r(m):this.setState(this._valueToQuery(m)))}},this._setSuggestionsRef=e=>{e&&(this._suggestionsElement=e)},this._scrollIfNotVisible=(e,t)=>{const n=t.scrollTop,s=t.scrollTop+t.clientHeight,r=e.offsetTop,o=r+e.clientHeight;r<=n?e.scrollIntoView(!0):o>=s&&e.scrollIntoView(!1)},!(e=>Array.isArray(e.source)||!e.allowUserDefinedValues)(e))throw new Error("allowUserDefinedProps === true cay only be used if source is array");this.state={valueFromProps:e.value,isOpened:!1,active:e.value,queryValue:f(e.source)[e.value]||(e.allowUserDefinedValues?e.value:"")}}componentDidMount(){this.props.suggestionsInPortal&&window.addEventListener("resize",this._resize)}componentDidUpdate(){this.state.isOpened&&this._handleMeasure()}componentWillUnmount(){this._inputElement&&this._inputElement.removeEventListener("keyup",this._handleKeyUpEnter),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),window.removeEventListener("resize",this._resize)}render(){return o.createElement(u.OutsideEvent,{handler:this._handleOutsideClick,click:!0},(e=>o.createElement("div",{className:i(h.autocomplete,"js-dialog-skip-escape"),ref:e},o.createElement(c.InputControl,{id:this.props.id,name:this.props.name,endSlot:Object.keys(this._suggestions(this.state.queryValue)).length?o.createElement(l.EndSlot,null,o.createElement("span",{className:h.caret,onClick:this._handleCaretClick,tabIndex:-1},o.createElement(a.ToolWidgetCaret,{className:h.icon,dropped:this.state.isOpened}))):void 0,maxLength:this.props.maxLength,reference:this._setInputRef,containerReference:this._setContainerInputRef,stretch:!0,placeholder:this.props.placeholder,value:this.state.queryValue,intent:this.props.error?"danger":void 0,onChange:this._handleChange,onFocus:this._handleFocus,onBlur:this.props.onBlur,onMouseOver:this.props.onMouseOver,onMouseOut:this.props.onMouseOut,onKeyDown:this._handleKeyDown,autoComplete:"off",size:this.props.size}),this._renderSuggestions())))}static getDerivedStateFromProps(e,t){const{allowUserDefinedValues:n,value:s,source:r}=e;if(s===t.valueFromProps&&t.isOpened)return null;const o=n?s:""===s?"":f(r)[s]||t.queryValue;return{...t,valueFromProps:s,active:s,queryValue:o}}_renderSuggestions(){return this.props.suggestionsInPortal?this.state.isOpened?this._renderPortalSuggestions():null:this._renderSuggestionsItems()}_renderPortalSuggestions(){return o.createElement(d.Portal,null,this._renderSuggestionsItems())}_focus(){this._inputElement.focus()}_blur(){this._inputElement.blur()}_open(){const{onSuggestionsOpen:e}=this.props;this._focus(),this.setState({isOpened:!0,active:this.props.value}),e&&e()}_close(){const{onSuggestionsClose:e}=this.props;this.setState({isOpened:!1,active:void 0}),e&&e()}_suggestions(e){const{filter:t=p}=this.props,n=f(this.props.source),s={};return Object.keys(n).filter((s=>t(e,n[s]))).forEach((e=>s[e]=n[e])),s}_renderSuggestionsItems(){const e=this._suggestions(this.state.queryValue),t=Object.keys(e).map((t=>{const n=i(h.suggestion,this.state.active===t&&h.selected)
;return o.createElement("li",{id:t,key:t,className:n,onClick:this._handleItemClick},e[t])})),s=o.createElement("li",{className:h.noResults},r.t(null,void 0,n(56614)));if(!t.length&&this.props.noEmptyText)return null;const{appearingPosition:a,appearingWidth:l}=this.state;return o.createElement("ul",{className:i(h.suggestions,this.state.isOpened&&h.opened),ref:this._setSuggestionsRef,style:{left:a&&a.x,top:a&&a.y,width:l&&l}},t.length?t:s)}_handleKeyUpEnter(e){e.which===s.Enter&&e.stopImmediatePropagation()}_getActiveKeyByValue(e){const{filter:t=p}=this.props,n=this._suggestions(e),s=Object.keys(n);for(const r of s)if(t(e,n[r]))return r;return s[0]}_valueToQuery(e){return{queryValue:f(this.props.source)[e]||""}}}function f(e){let t={};return Array.isArray(e)?e.forEach((e=>{t[e]=e})):t=e,t}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>r});var s=n(50959);class r extends s.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addListener(this._handleChange)}_unsubscribe(e){e.removeListener(this._handleChange)}}},42842:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>l,PortalContext:()=>c});var s=n(50959),r=n(962),o=n(25931),i=n(67961),a=n(99663);class l extends s.PureComponent{constructor(){super(...arguments),this._uuid=(0,o.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);return e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"",this.props.className&&e.classList.add(this.props.className),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),r.createPortal(s.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,i.getRootOverlapManager)():this.context}}l.contextType=a.SlotContext;const c=s.createContext(null)},50655:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>s.Slot,SlotContext:()=>s.SlotContext});var s=n(99663)},10381:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var s=n(50959),r=n(97754),o=n(9745),i=n(34587),a=n(578);function l(e){const{dropped:t,className:n}=e;return s.createElement(o.Icon,{className:r(n,i.icon,{[i.dropped]:t}),icon:a})}},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},25931:(e,t,n)=>{"use strict"
;n.d(t,{nanoid:()=>s});let s=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")},20036:e=>{e.exports={ar:["إلغاء"],ca_ES:["Cancel·la"],cs:["Zrušit"],de:["Abbrechen"],el:["Άκυρο"],en:"Cancel",es:["Cancelar"],fa:["لغو"],fr:["Annuler"],he_IL:["ביטול"],hu_HU:["Törlés"],id_ID:["Batal"],it:["Annulla"],ja:["キャンセル"],ko:["취소"],ms_MY:["Batal"],nl_NL:["Annuleren"],pl:["Anuluj"],pt:["Cancelar"],ro:"Cancel",ru:["Отмена"],sv:["Avbryt"],th:["ยกเลิก"],tr:["İptal"],vi:["Hủy bỏ"],zh:["取消"],zh_TW:["取消"]}},68988:e=>{e.exports={ar:["موافق"],ca_ES:["Acceptar"],cs:"Ok",de:"Ok",el:"Ok",en:"Ok",es:["Aceptar"],fa:"Ok",fr:["D'accord"],he_IL:["אוקיי"],hu_HU:["Oké"],id_ID:"Ok",it:"Ok",ja:["OK"],ko:["확인"],ms_MY:"Ok",nl_NL:"Ok",pl:"Ok",pt:"Ok",ro:"Ok",ru:["Ок"],sv:["OK"],th:["ตกลง"],tr:["Tamam"],vi:"Ok",zh:["确认"],zh_TW:["確認"]}},56614:e=>{e.exports={ar:["لا توجد نتائج"],ca_ES:["No s'han trobat resultats"],cs:"No results found",de:["Keine Ergebnisse"],el:"No results found",en:"No results found",es:["No se han encontrado resultados"],fa:"No results found",fr:["Pas de résultat trouvé"],he_IL:["לא נמצאו תוצאות"],hu_HU:"No results found",id_ID:["Hasil tidak ditemukan"],it:["Nessun risultato trovato"],ja:["該当なし"],ko:["결과를 찾을 수 없습니다"],ms_MY:["Tiada keputusan dijumpai"],nl_NL:"No results found",pl:["Brak wyników przeszukiwania"],pt:["Nenhum resultado encontrado"],ro:"No results found",ru:["Не найдено результатов"],sv:["Inga resultat hittades"],th:["ไม่พบข้อมูลใดๆ"],tr:["Hiç sonuç bulunamadı"],vi:["Không tìm thấy kết quả"],zh:["未搜寻结果"],zh_TW:["未找到結果"]}},85520:e=>{e.exports={ar:["حفظ"],ca_ES:["Desa"],cs:["Uložit"],de:["Speichern"],el:["Αποθήκευση"],en:"Save",es:["Guardar"],fa:["ذخیره"],fr:["Sauvegarder"],he_IL:["שמור"],hu_HU:["Mentés"],id_ID:["Simpan"],it:["Salva"],ja:["保存"],ko:["저장"],ms_MY:["Simpan"],nl_NL:["Opslaan"],pl:["Zapisz"],pt:["Salvar"],ro:"Save",ru:["Сохранить"],sv:["Spara"],th:["บันทึก"],tr:["Kaydet"],vi:["Lưu"],zh:["保存"],zh_TW:["儲存"]}}}]);