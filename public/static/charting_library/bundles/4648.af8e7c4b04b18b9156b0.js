(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4648,6408],{5734:e=>{e.exports={dialog:"dialog-aRAWUDhF",rounded:"rounded-aRAWUDhF",shadowed:"shadowed-aRAWUDhF",fullscreen:"fullscreen-aRAWUDhF",darker:"darker-aRAWUDhF",backdrop:"backdrop-aRAWUDhF"}},38446:e=>{e.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},36547:e=>{e.exports={defaultSelect:"defaultSelect-OM7V5ndi"}},14619:e=>{e.exports={itemWrap:"itemWrap-srH7jxJB",item:"item-srH7jxJB",icon:"icon-srH7jxJB",selected:"selected-srH7jxJB",label:"label-srH7jxJB"}},7625:e=>{e.exports={lineEndSelect:"lineEndSelect-gw7ESiZg",right:"right-gw7ESiZg"}},66220:e=>{e.exports={lineStyleSelect:"lineStyleSelect-GcXENVb4",multipleStyles:"multipleStyles-GcXENVb4"}},99118:e=>{e.exports={lineWidthSelect:"lineWidthSelect-EUDB1YgB",bar:"bar-EUDB1YgB",isActive:"isActive-EUDB1YgB",item:"item-EUDB1YgB"}},68089:e=>{e.exports={container:"container-dhpv13DH",active:"active-dhpv13DH",disabled:"disabled-dhpv13DH",icon:"icon-dhpv13DH"}},45707:e=>{e.exports={wrap:"wrap-b6_0ORMg",disabled:"disabled-b6_0ORMg"}},3115:e=>{e.exports={dropdown:"dropdown-gZlS9p6t",dropdownMenu:"dropdownMenu-gZlS9p6t",firstColorPicker:"firstColorPicker-gZlS9p6t"}},47543:e=>{e.exports={row:"row-nGXZ4vJz",empty:"empty-nGXZ4vJz",wrap:"wrap-nGXZ4vJz",breakpointNormal:"breakpointNormal-nGXZ4vJz",breakpointMedium:"breakpointMedium-nGXZ4vJz",breakpointSmall:"breakpointSmall-nGXZ4vJz"}},50540:e=>{e.exports={coordinates:"coordinates-mb1bDWNb",input:"input-mb1bDWNb",selectionCoordinates:"selectionCoordinates-mb1bDWNb",selectionCoordinates__inputs:"selectionCoordinates__inputs-mb1bDWNb",selectionCoordinates__description:"selectionCoordinates__description-mb1bDWNb",hintButton:"hintButton-mb1bDWNb"}},35199:e=>{e.exports={wrapper:"wrapper-NVcHMTVy",checkbox:"checkbox-NVcHMTVy",colorSelect:"colorSelect-NVcHMTVy",hintButton:"hintButton-NVcHMTVy"}},22497:e=>{e.exports={withoutPadding:"withoutPadding-KtEcG0Q0"}},54970:e=>{e.exports={input:"input-mIsHGNhw",control:"control-mIsHGNhw",item:"item-mIsHGNhw",cell:"cell-mIsHGNhw",fragmentCell:"fragmentCell-mIsHGNhw",largeWidth:"largeWidth-mIsHGNhw",
withTitle:"withTitle-mIsHGNhw",title:"title-mIsHGNhw"}},89232:e=>{e.exports={line:"line-j5rMaiWF",control:"control-j5rMaiWF",valueInput:"valueInput-j5rMaiWF",valueUnit:"valueUnit-j5rMaiWF",input:"input-j5rMaiWF"}},76739:e=>{e.exports={unit:"unit-ZtRdVxiD",input:"input-ZtRdVxiD",normal:"normal-ZtRdVxiD",big:"big-ZtRdVxiD",dropdown:"dropdown-ZtRdVxiD",dropdownMenu:"dropdownMenu-ZtRdVxiD"}},22332:e=>{e.exports={optionalTwoColors:"optionalTwoColors-LDRcAXEV",colorPicker:"colorPicker-LDRcAXEV",dropdown:"dropdown-LDRcAXEV",dropdownMenu:"dropdownMenu-LDRcAXEV"}},13784:e=>{e.exports={dropdown:"dropdown-RxdEkbF0",normal:"normal-RxdEkbF0",big:"big-RxdEkbF0",dropdownMenu:"dropdownMenu-RxdEkbF0"}},66586:e=>{e.exports={range:"range-GLEBGed4",valueInput:"valueInput-GLEBGed4",rangeSlider:"rangeSlider-GLEBGed4",rangeSlider_mixed:"rangeSlider_mixed-GLEBGed4",input:"input-GLEBGed4",hintButton:"hintButton-GLEBGed4"}},85357:e=>{e.exports={select:"select-hJtsYZ3G",wrap:"wrap-hJtsYZ3G",colorsWrap:"colorsWrap-hJtsYZ3G"}},42793:e=>{e.exports={colorPicker:"colorPicker-VK3h8amb",fontStyleButton:"fontStyleButton-VK3h8amb",dropdown:"dropdown-VK3h8amb",dropdownMenu:"dropdownMenu-VK3h8amb",hintButton:"hintButton-VK3h8amb"}},81364:e=>{e.exports={twoColors:"twoColors-C2hZXnYv",colorPicker:"colorPicker-C2hZXnYv"}},27394:e=>{e.exports={dropdown:"dropdown-eLkGg0Ft",menu:"menu-eLkGg0Ft"}},8326:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},64104:e=>{e.exports={desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},58516:e=>{e.exports={btnContent:"btnContent-ivexqeZZ",contentPart:"contentPart-ivexqeZZ"}},54583:e=>{e.exports={checkbox:"checkbox-aOSYFxuH"}},65542:e=>{e.exports={range:"range-mFgGeMmT",disabled:"disabled-mFgGeMmT",rangeSlider:"rangeSlider-mFgGeMmT",rangeSliderMiddleWrap:"rangeSliderMiddleWrap-mFgGeMmT",rangeSliderMiddle:"rangeSliderMiddle-mFgGeMmT",dragged:"dragged-mFgGeMmT",pointer:"pointer-mFgGeMmT",rangePointerWrap:"rangePointerWrap-mFgGeMmT"}},27306:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},53017:(e,t,n)=>{"use strict";function o(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function i(e){return o([e])}n.d(t,{isomorphicRef:()=>i,mergeRefs:()=>o})},52778:(e,t,n)=>{"use strict";n.d(t,{OutsideEvent:()=>i});var o=n(36383);function i(e){const{children:t,...n}=e;return t((0,o.useOutsideEvent)(n))}},66045:(e,t,n)=>{"use strict";n.d(t,{FontSizeSelect:()=>c});var o=n(50959),i=n(97754),r=n.n(i),a=n(90405),l=n(90186),s=n(36547);function c(e){const{id:t,fontSize:n,fontSizes:i=[],className:c,disabled:d,fontSizeChange:u}=e;return o.createElement(a.Select,{id:t,disabled:d,className:r()(c,s.defaultSelect),
menuClassName:s.defaultSelect,items:(p=i,p.map((e=>({value:e.value,content:e.title})))),value:n,onChange:u,...(0,l.filterDataProps)(e)});var p}},94697:(e,t,n)=>{"use strict";n.d(t,{DisplayItem:()=>d,DropItem:()=>u,IconDropdown:()=>c});var o=n(50959),i=n(97754),r=n.n(i),a=n(90405),l=n(9745),s=n(14619);function c(e){const{menuItemClassName:t,...n}=e;return o.createElement(a.Select,{...n,menuItemClassName:r()(t,s.itemWrap)})}function d(e){return o.createElement("div",{className:r()(s.item,s.selected,e.className)},o.createElement(l.Icon,{className:s.icon,icon:e.icon}))}function u(e){return o.createElement("div",{className:s.item},o.createElement(l.Icon,{className:r()(s.icon,e.iconClassName),icon:e.icon}),o.createElement("div",{className:s.label},e.label))}},53598:(e,t,n)=>{"use strict";n.d(t,{LineStyleSelect:()=>f});var o=n(11542),i=n(50959),r=n(97754),a=n.n(r),l=n(79849),s=n(94697),c=n(501),d=n(23851),u=n(57740),p=n(80427),h=n(66220);const m=[{type:l.LINESTYLE_SOLID,icon:c,label:o.t(null,void 0,n(1277))},{type:l.LINESTYLE_DASHED,icon:d,label:o.t(null,void 0,n(59317))},{type:l.LINESTYLE_DOTTED,icon:u,label:o.t(null,void 0,n(42973))}];class f extends i.PureComponent{render(){const{id:e,lineStyle:t,className:n,lineStyleChange:o,disabled:r,additionalItems:l,allowedLineStyles:c}=this.props;let d=function(e){let t=[...m];return void 0!==e&&(t=t.filter((t=>e.includes(t.type)))),t.map((e=>({value:e.type,selectedContent:i.createElement(s.DisplayItem,{icon:e.icon}),content:i.createElement(s.DropItem,{icon:e.icon,label:e.label})})))}(c);return l&&(d=[{readonly:!0,content:l},...d]),i.createElement(s.IconDropdown,{id:e,disabled:r,className:a()(h.lineStyleSelect,n),hideArrowButton:!0,items:d,value:t,onChange:o,"data-name":"line-style-select",addPlaceholderToItems:!1,placeholder:i.createElement(s.DisplayItem,{icon:p,className:h.multipleStyles})})}}},50890:(e,t,n)=>{"use strict";n.d(t,{LineWidthSelect:()=>d});var o=n(50959),i=n(97754),r=n(90405),a=n(99118);const l=[1,2,3,4];function s(e){const{id:t,value:n,items:s=l,disabled:c,onChange:d}=e;return o.createElement(r.Select,{id:t,disabled:c,hideArrowButton:!0,className:a.lineWidthSelect,items:(u=s,u.map((e=>({value:e,selectedContent:p(e,!0),content:p(e)})))),value:n,onChange:d,"data-name":"line-width-select"});var u;function p(e,t){const r={borderTopWidth:e};return o.createElement("div",{className:a.item},o.createElement("div",{className:i(a.bar,{[a.isActive]:e===n&&!t}),style:r}," "))}}var c=n(45560);function d(e){const{property:t}=e,[n,i]=(0,c.useDefinitionProperty)({property:t});return o.createElement(s,{...e,value:n,onChange:i})}},66849:(e,t,n)=>{"use strict";n.d(t,{ControlCustomHeightContext:()=>r,ControlCustomWidthContext:()=>i});var o=n(50959);const i=o.createContext({}),r=o.createContext({})},82064:(e,t,n)=>{"use strict";n.d(t,{Section:()=>tn});var o=n(50959),i=n(295),r=n(48897),a=n(45560),l=n(31356);function s(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r},title:s,solutionId:c},offset:d}=e,[u]=(0,a.useDefinitionProperty)({property:i,defaultValue:!1
}),[p]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0});return p?o.createElement(l.CommonSection,{id:t,offset:d,checked:n,title:s,solutionId:c,disabled:e.disabled||u}):null}var c=n(97754),d=n.n(c),u=n(22064),p=n(53598);function h(e){const{property:t}=e,[n,i]=(0,a.useDefinitionProperty)({property:t});return o.createElement(p.LineStyleSelect,{...e,lineStyle:n,lineStyleChange:i})}var m=n(50890),f=n(60521),g=n(50151);function v(e){return"mixed"===e}function y(e,t,n){const[i,r]=(0,o.useState)(e),a=(0,o.useRef)(i);return(0,o.useEffect)((()=>{r(e)}),[e,n]),[i,function(e){a.current=e,r(e)},function(){t(a.current)},function(){a.current=e,r(e)}]}var b=n(68335),E=n(92399),D=n(37160),_=n(87663),w=n(49483);function C(e){const{property:t,...n}=e,[i,r]=(0,o.useState)(performance.now()),[l,s]=(0,a.useDefinitionProperty)({property:t,handler:()=>r(performance.now())}),c=y(l,s,i);return o.createElement(S,{...n,valueHash:i,sharedBuffer:c})}function S(e){const{sharedBuffer:t,min:n,max:i,step:r,...a}=e,[l,s,c,d]=t,u=(0,o.useRef)(null),p=(0,o.useRef)(null),h={flushed:!1};return o.createElement(P,{...a,ref:p,onValueChange:function(e,t){s(e),"step"!==t||h.flushed||(c(),h.flushed=!0)},onKeyDown:function(e){if(e.defaultPrevented||h.flushed)return;switch((0,b.hashFromEvent)(e.nativeEvent)){case 27:d(),h.flushed=!0;break;case 13:e.preventDefault();const t=(0,g.ensureNotNull)(p.current).getClampedValue();null!==t&&(s(t),c(),h.flushed=!0)}},onBlur:function(e){const t=(0,g.ensureNotNull)(u.current);if(!t.contains(document.activeElement)&&!t.contains(e.relatedTarget)){const e=(0,g.ensureNotNull)(p.current).getClampedValue();null===e||h.flushed||(s(e),c(),h.flushed=!0)}},value:l,roundByStep:!1,containerReference:function(e){u.current=e},inputMode:w.CheckMobile.iOS()?void 0:"numeric",min:n,max:i,step:r,stretch:!1})}const x={mode:"float",min:-Number.MAX_VALUE,max:Number.MAX_VALUE,step:1,precision:0,inheritPrecisionFromStep:!0};class P extends o.PureComponent{constructor(e){super(e),this._selection=null,this._restoreSelection=!1,this._input=null,this._handleSelectionChange=()=>{this._restoreSelection||document.activeElement!==(0,g.ensureNotNull)(this._input)||this._saveSelection((0,g.ensureNotNull)(this._input))},this._handleInputReference=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(e)},this._onFocus=e=>{this._saveSelection((0,g.ensureNotNull)(this._input)),this.setState({focused:!0}),this.props.onFocus&&this.props.onFocus(e)},this._onBlur=e=>{this._selection=null,this.setState({displayValue:N(this.props,this.props.value,k(this.props)),focused:!1}),this.props.onBlur&&this.props.onBlur(e)},this._onValueChange=e=>{const t=e.currentTarget,n=t.value,o=function(e,t,n){switch(n){case"integer":return T.test(t)?t:e;case"float":return t=t.replace(/,/g,"."),V.test(t)?t:e}}(this.state.displayValue,n,this.props.mode),i=I(o),r=this._checkValueBoundaries(i);var a,l;this.setState({displayValue:o}),o!==n&&(a=this.state.displayValue,l=(l=o).replace(/,/g,"."),
(a=a.replace(/,/g,".")).includes(".")||!l.includes("."))?(this._restoreSelection=!0,this.forceUpdate()):this._saveSelection(t),r.value&&N(this.props,i)===o&&this.props.onValueChange(i,"input")},this._onValueByStepChange=e=>{const{roundByStep:t=!0,step:n=1}=this.props,o=I(this.state.displayValue);let i;if(isNaN(o)){const{defaultValue:e}=this.props;if(void 0===e)return;i=e}else{const r=new f.Big(o),a=new f.Big(n),l=r.mod(a);let s=r.plus(e*n);!l.eq(0)&&t&&(s=s.plus((e>0?0:1)*n).minus(l)),i=s.toNumber()}this._checkValueBoundaries(i).value&&(this.setState({displayValue:N(this.props,i,k(this.props))}),this.props.onValueChange(i,"step"))},this.state={value:B(this.props.value),displayValue:N(this.props,this.props.value,k(this.props)),focused:!1,valueHash:this.props.valueHash}}componentDidMount(){document.addEventListener("selectionchange",this._handleSelectionChange)}componentWillUnmount(){document.removeEventListener("selectionchange",this._handleSelectionChange)}componentDidUpdate(){const e=(0,g.ensureNotNull)(this._input),t=this._selection;if(null!==t&&this._restoreSelection&&document.activeElement===e){const{start:n,end:o,direction:i}=t;e.setSelectionRange(n,o,i)}this._restoreSelection=!1}render(){return o.createElement(E.NumberInputView,{type:"text",inputMode:this.props.inputMode,name:this.props.name,fontSizeStyle:"medium",value:this.state.displayValue,className:this.props.className,placeholder:this.props.placeholder,forceShowControls:this.props.forceShowControls,disabled:this.props.disabled,stretch:this.props.stretch,error:Boolean(this.props.error),errorMessage:this.props.error,onValueChange:this._onValueChange,onValueByStepChange:this._onValueByStepChange,containerReference:this.props.containerReference,inputReference:this._handleInputReference,onClick:this.props.onClick,onFocus:this._onFocus,onBlur:this._onBlur,onKeyDown:this.props.onKeyDown,autoSelectOnFocus:!0,"data-name":this.props["data-name"],highlight:this.props.highlight})}getClampedValue(){const{min:e,max:t}=this.props,n=I(this.state.displayValue);return isNaN(n)?null:(0,D.clamp)(n,e,t)}static getDerivedStateFromProps(e,t){const{valueHash:n}=e,o=B(e.value);if(t.value!==o||t.valueHash!==n){return{value:o,valueHash:n,displayValue:N(e,o,t.focused&&t.valueHash===n?void 0:k(e))}}return null}_saveSelection(e){const{selectionStart:t,selectionEnd:n,selectionDirection:o}=e;null!==t&&null!==n&&null!==o&&(this._selection={start:t,end:n,direction:o})}_checkValueBoundaries(e){const{min:t,max:n}=this.props,o=function(e,t,n){const o=e>=t,i=e<=n;return{passMin:o,passMax:i,pass:o&&i,clamped:(0,D.clamp)(e,t,n)}}(e,t,n);return{value:o.pass}}}P.defaultProps=x;const T=/^-?[0-9]*$/,V=/^(-?([0-9]+\.?[0-9]*)|(-?[0-9]*))$/;function N(e,t,n){return v(t=B(t))?"—":(null!==t&&void 0!==n&&(n=Math.max(M(t),n)),function(e,t){if(null===e)return"";return new _.NumericFormatter(t).format(e)}(t,n))}function k(e){let t=0;return e.inheritPrecisionFromStep&&e.step<=1&&(t=M(e.step)),Math.max(e.precision,t)||void 0}function M(e){const t=Math.trunc(e).toString();return(0,
D.clamp)(_.NumericFormatter.formatNoE(e).length-t.length-1,0,15)}function I(e,t){return new _.NumericFormatter(t).parse(e)}function B(e){return"number"==typeof e&&Number.isFinite(e)||v(e)?e:null}var F=n(24377),A=n(58593),L=n(87095);function R(e){const{color:t,thickness:n,thicknessItems:i,noAlpha:r}=e,[l,s]=(0,a.useDefinitionProperty)({property:t}),[c,d]=(0,a.useDefinitionProperty)(n?{property:n}:{defaultValue:void 0});return o.createElement(A.ColorSelect,{...e,color:function(){if(!l)return null;if("mixed"===l)return"mixed";return(0,F.rgbToHexString)((0,F.parseRgb)(l))}(),onColorChange:function(e){const t=l&&"mixed"!==l?(0,L.alphaToTransparency)((0,F.parseRgba)(l)[3]):0;s((0,L.generateColor)(String(e),t,!0))},thickness:c,thicknessItems:i,onThicknessChange:d,opacity:r?void 0:l&&"mixed"!==l?(0,F.parseRgba)(l)[3]:void 0,onOpacityChange:r?void 0:function(e){s((0,L.generateColor)(l,(0,L.alphaToTransparency)(e),!0))}})}var z=n(11542),W=n(73436),G=n(94697),H=n(90186),O=n(43382),U=n(98853),J=n(7625);const Y=[{type:W.LineEnd.Normal,icon:O,label:z.t(null,void 0,n(55362))},{type:W.LineEnd.Arrow,icon:U,label:z.t(null,void 0,n(96237))}];class X extends o.PureComponent{constructor(e){super(e),this._items=[],this._items=Y.map((t=>({value:t.type,selectedContent:o.createElement(G.DisplayItem,{icon:t.icon}),content:o.createElement(G.DropItem,{icon:t.icon,iconClassName:d()(e.isRight&&J.right),label:t.label})})))}render(){const{id:e,lineEnd:t,className:n,lineEndChange:i,isRight:r,disabled:a}=this.props;return o.createElement(G.IconDropdown,{id:e,disabled:a,className:d()(J.lineEndSelect,r&&J.right,n),items:this._items,value:t,onChange:i,hideArrowButton:!0,...(0,H.filterDataProps)(this.props)})}}function $(e){const{property:t}=e,[n,i]=(0,a.useDefinitionProperty)({property:t});return o.createElement(X,{...e,lineEnd:n,lineEndChange:i})}var Z=n(78260),j=n(47543);function K(e){const{children:t,className:n,breakPoint:i="Normal"}=e;return o.createElement(Z.CellWrap,{className:c(j.wrap,n,j[`breakpoint${i}`])},o.Children.map(t,(e=>o.isValidElement(e)?o.createElement("span",{key:null===e.key?void 0:e.key,className:c(j.row,r(e)&&j.empty)},e):e)));function r(e){return!(!o.isValidElement(e)||e.type!==o.Fragment||!Array.isArray(e.props.children))&&e.props.children.every((e=>null===e))}}const q={1:"float",0:"integer"};var Q=n(77975),ee=n(89232);function te(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r,leftEnd:s,rightEnd:d,value:p,extendLeft:f,extendRight:g},title:v,valueMin:y,valueMax:b,valueStep:E,valueUnit:D,extendLeftTitle:_,extendRightTitle:w,solutionId:S},offset:x}=e,[P]=(0,a.useDefinitionProperty)({property:n,defaultValue:!0}),[T]=(0,a.useDefinitionProperty)({property:i,defaultValue:!1}),[V]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0}),N=(0,Q.useWatchedValueReadonly)({watchedValue:y,defaultValue:void 0}),k=(0,Q.useWatchedValueReadonly)({watchedValue:b,defaultValue:void 0}),M=(0,Q.useWatchedValueReadonly)({watchedValue:E,defaultValue:void 0}),I=(0,Q.useWatchedValueReadonly)({watchedValue:D,defaultValue:void 0
}),B=e.disabled||!P;return V?o.createElement(o.Fragment,null,o.createElement(l.CommonSection,{id:t,offset:x,checked:n,title:v,solutionId:S,disabled:e.disabled||T},o.createElement(K,{className:ee.line,breakPoint:"Small"},o.createElement(o.Fragment,null,function(){const{definition:{properties:{color:n,width:i},widthValues:r}}=e;if(n)return o.createElement("span",{className:ee.control},o.createElement(R,{color:n,thickness:i,disabled:B,thicknessItems:r}));return i&&o.createElement("span",{className:ee.control},o.createElement(m.LineWidthSelect,{id:(0,u.createDomId)(t,"line-width-select"),items:r,property:i,disabled:B}))}(),function(){const{definition:{properties:{style:n}}}=e;return n&&o.createElement("span",{className:ee.control},o.createElement(h,{id:(0,u.createDomId)(t,"line-style-select"),property:n,disabled:B}))}()),(s||d||p)&&o.createElement(o.Fragment,null,o.createElement(o.Fragment,null,s&&o.createElement($,{id:(0,u.createDomId)(t,"left-end-select"),"data-name":"left-end-select",className:ee.control,property:s,disabled:B}),d&&o.createElement($,{id:(0,u.createDomId)(t,"right-end-select"),"data-name":"right-end-select",className:ee.control,property:d,disabled:B,isRight:!0})),function(){const{definition:{valueType:t}}=e;return p&&o.createElement("span",{className:c(ee.valueInput,ee.control)},o.createElement(C,{className:ee.input,property:p,min:N,max:k,step:M,disabled:B,mode:void 0!==t?q[t]:void 0,name:"line-value-input"}),o.createElement("span",{className:ee.valueUnit},I))}()))),f&&o.createElement(l.CommonSection,{id:`${t}ExtendLeft`,offset:x,checked:f,title:_,disabled:e.disabled||T}),g&&o.createElement(l.CommonSection,{id:`${t}ExtendRight`,offset:x,checked:g,title:w,disabled:e.disabled||T})):null}var ne=n(93613),oe=n(90405),ie=n(36947);function re(e){const{property:t,options:n,...i}=e,[r,l]=(0,a.useDefinitionProperty)({property:t}),s=(0,ie.useForceUpdate)();return(0,o.useEffect)((()=>{const e=()=>s();return Array.isArray(n)||n.subscribe(e),()=>{Array.isArray(n)||n.unsubscribe(e)}}),[]),o.createElement(oe.Select,{...i,onChange:l,value:r,items:(Array.isArray(n)?n:n.value()).map((e=>e.readonly?{content:e.title,readonly:e.readonly}:{content:e.title,value:e.value,disabled:e.disabled,id:e.id}))})}var ae=n(3115);const le=[{title:z.t(null,void 0,n(35637)),value:ne.ColorType.Solid},{title:z.t(null,void 0,n(16079)),value:ne.ColorType.Gradient}];function se(e){const{id:t,disabled:n,noAlpha:i,properties:r}=e,{color:l,gradientColor1:s,gradientColor2:c,type:d}=r,[p]=(0,a.useDefinitionProperty)({property:d,defaultValue:ne.ColorType.Solid});return o.createElement(K,null,o.createElement(re,{id:(0,u.createDomId)(t,"background-type-options-dropdown"),"data-name":"background-type-options-dropdown",className:ae.dropdown,menuClassName:ae.dropdownMenu,disabled:n,property:d,options:le}),p===ne.ColorType.Solid?o.createElement(R,{color:l,disabled:n,noAlpha:i}):o.createElement(o.Fragment,null,o.createElement(R,{className:ae.firstColorPicker,color:s,disabled:n,noAlpha:i}),o.createElement(R,{color:c,disabled:n,noAlpha:i})))}function ce(e){
const{definition:{id:t,properties:n,title:i,noAlpha:r,solutionId:s},offset:c}=e,{color:d,checked:u,disabled:p,visible:h}=n,[m]=(0,a.useDefinitionProperty)({property:u,defaultValue:!0}),[f]=(0,a.useDefinitionProperty)({property:p,defaultValue:!1}),[g]=(0,a.useDefinitionProperty)({property:h,defaultValue:!0}),v=e.disabled||!m;return g?o.createElement(l.CommonSection,{id:t,offset:c,checked:u,title:i,solutionId:s,disabled:e.disabled||f},o.createElement(Z.CellWrap,null,n.hasOwnProperty("type")?o.createElement(se,{id:t,properties:n,disabled:v,noAlpha:r}):o.createElement(R,{color:d,disabled:v,noAlpha:r}))):null}var de=n(54368),ue=n(33013),pe=n(45707);function he(e){const{value:t,disabled:n,onChange:i,className:r}=e;return o.createElement("div",{className:c(pe.wrap,r,{[pe.disabled]:n})},o.createElement(de.Opacity,{hideInput:!0,color:ue.colorsPalette["color-tv-blue-500"],opacity:1-t/100,onChange:function(e){n||i(100-100*e)},disabled:n}))}function me(e){const{property:t,...n}=e,[i,r]=(0,a.useDefinitionProperty)({property:t});return o.createElement(he,{...n,value:i,onChange:r})}function fe(e){const{definition:{id:t,properties:{transparency:n,checked:i,disabled:r,visible:s},title:c,solutionId:d},offset:u}=e,[p]=(0,a.useDefinitionProperty)({property:i,defaultValue:!0}),[h]=(0,a.useDefinitionProperty)({property:r,defaultValue:!1}),[m]=(0,a.useDefinitionProperty)({property:s,defaultValue:!0}),f=e.disabled||!p;return m?o.createElement(l.CommonSection,{id:t,offset:u,checked:i,title:c,solutionId:d,disabled:e.disabled||h},o.createElement(Z.CellWrap,null,o.createElement(me,{property:n,disabled:f}))):null}var ge=n(81364);function ve(e){const{definition:{id:t,properties:{color1:n,color2:i,checked:r,disabled:s,visible:c},title:d,noAlpha1:u,noAlpha2:p,solutionId:h},offset:m}=e,[f]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0}),[g]=(0,a.useDefinitionProperty)({property:s,defaultValue:!1}),[v]=(0,a.useDefinitionProperty)({property:c,defaultValue:!0}),y=e.disabled||!f||g;return v?o.createElement(l.CommonSection,{id:t,offset:m,checked:r,solutionId:h,title:d,disabled:e.disabled||g},o.createElement(Z.CellWrap,{className:ge.twoColors},b(n,u),b(i,p))):null;function b(e,t){return o.createElement("span",{className:ge.colorPicker},o.createElement(R,{color:e,disabled:y,noAlpha:t}))}}var ye=n(66849),be=n(76739);function Ee(e){const{definition:{id:t,properties:{checked:n,value:i,unitOptionsValue:r,disabled:s,visible:d},min:p,max:h,step:m,title:f,unit:v,unitOptions:y,type:b,solutionId:E},offset:D}=e,[_]=(0,a.useDefinitionProperty)({property:n,defaultValue:!0}),[w]=(0,a.useDefinitionProperty)({property:s,defaultValue:!1}),[S]=(0,a.useDefinitionProperty)({property:d,defaultValue:!0}),x=(0,Q.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),P=(0,Q.useWatchedValueReadonly)({watchedValue:h,defaultValue:void 0}),T=(0,Q.useWatchedValueReadonly)({watchedValue:m,defaultValue:void 0}),V=(0,Q.useWatchedValueReadonly)({watchedValue:v,defaultValue:void 0}),N=(0,o.useContext)(ye.ControlCustomWidthContext),k=e.disabled||!_
;return S?o.createElement(l.CommonSection,{id:t,offset:D,checked:n,title:f,solutionId:E,disabled:e.disabled||w},o.createElement(Z.CellWrap,null,o.createElement(K,null,o.createElement(C,{className:c(be.input,N[t]&&be[N[t]]),property:i,min:x,max:P,step:T,disabled:k,mode:q[b],name:"number-input","data-name":t}),r&&o.createElement(re,{id:(0,u.createDomId)(t,"unit-options-dropdown"),"data-name":"unit-options-dropdown",className:be.dropdown,menuClassName:be.dropdownMenu,disabled:k,property:r,options:(0,g.ensureDefined)(y)})),V&&o.createElement("span",{className:be.unit},V))):null}function De(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r},childrenDefinitions:s,title:c},offset:d}=e,[u]=(0,a.useDefinitionProperty)({property:n,defaultValue:!0}),[p]=(0,a.useDefinitionProperty)({property:i,defaultValue:!1}),[h]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0}),m=e.disabled||!u;return h?o.createElement(o.Fragment,null,o.createElement(l.CommonSection,{id:t,offset:d,checked:n,title:c,disabled:e.disabled||p}),s.map((e=>o.createElement(tn,{key:e.id,disabled:m,definition:e,offset:!0})))):null}var _e=n(66045);function we(e){const{property:t}=e,[n,i]=(0,a.useDefinitionProperty)({property:t});return o.createElement(_e.FontSizeSelect,{...e,fontSize:n,fontSizeChange:i,"data-name":"font-size-select"})}var Ce=n(9745),Se=n(68089);function xe(e){const{className:t,checked:n,icon:i,disabled:r,onClick:a}=e;return o.createElement("div",{className:d()(t,Se.container,n&&!r&&Se.active,r&&Se.disabled),onClick:r?void 0:a,"data-role":"button",...(0,H.filterDataProps)(e)},o.createElement(Ce.Icon,{className:Se.icon,icon:i}))}function Pe(e){const{icon:t,className:n,property:i,disabled:r}=e,[l,s]=(0,a.useDefinitionProperty)({property:i});return o.createElement(xe,{className:n,icon:t,checked:l,onClick:function(){s(!l)},disabled:r,...(0,H.filterDataProps)(e)})}var Te=n(67029),Ve=n(11062),Ne=n(2568);function ke(e){const{property:t,...n}=e,[i,r]=(0,a.useDefinitionProperty)({property:t}),l=(0,o.useCallback)((e=>r(e.target.value)),[r]);return o.createElement(Ne.Textarea,{...n,value:i,onChange:l})}var Me=n(8295),Ie=n(29285),Be=n(42793);const Fe=e=>({content:e.title,title:e.title,value:e.value,id:e.id}),Ae=e=>({content:e.title,title:e.title,value:e.value,id:e.id});function Le(e){const{definition:{id:t,properties:{color:n,size:i,checked:r,disabled:s,bold:c,italic:d,text:p,alignmentHorizontal:h,alignmentVertical:m,orientation:f,backgroundVisible:g,backgroundColor:v,borderVisible:y,borderColor:b,borderWidth:E,wrap:D},title:_,solutionId:w,sizeItems:C,alignmentTitle:S,alignmentHorizontalItems:x,alignmentVerticalItems:P,orientationTitle:T,orientationItems:V,backgroundTitle:N,borderTitle:k,borderWidthItems:M,wrapTitle:I},offset:B}=e,F=(0,o.useContext)(ye.ControlCustomHeightContext),[A]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0}),[L]=(0,a.useDefinitionProperty)({property:s,defaultValue:!1}),[z,W]=(0,a.useDefinitionProperty)({property:m,defaultValue:void 0}),[G,H]=(0,a.useDefinitionProperty)({property:f,defaultValue:"horizontal"
}),[O,U]=(0,a.useDefinitionProperty)({property:h,defaultValue:void 0}),[J]=(0,a.useDefinitionProperty)({property:g,defaultValue:!1}),[Y]=(0,a.useDefinitionProperty)({property:y,defaultValue:!1}),X=e.disabled||!A;return o.createElement(o.Fragment,null,function(){if(_)return o.createElement(l.CommonSection,{id:t,offset:B,checked:r,title:_,solutionId:w,disabled:e.disabled||L},o.createElement(K,{breakPoint:"Small"},q(),Q()));return o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{placement:"first",colSpan:2,offset:B,"data-section-name":t},q(),Q(),w&&!1))}(),p&&o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{placement:"first",colSpan:2,offset:B,"data-section-name":t},o.createElement(ke,{className:Te.InputClasses.FontSizeMedium,rows:($=F[t],"big"===$?9:5),stretch:!0,property:p,disabled:X,onFocus:function(e){e.target.select()},name:"text-input"}))),(h||m)&&o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:B,"data-section-name":t},o.createElement(Z.CellWrap,null,S)),o.createElement(Ve.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},o.createElement(K,{breakPoint:"Small"},void 0!==z&&void 0!==P&&o.createElement(oe.Select,{id:(0,u.createDomId)(t,"alignment-vertical-select"),"data-name":"alignment-vertical-select",className:Be.dropdown,menuClassName:Be.dropdownMenu,disabled:X,value:z,items:P.map(Fe),onChange:W}),void 0!==O&&void 0!==x&&o.createElement(oe.Select,{id:(0,u.createDomId)(t,"alignment-horizontal-select"),"data-name":"alignment-horizontal-select",className:Be.dropdown,menuClassName:Be.dropdownMenu,disabled:X,value:O,items:x.map(Fe),onChange:U})))),void 0!==f&&void 0!==V&&o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:B,"data-section-name":t},o.createElement(Z.CellWrap,null,T)),o.createElement(Ve.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},o.createElement(K,{breakPoint:"Small"},o.createElement(oe.Select,{id:(0,u.createDomId)(t,"orientation-select"),"data-name":"orientation-select",className:Be.dropdown,menuClassName:Be.dropdownMenu,disabled:X,value:G,items:V.map(Ae),onChange:H})))),ee(N,g,v,!!g&&!J),ee(k,y,b,!!y&&!Y,E,M),D&&o.createElement(l.CommonSection,{id:`${t}Wrap`,offset:B,checked:D,title:I,disabled:e.disabled||L}));var $;function j(e,t,n){return e?o.createElement(Pe,{className:Be.fontStyleButton,icon:t,property:e,disabled:X,"data-name":n}):null}function q(){return o.createElement(o.Fragment,null,n&&o.createElement("div",{className:Be.colorPicker},o.createElement(R,{color:n,disabled:X})),i&&C&&o.createElement(we,{id:(0,u.createDomId)(t,"font-size-select"),property:i,fontSizes:C,disabled:X}))}function Q(){return o.createElement(o.Fragment,null,j(c,Me,"toggle-bold"),j(d,Ie,"toggle-italic"))}function ee(e,n,i,r,a,s){return i||n?o.createElement(l.CommonSection,{id:`${t}ColorSelect`,offset:B,checked:n,title:e,
disabled:X},i&&o.createElement(R,{color:i,thickness:a,thicknessItems:s,disabled:X||r})):null}}var Re=n(86623),ze=n(1722);function We(e){const{property:t,mathOperations:n="+/*",mode:i="float",disabled:r,...l}=e,[s,c]=(0,o.useState)(performance.now()),[d,u]=(0,a.useDefinitionProperty)({property:t,handler:()=>c(performance.now())}),[p,h,m,f]=y(d,u,s),g=(0,o.useMemo)((()=>{const e=new RegExp(`^[${n.split("").join("\\")}-]?(${"float"===i?"(\\d+\\.\\d*)|":""}(\\d*))$`);return t=>(0,ze.isString)(t)&&e.test(t)}),[n,i]);return o.createElement(Re.FormInput,{...l,type:"text",value:p,onChange:function(e){const{value:t}=e.currentTarget;h(g(t)?t:p)},onKeyDown:function(e){if(e.defaultPrevented)return;switch((0,b.hashFromEvent)(e.nativeEvent)){case 27:f();break;case 13:v()}},onBlur:function(){v()},disabled:r,stretch:!1,autoSelectOnFocus:!0});function v(){p.length&&m()}}var Ge=n(50540);function He(e){const{definition:{properties:{x:t,y:n,disabled:i},id:r,title:a,solutionId:l},definition:s,offset:c}=e,d=i&&i.value()||e.disabled;return o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{verticalAlign:"top",placement:"first",offset:c,"data-section-name":r},o.createElement("span",{className:Ge.coordinates},a)),(t||n)&&o.createElement(Ve.PropertyTable.Cell,{placement:"last",offset:c,"data-section-name":r},o.createElement(K,{breakPoint:"Medium"},"coordinates"===s.propType?o.createElement(Oe,{definition:s,disabled:d}):o.createElement(Ue,{definition:s,disabled:d})),l&&!1))}function Oe(e){const{definition:{properties:{x:t,y:n},minX:i,maxX:r,stepX:a,minY:l,maxY:s,stepY:c,typeX:d,typeY:u},disabled:p}=e,h=(0,Q.useWatchedValueReadonly)({watchedValue:i,defaultValue:void 0}),m=(0,Q.useWatchedValueReadonly)({watchedValue:r,defaultValue:void 0}),f=(0,Q.useWatchedValueReadonly)({watchedValue:a,defaultValue:void 0}),g=(0,Q.useWatchedValueReadonly)({watchedValue:l,defaultValue:void 0}),v=(0,Q.useWatchedValueReadonly)({watchedValue:s,defaultValue:void 0}),y=(0,Q.useWatchedValueReadonly)({watchedValue:c,defaultValue:void 0});return o.createElement(o.Fragment,null,n&&o.createElement(C,{className:Ge.input,property:n,min:g,max:v,step:y,disabled:p,name:"y-input",mode:void 0!==u?q[u]:"integer"}),t&&o.createElement(C,{className:Ge.input,property:t,min:h,max:m,step:f,disabled:p,name:"x-input",mode:void 0!==d?q[d]:"integer"}))}function Ue(e){const{definition:{properties:{x:t,y:i},mathOperationsX:r,mathOperationsY:a,modeX:l,modeY:s},disabled:c}=e;return o.createElement("div",{className:Ge.selectionCoordinates},o.createElement("div",{className:Ge.selectionCoordinates__inputs},i&&o.createElement(We,{property:i,mathOperations:a,mode:s,disabled:c,className:Ge.input,placeholder:z.t(null,void 0,n(95166))}),t&&o.createElement(We,{property:t,mathOperations:r,mode:l,disabled:c,className:Ge.input,placeholder:z.t(null,void 0,n(76080))})),o.createElement("div",{className:Ge.selectionCoordinates__description},z.t(null,void 0,n(78019))))}var Je=n(13784);function Ye(e){
const{definition:{id:t,properties:{checked:n,option:i,disabled:r,visible:s},title:c,solutionId:p,options:h},offset:m}=e,[f]=(0,a.useDefinitionProperty)({property:n,defaultValue:!0}),[g]=(0,a.useDefinitionProperty)({property:r,defaultValue:!1}),[v]=(0,a.useDefinitionProperty)({property:s,defaultValue:!0}),y=(0,o.useContext)(ye.ControlCustomWidthContext),b=e.disabled||!f;return v?o.createElement(l.CommonSection,{id:t,offset:m,checked:n,title:c,solutionId:p,disabled:e.disabled||g},o.createElement(Z.CellWrap,null,o.createElement(re,{id:(0,u.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:d()(Je.dropdown,y[t]&&Je[y[t]]),menuClassName:d()(Je.dropdownMenu,y[t]&&Je[y[t]]),disabled:b||g,property:i,options:h}))):null}var Xe=n(71953);var $e=n(38223),Ze=n(65542);class je extends o.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._rafPosition=null,this._rafDragStop=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null!==this._rafPosition||this.props.disabled||(this._rafPosition=requestAnimationFrame((()=>{const{from:t,to:n,min:o,max:i}=this.props,r=this._getNewPosition(e),a=1===this._detectPointerMode(e),l=a?(0,D.clamp)(r,o,n):t,s=a?n:(0,D.clamp)(r,t,i);l<=s&&this._handleChange(l,s),this._rafPosition=null})))},this._handleDragStop=()=>{null!==this._rafDragStop||this.props.disabled||(this._rafDragStop=requestAnimationFrame((()=>{this.setState({pointerDragMode:0}),this._rafDragStop=null,this.props.onCommit()})))},this._onSliderClick=e=>{w.CheckMobile.any()||(this._handlePosition(e.nativeEvent),this._dragSubscribe())},this._mouseUp=e=>{this._dragUnsubscribe(),this._handlePosition(e),this._handleDragStop()},this._mouseMove=e=>{this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this._handleDragStop()},this.state={pointerDragMode:0}}componentWillUnmount(){null!==this._rafPosition&&(cancelAnimationFrame(this._rafPosition),this._rafPosition=null),null!==this._rafDragStop&&(cancelAnimationFrame(this._rafDragStop),this._rafDragStop=null),this._dragUnsubscribe()}render(){const{className:e,disabled:t,from:n,to:i,min:r,max:a}=this.props,{pointerDragMode:l}=this.state,s=0!==l,d=a-r,u=0===d?r:(n-r)/d,p=0===d?a:(i-r)/d,h=(0,$e.isRtl)()?"right":"left";return o.createElement("div",{className:c(e,Ze.range,t&&Ze.disabled)},o.createElement("div",{className:Ze.rangeSlider,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},o.createElement("div",{className:Ze.rangeSliderMiddleWrap},o.createElement("div",{className:c(Ze.rangeSliderMiddle,s&&Ze.dragged),style:{[h]:100*u+"%",width:100*(p-u)+"%"}})),o.createElement("div",{className:Ze.rangePointerWrap},o.createElement("div",{className:c(Ze.pointer,s&&Ze.dragged),style:{[h]:100*u+"%"},ref:this._refPointer})),o.createElement("div",{
className:Ze.rangePointerWrap},o.createElement("div",{className:c(Ze.pointer,s&&Ze.dragged),style:{[h]:100*p+"%"}}))))}_dragSubscribe(){const e=(0,g.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=(0,g.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_getNewPosition(e){const{min:t,max:n}=this.props,o=n-t,i=(0,g.ensureNotNull)(this._container),r=(0,g.ensureNotNull)(this._pointer),a=i.getBoundingClientRect(),l=r.offsetWidth;let s=e.clientX-l/2-a.left;return(0,$e.isRtl)()&&(s=a.width-s-l),(0,D.clamp)(s/(a.width-l),0,1)*o+t}_detectPointerMode(e){const{from:t,to:n}=this.props,{pointerDragMode:o}=this.state;if(0!==o)return o;const i=this._getNewPosition(e),r=Math.abs(t-i),a=Math.abs(n-i),l=r===a?i<t?1:2:r<a?1:2;return this.setState({pointerDragMode:l}),l}_handleChange(e,t){const{from:n,to:o,onChange:i}=this.props;e===n&&t===o||i(e,t)}}var Ke=n(90692),qe=n(66586);function Qe(e){const{definition:{id:t,properties:{checked:n,disabled:i,from:r,to:s},title:c,solutionId:u,max:p,min:h},offset:m,disabled:f}=e,[g]=(0,a.useDefinitionProperty)({property:n,defaultValue:!0}),[b]=(0,a.useDefinitionProperty)({property:i,defaultValue:!1}),E=(0,Q.useWatchedValueReadonly)({watchedValue:h,defaultValue:void 0}),D=(0,Q.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),[_,w]=(0,a.useDefinitionProperty)({property:r}),[C,x]=(0,a.useDefinitionProperty)({property:s}),P=v(_)||v(C),T=y(P?"mixed":_,(function(e){if(w(e),v(I)){const e=D||100;B(e),x(e)}})),[V,N,k]=T,M=y(P?"mixed":C,(function(e){if(x(e),v(V)){const e=E||0;N(e),w(e)}})),[I,B,F]=M,A=v(V)||v(I),L=f||v(g)||!g,R={flushed:!1};return o.createElement(l.CommonSection,{id:t,offset:m,checked:n,title:c,disabled:f||b},o.createElement(Z.CellWrap,{className:qe.range},function(){if(!E||!D)return null;return o.createElement(Ke.MatchMedia,{rule:"screen and (max-width: 460px)"},(e=>o.createElement(K,{breakPoint:"Medium"},o.createElement(o.Fragment,null,o.createElement("span",{className:qe.valueInput},o.createElement(S,{className:qe.input,sharedBuffer:T,min:E,max:v(I)?D:I,step:1,disabled:L,name:"from-input",mode:"integer",defaultValue:E}),e?o.createElement("span",{className:qe.rangeSlider},"—"):o.createElement(je,{className:d()(qe.rangeSlider,A&&qe.rangeSlider_mixed),from:A?E:V,to:A?D:I,min:E,max:D,onChange:z,onCommit:W,disabled:L}))),o.createElement(o.Fragment,null,o.createElement("span",{className:qe.valueInput},o.createElement(S,{className:qe.input,sharedBuffer:M,min:v(V)?E:V,max:D,step:1,disabled:L,name:"to-input",mode:"integer",defaultValue:D}),u&&!1)))))}()));function z(e,t){N(Math.round(e)),B(Math.round(t))}function W(){R.flushed||(k(),F(),R.flushed=!0)}}var et=n(86067),tt=n(53424),nt=n(54970);function ot(e){const{definitions:t,name:n,offset:i}=e,r=d()(nt.cell,nt.fragmentCell,t.some((e=>void 0!==e.solutionId))&&nt.largeWidth)
;return o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{className:r,offset:i,placement:"first",verticalAlign:"adaptive",colSpan:2,"data-section-name":n,checkableTitle:!0},t.map((e=>o.createElement("div",{className:nt.item,key:e.id,"data-section-name":e.id},o.createElement(rt,{definition:e}))))))}function it(e){const{definition:t,offset:n}=e;return o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{className:nt.cell,offset:n,placement:"first",verticalAlign:"adaptive",colSpan:2,checkableTitle:!0},o.createElement(rt,{definition:t})))}function rt(e){const{definition:{id:t,properties:{disabled:n,checked:i,color:r,level:l,width:s,style:c},solutionId:p,title:m,widthValues:f,styleValues:g}}=e,[v]=(0,a.useDefinitionProperty)({property:i,defaultValue:!0}),[y]=(0,a.useDefinitionProperty)({property:n,defaultValue:!1}),b=y||!v;return o.createElement(o.Fragment,null,o.createElement(tt.CheckableTitle,{name:`is-enabled-${t}`,className:d()(m&&nt.withTitle),title:m&&o.createElement("span",{className:nt.title},m),property:i,disabled:y}),l&&o.createElement(C,{className:d()(nt.input,nt.control),property:l,disabled:b}),r&&o.createElement(R,{className:nt.control,disabled:b,color:r,thickness:s,thicknessItems:f}),c&&o.createElement(h,{id:(0,u.createDomId)(t,"leveled-line-style-select"),className:nt.control,property:c,disabled:b,allowedLineStyles:g}),p&&!1)}var at=n(27394);function lt(e){const{definition:{id:t,properties:{option1:n,option2:i,checked:r,disabled:s},title:c,solutionId:d,optionsItems1:p,optionsItems2:h},offset:m}=e,[f]=(0,a.useDefinitionProperty)({property:r,defaultValue:!0}),[g]=(0,a.useDefinitionProperty)({property:s,defaultValue:!1}),v=e.disabled||!f;return o.createElement(l.CommonSection,{id:t,offset:m,checked:r,title:c,solutionId:d,disabled:e.disabled||g},o.createElement(K,{className:at.twoOptions},o.createElement(re,{id:(0,u.createDomId)(t,"two-options-dropdown-1"),"data-name":"two-options-dropdown-1",className:at.dropdown,menuClassName:at.menu,property:n,disabled:v,options:p}),o.createElement(re,{id:(0,u.createDomId)(t,"two-options-dropdown-2"),"data-name":"two-options-dropdown-2",className:at.dropdown,menuClassName:at.menu,property:i,disabled:v,options:h})))}var st=n(22332);function ct(e){const{definition:{id:t,properties:{color1:n,color2:i,option:r},options:a,color1Visible:s,color2Visible:c,title:p,noAlpha1:h,noAlpha2:m,solutionId:f},offset:g}=e,v=(0,Q.useWatchedValueReadonly)({watchedValue:s,defaultValue:!1}),y=(0,Q.useWatchedValueReadonly)({watchedValue:c,defaultValue:!1}),b=(0,o.useContext)(ye.ControlCustomWidthContext);return o.createElement(l.CommonSection,{id:t,offset:g,solutionId:f,title:p},o.createElement(Z.CellWrap,{className:st.optionalTwoColors},o.createElement(K,null,o.createElement(re,{id:(0,u.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:d()(st.dropdown,b[t]&&st[b[t]]),menuClassName:d()(st.dropdownMenu,b[t]&&st[b[t]]),property:r,options:a}),o.createElement(o.Fragment,null,v&&E(n,h),y&&E(i,m)))));function E(e,t){
return o.createElement("span",{className:st.colorPicker},o.createElement(R,{color:e,noAlpha:t}))}}var dt=n(39828),ut=n(22497);function pt(e){const{source:t,inputs:n,model:i,inputsTabProperty:r,studyMetaInfo:a}=e.definition;return o.createElement(dt.InputsTabContent,{className:ut.withoutPadding,property:r,model:i,study:t,studyMetaInfo:a,inputs:n})}var ht=n(56840),mt=n(38297),ft=n(83682),gt=n(173);var vt=n(20520),yt=n(37558),bt=n(41590),Et=n(27317),Dt=n(40173);function _t(e){!function(e,t){(0,o.useEffect)((()=>{const n=t||document;return n.addEventListener("scroll",e),()=>n.removeEventListener("scroll",e)}),[e])}(e,document)}var wt=n(12811),Ct=n(24437),St=n(38446);function xt(e){const{children:t,highlight:n,disabled:i,reference:r,...a}=e,l=n?"primary":"default";return o.createElement("div",{...a,ref:r,className:d()(St.wrapper,St[`intent-${l}`],St["border-thin"],St["size-medium"],n&&St.highlight,n&&St.focused,i&&St.disabled),"data-role":"button"},o.createElement("div",{className:d()(St.childrenContainer,i&&St.disabled)},t),n&&o.createElement("span",{className:St.shadow}))}var Pt=n(64104);const Tt=()=>null,Vt=(0,Dt.mergeThemes)(Et.DEFAULT_MENU_THEME,{menuBox:Pt.menuBox});function Nt(e){const{value:t,disabled:n,onSelect:i}=e,r=(0,o.useRef)(null),{current:a}=(0,o.useRef)(ht.getJSON("RecentlyUsedEmojis",[t])),[l,s]=(0,o.useState)(a),[c,d]=(0,o.useState)(!1),u=(0,o.useCallback)((()=>d(!1)),[]);_t(u);const p=(0,o.useCallback)((e=>{const t=Array.from(new Set([e,...l])).slice(0,18);ht.setJSON("RecentlyUsedEmojis",t),s(t),i(e),u()}),[l,i]),h=(m=l,(0,o.useMemo)((()=>(gt.emojiGroups[0].emojis=m,[...gt.emojiGroups])),[m]));var m;return o.createElement(o.Fragment,null,o.createElement(xt,{reference:r,highlight:c,disabled:n,"data-name":"emoji-picker"},o.createElement(ft.EmojiWrap,{emoji:t,onClick:function(){n||d(!0)}})),o.createElement(Ke.MatchMedia,{rule:Ct.DialogBreakpoints.TabletSmall},(e=>c&&o.createElement(yt.DrawerManager,null,e?o.createElement(bt.Drawer,{className:Pt.drawer,position:"Bottom",onClose:u},o.createElement(mt.EmojiList,{emojis:h,onSelect:p,height:378})):o.createElement(vt.PopupMenu,{theme:Vt,isOpened:!0,position:(0,wt.getPopupPositioner)(r.current,{horizontalDropDirection:wt.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:wt.HorizontalAttachEdge.Left}),onClickOutside:u,onClose:Tt},o.createElement(mt.EmojiList,{className:Pt.desktopSize,emojis:h,onSelect:p,height:378}))))))}var kt=n(35199);function Mt(e){const{definition:{id:t,title:n,properties:i,solutionId:r},offset:s}=e,{checked:c,emoji:d,backgroundColor:u}=i,[p]=(0,a.useDefinitionProperty)({property:c,defaultValue:!1}),[h,m]=(0,a.useDefinitionProperty)({property:d,defaultValue:"🙂"}),[f,g]=(0,a.useDefinitionProperty)({property:u,defaultValue:ue.colorsPalette["color-tv-blue-a600"]}),[v]=(0,a.useDefinitionProperty)({property:i.disabled,defaultValue:!1}),y=e.disabled||!p;return o.createElement(l.CommonSection,{id:t,offset:s,checked:c,title:n,solutionId:r,disabled:e.disabled||v},o.createElement(Nt,{value:h,disabled:y,onSelect:m
}),o.createElement(A.ColorSelect,{className:kt.colorSelect,disabled:y,color:function(){if("mixed"===f)return f;return(0,F.rgbToHexString)((0,F.parseRgb)(f))}(),opacity:f&&"mixed"!==f?(0,F.parseRgba)(f)[3]:void 0,onColorChange:function(e){const t=f&&"mixed"!==f?(0,L.alphaToTransparency)((0,F.parseRgba)(f)[3]):0;g((0,L.generateColor)(String(e),t,!0))},onOpacityChange:function(e){g((0,L.generateColor)(f,(0,L.alphaToTransparency)(e),!0))}}))}function It(e){const{definition:{id:t,properties:{disabled:n,visible:i},childrenDefinitions:r,title:s},offset:c}=e,[d]=(0,a.useDefinitionProperty)({property:n,defaultValue:!1}),[u]=(0,a.useDefinitionProperty)({property:i,defaultValue:!0}),p=e.disabled;return u?o.createElement(o.Fragment,null,s&&o.createElement(l.CommonSection,{id:t,offset:c,title:s,disabled:e.disabled||d}),r.map((e=>o.createElement(tn,{key:e.id,disabled:p,definition:e,offset:Boolean(s)})))):null}var Bt=n(38528),Ft=n(36104),At=n(3343),Lt=n(26597),Rt=n(59054),zt=n(50238),Wt=n(16838),Gt=n(16396),Ht=n(32389),Ot=n(54583);function Ut(e){const{isDisabled:t,hint:n,label:i,isChecked:r,checkboxClassName:a,labelClassName:l,indeterminate:s,isActive:d,checkboxTabIndex:u,checkboxReference:p,checkboxDataRole:h,...m}=e;return o.createElement(Gt.PopupMenuItem,{...m,isDisabled:t,shortcut:n,dontClosePopup:!0,labelRowClassName:l,label:o.createElement(Ht.Checkbox,{reference:p,disabled:t,label:i,checked:r,indeterminate:s,className:c(Ot.checkbox,a),tabIndex:u,"data-role":h})})}var Jt=n(81091);function Yt(e){const[t,n]=(0,zt.useRovingTabindexElement)(null);return o.createElement(Ut,{...e,className:Jt.item,checkboxClassName:Jt.checkbox,checkboxReference:t,checkboxTabIndex:n,checkboxDataRole:Wt.PLATFORM_ACCESSIBILITY_ENABLED?"menuitem":void 0,onKeyDown:function(e){if(!Wt.PLATFORM_ACCESSIBILITY_ENABLED)return;const n=(0,At.hashFromEvent)(e);13!==n&&32!==n||(e.preventDefault(),t.current instanceof HTMLElement&&t.current.click())},"aria-disabled":Wt.PLATFORM_ACCESSIBILITY_ENABLED&&e.isDisabled||void 0})}var Xt=n(81261),$t=n(58516);function Zt(e){return!e.readonly&&!e.disabled}function jt(e){const{selectedItems:t,placeholder:n}=e;if(!t.length)return o.createElement("span",null,n);const i=t.map((e=>{var t,n,o;return null!==(n=null!==(t=e.selectedContent)&&void 0!==t?t:e.content)&&void 0!==n?n:null===(o=e.value)||void 0===o?void 0:o.toString()})).reduce(((e,t,n)=>t?(e.push(o.createElement("span",{key:n,className:$t.contentPart},t)),e.push(o.createElement("span",{key:`separator_${n}`},","," ")),e):e),[]);return i.length&&i.splice(-1),o.createElement("span",{className:$t.btnContent},i)}function Kt(e,t){
const{id:n,items:i,menuClassName:r,menuItemClassName:a,tabIndex:l,disabled:s,highlight:c,intent:d,hideArrowButton:p,placeholder:h,value:m,"aria-labelledby":f,onFocus:g,onBlur:v,onClick:y,onChange:b,onKeyDown:E,openMenuOnEnter:D=!0,"aria-describedby":_,"aria-invalid":w,...C}=e,{listboxId:S,isOpened:x,isFocused:P,buttonTabIndex:T,highlight:V,intent:N,open:k,onOpen:M,close:I,toggle:B,buttonFocusBindings:F,onButtonClick:A,buttonRef:L,listboxRef:R,listboxTabIndex:z,buttonAria:W}=(0,Ft.useControlDisclosure)({id:n,disabled:s,buttonTabIndex:l,intent:d,highlight:c,onFocus:g,onBlur:v,onClick:y}),G=i.filter(Zt).filter((e=>m.some((t=>e.value===t)))),H=(0,u.joinDomIds)(f,n),O=H.length>0?H:void 0,U=(0,o.useMemo)((()=>({role:"listbox","aria-labelledby":f})),[f]),J=(0,Lt.useKeyboardToggle)(B,x||D),Y=(0,Lt.useKeyboardOpen)(x,k),X=(0,Lt.useKeyboardEventHandler)([J,Y]),$=(0,Bt.useMergedRefs)([L,t]);return o.createElement(Rt.ControlDisclosureView,{...C,...W,...F,id:n,role:"button",tabIndex:T,"aria-owns":W["aria-controls"],"aria-haspopup":"listbox","aria-labelledby":O,disabled:s,hideArrowButton:p,isFocused:P,isOpened:x,highlight:V,intent:N,ref:$,onClick:A,onClose:I,onKeyDown:X,onOpen:M,listboxTabIndex:z,listboxId:S,listboxClassName:r,listboxAria:U,"aria-describedby":_,"aria-invalid":w,listboxReference:R,onListboxKeyDown:function(e){switch((0,At.hashFromEvent)(e)){case 27:case 9:return void(x&&(e.preventDefault(),I()))}(0,Xt.handleAccessibleMenuKeyDown)(e)},onListboxFocus:e=>(0,Xt.handleAccessibleMenuFocus)(e,L),buttonChildren:o.createElement(jt,{selectedItems:null!=G?G:null,placeholder:h})},i.map(((e,t)=>{var i,r,l;if(e.readonly)return o.createElement(o.Fragment,{key:`readonly_item_${t}`},e.content);const s=function(e,t){var n;return null!==(n=null==t?void 0:t.id)&&void 0!==n?n:(0,u.createDomId)(e,"item",null==t?void 0:t.value)}(n,e);return o.createElement(Yt,{key:s,id:s,className:a,"aria-selected":m===e.value,isChecked:m.includes(e.value),label:null!==(l=null!==(i=e.content)&&void 0!==i?i:null===(r=e.value)||void 0===r?void 0:r.toString())&&void 0!==l?l:"",onClick:()=>function(e){const t=new Set(m);t.has(e)?t.delete(e):t.add(e);b(Array.from(t))}(e.value),isDisabled:e.disabled})})))}Kt.displayName="Multiselect";const qt=o.forwardRef(Kt);var Qt=n(85357);function en(e){const{definition:t}=e,{checkableListOptions:r,definitions:a}=t,[l,s]=(0,o.useState)(p());(0,o.useEffect)((()=>{const e={},t=()=>{const e=p();s(e)};return t(),r.forEach((n=>{var o;n.properties.checked&&(null===(o=n.properties.checked)||void 0===o||o.subscribe(e,t))})),()=>{r.forEach((n=>{var o;n.properties.checked&&(null===(o=n.properties.checked)||void 0===o||o.unsubscribe(e,t))}))}}),[t]);const c=[],d=[],u=[];return a.value().forEach((e=>{(0,i.isPropertyDefinition)(e)&&((0,i.isColorDefinition)(e)?c.push(e):(0,i.isLineDefinition)(e)?d.push(e):(0,i.isOptionsDefinition)(e)&&u.push(e))})),o.createElement(o.Fragment,null,o.createElement(Ve.PropertyTable.Row,null,o.createElement(Ve.PropertyTable.Cell,{verticalAlign:"topCenter",placement:"first"
},t.title),o.createElement(Ve.PropertyTable.Cell,{placement:"last"},o.createElement("div",{className:Qt.wrap},o.createElement(qt,{className:Qt.select,placeholder:z.t(null,void 0,n(45044)),onChange:function(e){const t=new Set(e);r.forEach((e=>{var n,o;!t.has(e.id)||(null===(n=e.properties.checked)||void 0===n?void 0:n.value())?!t.has(e.id)&&(null===(o=e.properties.checked)||void 0===o?void 0:o.value())&&e.properties.checked.setValue(!1):(0,g.ensureDefined)(e.properties.checked).setValue(!0)}))},value:l,matchButtonAndListboxWidths:!0,items:r.map((e=>({id:e.id,content:(0,g.ensureDefined)(e.title),value:e.id})))}),c.length||d.length?o.createElement("div",{className:Qt.colorsWrap},d.map((e=>o.createElement(R,{key:e.id,color:(0,g.ensureDefined)(e.properties.color),thickness:e.properties.width,thicknessItems:e.widthValues}))),c.map((e=>o.createElement(R,{key:e.id,color:e.properties.color})))):null))),u.map((e=>o.createElement(Ve.PropertyTable.Row,{key:e.id},o.createElement(Ve.PropertyTable.Cell,{placement:"first"}),o.createElement(Ve.PropertyTable.Cell,{placement:"last"},o.createElement(re,{className:Qt.select,property:e.properties.option,options:e.options}))))));function p(){return r.filter((e=>{var t;return(0,g.ensureDefined)(null===(t=e.properties)||void 0===t?void 0:t.checked).value()})).map((e=>e.id))}}function tn(e){const{definition:t,offset:n,disabled:a}=e;if(function(e){(0,o.useEffect)((()=>{if(void 0===e)return;const t={...e.properties};return Object.entries(t).forEach((([n,o])=>{void 0!==o&&o.subscribe(t,(()=>Xe.logger.logNormal(`Property "${n}" in definition "${e.id}" was updated to value "${o.value()}"`)))})),()=>{Object.entries(t).forEach((([,e])=>{null==e||e.unsubscribeAll(t)}))}}),[e])}((0,i.isPropertyDefinitionsGroup)(t)?void 0:t),(0,i.isPropertyDefinitionsGroup)(t))return o.createElement(nn,{definition:t,offset:n,disabled:a});switch(t.propType){case"line":return o.createElement(te,{...e,definition:t});case"checkable":return o.createElement(s,{...e,definition:t});case"color":return o.createElement(ce,{...e,definition:t});case"transparency":return o.createElement(fe,{...e,definition:t});case"twoColors":return o.createElement(ve,{...e,definition:t});case"optionalTwoColors":return o.createElement(ct,{...e,definition:t});case"fourColors":case"soundSelect":case"image":default:return null;case"number":return o.createElement(Ee,{...e,definition:t});case"symbol":return o.createElement(r.SymbolInputsButton,{...e,definition:t});case"text":return o.createElement(Le,{...e,definition:t});case"checkableSet":return o.createElement(De,{...e,definition:t});case"set":return o.createElement(It,{...e,definition:t});case"options":return o.createElement(Ye,{...e,definition:t});case"range":return o.createElement(Qe,{...e,definition:t});case"coordinates":case"selectionCoordinates":return o.createElement(He,{...e,definition:t});case"twoOptions":return o.createElement(lt,{...e,definition:t});case"leveledLine":return o.createElement(it,{...e,definition:t});case"emoji":return o.createElement(Mt,{...e,definition:t});case"studyInputs":
return o.createElement(pt,{...e,definition:t})}}function nn(e){const{definition:t}=e,n=(0,Q.useWatchedValueReadonly)({watchedValue:t.definitions});return(0,Q.useWatchedValueReadonly)({watchedValue:t.visible,defaultValue:!0})?(0,i.isCheckableListOptionsDefinition)(t)?o.createElement(en,{definition:t}):o.createElement(o.Fragment,null,t.title&&o.createElement(et.GroupTitleSection,{title:t.title,name:t.id}),n&&function(e){const t=[];return e.reduce(((e,t)=>{if((0,i.isPropertyDefinitionsGroup)(t)||"leveledLine"!==t.propType)e.push(t);else{const n=e[e.length-1];Array.isArray(n)?n.push(t):e.push([t])}return e}),t)}(n).map((n=>Array.isArray(n)?o.createElement(ot,{key:n[0].id,name:t.id,definitions:n}):o.createElement(tn,{key:n.id,...e,definition:n}))),"general"===t.groupType&&o.createElement(Ve.PropertyTable.GroupSeparator,{size:1})):null}},95711:(e,t,n)=>{"use strict";n.d(t,{PopupContext:()=>o});const o=n(50959).createContext(null)},16181:(e,t,n)=>{"use strict";n.d(t,{PopupDialog:()=>x});var o=n(50959),i=n(97754),r=n(50151),a=n(99663),l=n(67961),s=n(90186),c=n(5734);class d extends o.PureComponent{constructor(){super(...arguments),this._manager=new l.OverlapManager,this._handleSlot=e=>{this._manager.setContainer(e)}}render(){const{rounded:e=!0,shadowed:t=!0,fullscreen:n=!1,darker:r=!1,className:l,backdrop:d,containerTabIndex:u=-1}=this.props,p=i(l,c.dialog,e&&c.rounded,t&&c.shadowed,n&&c.fullscreen,r&&c.darker),h=(0,s.filterDataProps)(this.props),m=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return o.createElement(o.Fragment,null,o.createElement(a.SlotContext.Provider,{value:this._manager},d&&o.createElement("div",{onClick:this.props.onClickBackdrop,className:c.backdrop}),o.createElement("div",{...h,className:p,style:m,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,onKeyDown:this.props.onKeyDown,tabIndex:u,"aria-label":this.props.containerAriaLabel},this.props.children)),o.createElement(a.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:e,left:t,width:n,right:o,top:i,zIndex:r,height:a}=this.props;return{bottom:e,left:t,right:o,top:i,zIndex:r,maxWidth:n,height:a}}}var u=n(86431),p=n(52778),h=n(37160);function m(e,t,n,o){return e+t>o&&(e=o-t),e<n&&(e=n),e}function f(e){return{x:(0,h.clamp)(e.x,20,document.documentElement.clientWidth-20),y:(0,h.clamp)(e.y,20,window.innerHeight-20)}}function g(e){return{x:e.clientX,y:e.clientY}}function v(e){return{x:e.touches[0].clientX,y:e.touches[0].clientY}}class y{constructor(e,t,n={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=e=>{if(0!==e.button||this._isTargetNoDraggable(e))return;e.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const t=f(g(e));this._dragStart(t)},this._onTouchDragStart=e=>{if(this._isTargetNoDraggable(e))return;this._canBeTouchClick=!0,e.preventDefault(),
this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const t=f(v(e));this._dragStart(t)},this._onMouseDragEnd=e=>{e.target instanceof Node&&this._header.contains(e.target)&&e.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=e=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(e){if(e instanceof SVGElement){const t=document.createEvent("SVGEvents");t.initEvent("click",!0,!0),e.dispatchEvent(t)}e instanceof HTMLElement&&e.click()}(e.target))},this._onMouseDragMove=e=>{const t=f(g(e));this._dragMove(t)},this._onTouchDragMove=e=>{this._canBeTouchClick=!1,e.preventDefault();const t=f(v(e));this._dragMove(t)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=e,this._header=t,this._options=n,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(e){this._options=e}_dragStart(e){const t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,finishX:e.x,finishY:e.y,dialogX:t.left,dialogY:t.top};const n=Math.round(t.left),o=Math.round(t.top);this._dialog.style.transform=`translate(${n}px, ${o}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(e){if(this._drag){if(this._drag.finishX=e.x,this._drag.finishY=e.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const t=e.x-this._drag.startX,n=e.y-this._drag.startY;this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+n)}this._frame=null}))}}_moveDialog(e,t){const n=this._dialog.getBoundingClientRect(),{boundByScreen:o}=this._options,i=m(e,n.width,o?0:-1/0,o?window.innerWidth:1/0),r=m(t,n.height,o?0:-1/0,o?window.innerHeight:1/0);this._dialog.style.transform=`translate(${Math.round(i)}px, ${Math.round(r)}px)`}_isTargetNoDraggable(e){return e.target instanceof Element&&null!==e.target.closest("[data-disable-drag]")}}const b={vertical:0};class E{constructor(e,t){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=e,this._guard=t.guard||b,this._calculateDialogPosition=t.calculateDialogPosition,this._initialHeight=e.style.height,
window.addEventListener("resize",this._handleResize)}updateOptions(e){this._guard=e.guard||b,this._calculateDialogPosition=e.calculateDialogPosition}setFullscreen(e){this._isFullscreen!==e&&(this._isFullscreen=e,this.recalculateBounds())}centerAndFit(){const{x:e,y:t}=this.getDialogsTopLeftCoordinates(),n=this._calcAvailableHeight(),o=this._calcDialogHeight();if(n===o)if(this._calculateDialogPosition){const{left:e,top:t}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else this._dialog.style.height=o+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${e}px, ${t}px)`}getDialogsTopLeftCoordinates(){const{clientHeight:e,clientWidth:t}=document.documentElement,n=this._calcDialogHeight(),o=t/2-this._dialog.clientWidth/2,i=e/2-n/2;return{x:Math.round(o),y:Math.round(i)}}recalculateBounds(){var e;const{clientHeight:t,clientWidth:n}=document.documentElement,{vertical:o}=this._guard,i=null===(e=this._calculateDialogPosition)||void 0===e?void 0:e.call(this,this._dialog,{clientWidth:n,clientHeight:t},{vertical:o});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",i){const{left:e,top:t,width:n,height:o}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`,n&&(this._dialog.style.width=`${n}px`,this._dialog.style.minWidth="unset"),o&&(this._dialog.style.height=`${o}px`,this._dialog.style.minHeight="unset")}}else if(i){const{left:e,top:t}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const e=this._dialog.getBoundingClientRect(),i=t-2*o,r=m(e.left,e.width,0,n),a=m(e.top,e.height,o,t);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(r)}px, ${Math.round(a)}px)`,this._dialog.style.height=i<e.height?i+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_calcDialogHeight(){const e=this._calcAvailableHeight();return e<this._dialog.clientHeight?e:this._dialog.clientHeight}_calcAvailableHeight(){return document.documentElement.clientHeight-2*this._guard.vertical}}var D=n(42842),_=n(95711),w=n(99054),C=n(8326);C["tooltip-offset"];class S extends o.PureComponent{constructor(e){super(e),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._handleDialogRef=e=>{const{reference:t}=this.props;this._dialog=e,"function"==typeof t&&t(e)},this._handleFocus=e=>{this._moveToTop()},this._handleMouseDown=e=>{this._moveToTop()},this._handleTouchStart=e=>{this._moveToTop()},this.state={canFitTooltip:!1}}render(){return o.createElement(_.PopupContext.Provider,{value:this},o.createElement(p.OutsideEvent,{mouseDown:!0,touchStart:!0,
handler:this.props.onClickOutside},(e=>o.createElement("div",{ref:e,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"]},o.createElement(d,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:i(C.dialog,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:e,boundByScreen:t,onDragStart:n}=this.props,o=(0,r.ensureNotNull)(this._dialog);if(e){const e=o.querySelector("[data-dragg-area]");if(e&&e instanceof HTMLElement){const i=new y(o,e,{boundByScreen:Boolean(t),onDragStart:n});this._cleanUpFunctions.push((()=>i.destroy())),this._drag=i}}this._prevActiveElement=document.activeElement,this.props.autofocus&&!o.contains(document.activeElement)&&o.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,w.setFixedBodyState)(!0);const{guard:i,calculateDialogPosition:a}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const e=new E(o,{guard:i,calculateDialogPosition:a});this._cleanUpFunctions.push((()=>e.destroy())),this._resize=e}if(this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(o);o.focus()}}componentDidUpdate(){if(this._resize){const{guard:e,calculateDialogPosition:t}=this.props;this._resize.updateOptions({guard:e,calculateDialogPosition:t}),this._resize.setFullscreen(this._isFullScreen())}this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart})}componentWillUnmount(){var e;if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||(null===(e=this._dialog)||void 0===e?void 0:e.contains(document.activeElement))))try{this._prevActiveElement.focus({preventScroll:!0})}catch(e){}for(const e of this._cleanUpFunctions)e();(this._isFullScreen()||this.props.fixedBody)&&(0,w.setFixedBodyState)(!1)}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){null!==this.context&&this.context.moveToTop()}_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(e){if(this._resize&&this._dialog){const{x:t,y:n}=e,{x:o,y:i}=this._resize.getDialogsTopLeftCoordinates();this._dialog.style.setProperty("--animationTranslateStartX",`${t}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${n}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${o}px`),
this._dialog.style.setProperty("--animationTranslateEndY",`${i}px`),this._dialog.classList.add(C.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}S.contextType=D.PortalContext,S.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const x=(0,u.makeOverlapable)(S)},95276:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosure:()=>d});var o=n(50959),i=n(38528),r=n(26597),a=n(59054),l=n(36104),s=n(68335),c=n(66986);const d=o.forwardRef(((e,t)=>{const{id:n,tabIndex:d,disabled:u,highlight:p,intent:h,children:m,onClick:f,onFocus:g,onBlur:v,listboxAria:y,onListboxKeyDown:b,...E}=e,D=(0,o.useRef)({"aria-labelledby":n}),{listboxId:_,isOpened:w,isFocused:C,buttonTabIndex:S,listboxTabIndex:x,highlight:P,intent:T,onOpen:V,close:N,toggle:k,buttonFocusBindings:M,onButtonClick:I,buttonRef:B,listboxRef:F,buttonAria:A}=(0,l.useControlDisclosure)({id:n,disabled:u,buttonTabIndex:d,intent:h,highlight:p,onFocus:g,onBlur:v,onClick:f}),L=(0,r.useKeyboardToggle)(k),R=(0,r.useKeyboardClose)(w,N),z=(0,r.useKeyboardEventHandler)([L,R]);return o.createElement(a.ControlDisclosureView,{...E,...M,...A,id:n,role:"button",tabIndex:S,disabled:u,isOpened:w,isFocused:C,ref:(0,i.useMergedRefs)([B,t]),highlight:P,intent:T,onClose:N,onOpen:V,onClick:I,onKeyDown:z,listboxId:_,listboxTabIndex:x,listboxReference:F,listboxAria:null!=y?y:D.current,onListboxKeyDown:function(e){if(27===(0,s.hashFromEvent)(e))return e.preventDefault(),void N();null==b||b(e)}},m,o.createElement("span",{className:c.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:()=>N()}))}));d.displayName="ControlDisclosure"},86431:(e,t,n)=>{"use strict";n.d(t,{makeOverlapable:()=>r});var o=n(50959),i=n(42842);function r(e){return class extends o.PureComponent{render(){const{isOpened:t,root:n}=this.props;if(!t)return null;const r=o.createElement(e,{...this.props,zIndex:150});return"parent"===n?r:o.createElement(i.Portal,null,r)}}}},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var o=n(11542),i=n(50959),r=n(97754),a=n(9745),l=n(33765),s=n(27306);function c(e){const{className:t,isActive:c,onClick:d,onMouseDown:u,title:p,hidden:h,"data-name":m="remove-button",...f}=e;return i.createElement(a.Icon,{...f,"data-name":m,className:r(s.button,"apply-common-tooltip",c&&s.active,h&&s.hidden,t),icon:l,onClick:d,onMouseDown:u,title:p||o.t(null,void 0,n(34596))})}},81091:e=>{e.exports={checkbox:"checkbox-hcyAOCXc",item:"item-hcyAOCXc"}},12811:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>i,HorizontalDropDirection:()=>a,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>r,getPopupPositioner:()=>c});var o,i,r,a,l=n(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(i||(i={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(r||(r={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(a||(a={}));const s={
verticalAttachEdge:o.Bottom,horizontalAttachEdge:i.Left,verticalDropDirection:r.FromTopToBottom,horizontalDropDirection:a.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{var c,d;const{contentWidth:u,contentHeight:p,availableHeight:h}=n,m=(0,l.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:f=s.horizontalAttachEdge,horizontalDropDirection:g=s.horizontalDropDirection,horizontalMargin:v=s.horizontalMargin,verticalMargin:y=s.verticalMargin,matchButtonAndListboxWidths:b=s.matchButtonAndListboxWidths}=t;let E=null!==(c=t.verticalAttachEdge)&&void 0!==c?c:s.verticalAttachEdge,D=null!==(d=t.verticalDropDirection)&&void 0!==d?d:s.verticalDropDirection;E===o.AutoStrict&&(h<m.y+m.height+y+p?(E=o.Top,D=r.FromBottomToTop):(E=o.Bottom,D=r.FromTopToBottom));const _=E===o.Top?-1*y:y,w=f===i.Right?m.right:m.left,C=E===o.Top?m.top:m.bottom,S={x:w-(g===a.FromRightToLeft?u:0)+v,y:C-(D===r.FromBottomToTop?p:0)+_};return b&&(S.overrideWidth=m.width),S}}},3347:(e,t,n)=>{"use strict";n.d(t,{convertToDefinitionProperty:()=>r,makeProxyDefinitionProperty:()=>i});var o=n(51768);function i(e,t,n){const o=new Map,i=void 0!==t?t[0]:e=>e,r=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,a={value:()=>i(e.value()),setValue:t=>{e.setValue(r(t))},subscribe:(t,n)=>{const i=e=>{n(a)};o.set(n,i),e.subscribe(t,i)},unsubscribe:(t,n)=>{const i=o.get(n);i&&(e.unsubscribe(t,i),o.delete(n))},unsubscribeAll:t=>{e.unsubscribeAll(t),o.clear()},destroy:()=>{e.release(),null==n||n()}};return a}function r(e,t,n,r,a,l,s){const c=i(t.weakReference(),r,l),d=void 0!==r?void 0!==r[1]?r[1]:r[0]:e=>e,u=null!=a?a:o=>e.setProperty(t,d(o),n);return c.setValue=e=>{var t;s&&(0,o.trackEvent)(s.category,s.event,null===(t=s.label)||void 0===t?void 0:t.call(s,e)),u(e)},c}},43715:(e,t,n)=>{"use strict";n.d(t,{createLinePropertyDefinition:()=>s});var o=n(73436),i=n(79849);const r=[i.LINESTYLE_SOLID,i.LINESTYLE_DOTTED,i.LINESTYLE_DASHED],a=[1,2,3,4],l=[o.LineEnd.Normal,o.LineEnd.Arrow];function s(e,t){const n={propType:"line",properties:e,...t};return void 0!==n.properties.style&&(n.styleValues=r),void 0!==n.properties.width&&(n.widthValues=a),void 0===n.properties.leftEnd&&void 0===n.properties.rightEnd||void 0!==n.endsValues||(n.endsValues=l),void 0!==n.properties.value&&void 0===n.valueType&&(n.valueType=1),n}},295:(e,t,n)=>{"use strict";function o(e,t){return{propType:"checkable",properties:e,...t}}function i(e,t,n){return{propType:"checkableSet",properties:e,childrenDefinitions:n,...t}}function r(e,t){return{propType:"color",properties:e,noAlpha:!1,...t}}n.d(t,{convertFromReadonlyWVToDefinitionProperty:()=>z,convertFromWVToDefinitionProperty:()=>R,convertToDefinitionProperty:()=>A.convertToDefinitionProperty,createCheckablePropertyDefinition:()=>o,createCheckableSetPropertyDefinition:()=>i,createColorPropertyDefinition:()=>r,createCoordinatesPropertyDefinition:()=>S,createEmojiPropertyDefinition:()=>k,createLeveledLinePropertyDefinition:()=>d,createLinePropertyDefinition:()=>a.createLinePropertyDefinition,
createNumberPropertyDefinition:()=>u,createOptionalTwoColorsPropertyDefinition:()=>C,createOptionsPropertyDefinition:()=>p,createPropertyDefinitionsGeneralGroup:()=>B,createPropertyDefinitionsLeveledLinesGroup:()=>F,createRangePropertyDefinition:()=>P,createSelectionCoordinatesPropertyDefinition:()=>x,createSessionPropertyDefinition:()=>N,createStudyInputsPropertyDefinition:()=>M,createSymbolPropertyDefinition:()=>V,createTextPropertyDefinition:()=>_,createTransparencyPropertyDefinition:()=>T,createTwoColorsPropertyDefinition:()=>w,createTwoOptionsPropertyDefinition:()=>h,destroyDefinitions:()=>te,getColorDefinitionProperty:()=>X,getLockPriceScaleDefinitionProperty:()=>G,getPriceScaleSelectionStrategyDefinitionProperty:()=>W,getScaleRatioDefinitionProperty:()=>H,getSymbolDefinitionProperty:()=>$,isCheckableListOptionsDefinition:()=>ee,isColorDefinition:()=>q,isLineDefinition:()=>K,isOptionsDefinition:()=>Q,isPropertyDefinition:()=>Z,isPropertyDefinitionsGroup:()=>j,makeProxyDefinitionProperty:()=>A.makeProxyDefinitionProperty});var a=n(43715),l=n(79849);const s=[l.LINESTYLE_SOLID,l.LINESTYLE_DOTTED,l.LINESTYLE_DASHED],c=[1,2,3,4];function d(e,t){const n={propType:"leveledLine",properties:e,...t};return void 0!==n.properties.style&&(n.styleValues=s),void 0!==n.properties.width&&(n.widthValues=c),n}function u(e,t){return{propType:"number",properties:e,type:1,...t}}function p(e,t){return{propType:"options",properties:e,...t}}function h(e,t){return{propType:"twoOptions",properties:e,...t}}var m=n(11542);const f=[{id:"bottom",value:"bottom",title:m.t(null,void 0,n(65994))},{id:"middle",value:"middle",title:m.t(null,void 0,n(76476))},{id:"top",value:"top",title:m.t(null,void 0,n(91757))}],g=[{id:"left",value:"left",title:m.t(null,void 0,n(19286))},{id:"center",value:"center",title:m.t(null,void 0,n(72171))},{id:"right",value:"right",title:m.t(null,void 0,n(21141))}],v=[{id:"horizontal",value:"horizontal",title:m.t(null,void 0,n(77405))},{id:"vertical",value:"vertical",title:m.t(null,void 0,n(44085))}],y=[10,11,12,14,16,20,24,28,32,40].map((e=>({title:String(e),value:e}))),b=[1,2,3,4],E=m.t(null,void 0,n(92960)),D=m.t(null,void 0,n(90581));function _(e,t){const n={propType:"text",properties:e,...t,isEditable:t.isEditable||!1};return void 0!==n.properties.size&&void 0===n.sizeItems&&(n.sizeItems=y),void 0!==n.properties.alignmentVertical&&void 0===n.alignmentVerticalItems&&(n.alignmentVerticalItems=f),void 0!==n.properties.alignmentHorizontal&&void 0===n.alignmentHorizontalItems&&(n.alignmentHorizontalItems=g),(n.alignmentVerticalItems||n.alignmentHorizontalItems)&&void 0===n.alignmentTitle&&(n.alignmentTitle=E),void 0!==n.properties.orientation&&(void 0===n.orientationItems&&(n.orientationItems=v),void 0===n.orientationTitle&&(n.orientationTitle=D)),void 0!==n.properties.borderWidth&&void 0===n.borderWidthItems&&(n.borderWidthItems=b),n}function w(e,t){return{propType:"twoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function C(e,t){return{propType:"optionalTwoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}
function S(e,t){return{propType:"coordinates",properties:e,...t}}function x(e,t){return{propType:"selectionCoordinates",properties:e,...t}}function P(e,t){return{propType:"range",properties:e,...t}}function T(e,t){return{propType:"transparency",properties:e,...t}}function V(e,t){return{propType:"symbol",properties:e,...t}}function N(e,t){return{propType:"session",properties:e,...t}}function k(e,t){return{propType:"emoji",properties:e,...t}}function M(e,t){return{propType:"studyInputs",properties:e,...t}}var I=n(97145);function B(e,t,n,o){return{id:t,title:n,visible:o,groupType:"general",definitions:new I.WatchedValue(e)}}function F(e,t,n){return{id:t,title:n,groupType:"leveledLines",definitions:new I.WatchedValue(e)}}var A=n(3347);function L(e,t,n){const o=new Map,i=void 0!==t?t[0]:e=>e,r=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,a={value:()=>i(e.value()),setValue:t=>{var n;null===(n=e.setValue)||void 0===n||n.call(e,r(t))},subscribe:(t,n)=>{const i=()=>{n(a)};let r=o.get(t);void 0===r?(r=new Map,r.set(n,i),o.set(t,r)):r.set(n,i),e.subscribe(i)},unsubscribe:(t,n)=>{const i=o.get(t);if(void 0!==i){const t=i.get(n);void 0!==t&&(e.unsubscribe(t),i.delete(n))}},unsubscribeAll:t=>{const n=o.get(t);void 0!==n&&(n.forEach(((t,n)=>{e.unsubscribe(t)})),n.clear())}};return n&&(a.destroy=()=>n()),a}function R(e,t,n,o){const i=L(t,o),r=void 0!==o?void 0!==o[1]?o[1]:o[0]:e=>e;return i.setValue=o=>e.setWatchedValue(t,r(o),n),i}function z(e,t){return function(e,t,n,o){const i=new Map;return L({subscribe:(n,o)=>{const r=e=>n(t(e));i.set(n,r),e.subscribe(r,o)},unsubscribe:t=>{if(t){const n=i.get(t);n&&(e.unsubscribe(n),i.delete(t))}else i.clear(),e.unsubscribe()},value:()=>t(e.value())},n,o)}(e,(e=>e),t,(()=>e.release()))}function W(e,t){const n=(0,A.makeProxyDefinitionProperty)(t.weakReference());return n.setValue=t=>e.setPriceScaleSelectionStrategy(t),n}function G(e,t,n,o){const i=(0,A.makeProxyDefinitionProperty)(t.weakReference());return i.setValue=t=>{const i={lockScale:t};e.setPriceScaleMode(i,n,o)},i}function H(e,t,n,o){const i=(0,A.makeProxyDefinitionProperty)(t.weakReference(),o);return i.setValue=o=>{e.setScaleRatioProperty(t,o,n)},i}var O=n(24377),U=n(87095),J=n(49152);function Y(e,t){if((0,U.isHexColor)(e)){const n=(0,O.parseRgb)(e);return(0,O.rgbaToString)((0,O.rgba)(n,(100-t)/100))}return e}function X(e,t,n,o,i){let r;if(null!==n){const e=(0,J.combineProperty)(Y,t.weakReference(),n.weakReference());r=(0,A.makeProxyDefinitionProperty)(e.ownership())}else r=(0,A.makeProxyDefinitionProperty)(t.weakReference(),[()=>Y(t.value(),0),e=>e]);return r.setValue=n=>{i&&e.beginUndoMacro(o),e.setProperty(t,n,o),i&&e.endUndoMacro()},r}function $(e,t,n,o,i,r){const a=[(l=n,s=t,e=>{const t=l(s);if(e===s.value()&&null!==t){const e=t.ticker||t.full_name;if(e)return e}return e}),e=>e];var l,s;const c=(0,A.convertToDefinitionProperty)(e,t,i,a);r&&(c.setValue=r);const d=new Map;c.subscribe=(e,n)=>{const o=e=>{n(c)};d.set(n,o),t.subscribe(e,o)},c.unsubscribe=(e,n)=>{const o=d.get(n);o&&(t.unsubscribe(e,o),d.delete(n))};const u={}
;return o.subscribe(u,(()=>{d.forEach(((e,t)=>{t(c)}))})),c.destroy=()=>{o.unsubscribeAll(u),d.clear()},c}function Z(e){return e.hasOwnProperty("propType")}function j(e){return e.hasOwnProperty("groupType")}function K(e){return"line"===e.propType}function q(e){return"color"===e.propType}function Q(e){return"options"===e.propType}function ee(e){return"checkableListOptions"===e.groupType}function te(e){e.forEach((e=>{var t;if(Z(e)){Object.keys(e.properties).forEach((t=>{const n=e.properties[t];void 0!==n&&void 0!==n.destroy&&n.destroy()}))}else te(e.definitions.value()),null===(t=e.visible)||void 0===t||t.destroy()}))}},44996:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},23851:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},57740:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},80427:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5.5 7a.5.5 0 0 0 0 1h17a.5.5 0 0 0 0-1h-17Zm0 6a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm7 0a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm6.5.5c0-.28.22-.5.5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5ZM7 20a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"/></svg>'},501:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},98853:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 13.5H24m-19.5 0L8 17m-3.5-3.5L8 10"/></svg>'},43382:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 13.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm0 0H24"/></svg>'},8295:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 21h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3c2 0 4 1 4 3 0 1 0 2-1.5 3 1.5.5 2.5 2 2.5 4 0 2.75-2.638 4-5 4zM12 9l.004 3c.39.026.82 0 1.25 0C14.908 12 16 11.743 16 10.5c0-1.1-.996-1.5-2.5-1.5-.397 0-.927-.033-1.5 0zm0 5v5h1.5c1.5 0 3.5-.5 3.5-2.5S15 14 13.5 14c-.5 0-.895-.02-1.5 0z"/></svg>'},29285:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12.143 20l1.714-12H12V7h5v1h-2.143l-1.714 12H15v1h-5v-1h2.143z"/></svg>'}}]);