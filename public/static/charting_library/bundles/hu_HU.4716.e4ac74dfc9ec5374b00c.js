(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4716],{20747:e=>{e.exports="Re"},9846:e=>{e.exports="A"},55765:e=>{e.exports="L"},14642:e=>{e.exports="Dark"},69841:e=>{e.exports="Light"},673:e=>{e.exports=Object.create(null),e.exports.d_dates=["n"],e.exports.h_dates=["ó"],e.exports.m_dates=["hó"],e.exports.s_dates="s",e.exports.in_dates=["-ban/ben"]},97840:e=>{e.exports=["n"]},64302:e=>{e.exports=["ó"]},79442:e=>{e.exports=["hó"]},22448:e=>{e.exports="s"},16493:e=>{e.exports="{title} copy"},13395:e=>{e.exports=["N"]},37720:e=>{e.exports="M"},69838:e=>{e.exports="R"},59231:e=>{e.exports="T"},85521:e=>{e.exports="W"},13994:e=>{e.exports="h"},6791:e=>{e.exports="m"},2949:e=>{e.exports="s"},77297:e=>{e.exports=["Z"]},56723:e=>{e.exports=["Max"]},5801:e=>{e.exports="HL2"},98865:e=>{e.exports="HLC3"},42659:e=>{e.exports="OHLC4"},4292:e=>{e.exports=["Min"]},78155:e=>{e.exports=["Ny"]},88601:e=>{e.exports=Object.create(null),e.exports.Back_input="Back",e.exports.Minimize_input="Minimize",e.exports.CCI_input="CCI",e.exports["Hull MA_input"]="Hull MA",e.exports.UO_input="UO",e.exports.from_input="from",e.exports.to_input="to",e.exports["{number} item_combobox_input"]=["{number} items"],e.exports.Close_input=["Zárás"],e.exports.Style_input="Style",e.exports["Box size assignment method_input"]="Box size assignment method",e.exports["Color bars based on previous close_input"]="Color bars based on previous close",e.exports.Candles_input="Candles",e.exports.Borders_input="Borders",e.exports.Wick_input="Wick",e.exports["HLC bars_input"]="HLC bars",e.exports["Price source_input"]="Price source",e.exports.Type_input="Type",e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]=["Valódi árak mutatása az ártáblázaton (a Heikin-Ashi árak helyett)"],e.exports["Up bars_input"]="Up bars",e.exports["Down bars_input"]="Down bars",e.exports["Projection up bars_input"]="Projection up bars",e.exports["Projection down bars_input"]="Projection down bars",e.exports["Projection up color_input"]="Projection up color",e.exports["Projection down color_input"]="Projection down color",e.exports.Line_input=["Vonal"],e.exports.Fill_input="Fill",e.exports["Up color_input"]="Up color",e.exports["Down color_input"]="Down color",e.exports.Traditional_input="Traditional",e.exports["Box size_input"]="Box size",e.exports["Number of line_input"]="Number of line",e.exports["ATR length_input"]=["ATR Hossz"],e.exports["Reversal amount_input"]="Reversal amount",e.exports["Phantom bars_input"]="Phantom bars",e.exports["One step back building_input"]="One step back building",e.exports.Source_input="Source",e.exports.Wicks_input="Wicks",e.exports.Range_input="Range",e.exports.Length_input=["Hossz"],e.exports.Plot_input="Plot",e.exports.Zero_input="Zero",e.exports.Signal_input="Signal",e.exports.Long_input="Long",e.exports.Short_input="Short",e.exports.UpperLimit_input="UpperLimit",e.exports.LowerLimit_input="LowerLimit",e.exports.Offset_input="Offset",e.exports.length_input=["hossz"],e.exports.mult_input="mult",
e.exports.short_input="short",e.exports.long_input="long",e.exports.Limit_input="Limit",e.exports.Move_input="Move",e.exports.Value_input="Value",e.exports.Method_input="Method",e.exports["Values in status line_input"]="Values in status line",e.exports["Labels on price scale_input"]="Labels on price scale",e.exports["Accumulation/Distribution_input"]=["Akkumuláció/Disztribúció"],e.exports.ADR_B_input="ADR_B",e.exports["Equality Line_input"]="Equality Line",e.exports["Window Size_input"]=["Ablakméret"],e.exports.Sigma_input="Sigma",e.exports["Aroon Up_input"]="Aroon Up",e.exports["Aroon Down_input"]="Aroon Down",e.exports.Upper_input="Upper",e.exports.Lower_input="Lower",e.exports.Deviation_input="Deviation",e.exports["Levels Format_input"]="Levels Format",e.exports["Labels Position_input"]="Labels Position",e.exports["0 Level Color_input"]="0 Level Color",e.exports["0.236 Level Color_input"]="0.236 Level Color",e.exports["0.382 Level Color_input"]="0.382 Level Color",e.exports["0.5 Level Color_input"]="0.5 Level Color",e.exports["0.618 Level Color_input"]="0.618 Level Color",e.exports["0.65 Level Color_input"]="0.65 Level Color",e.exports["0.786 Level Color_input"]="0.786 Level Color",e.exports["1 Level Color_input"]="1 Level Color",e.exports["1.272 Level Color_input"]="1.272 Level Color",e.exports["1.414 Level Color_input"]="1.414 Level Color",e.exports["1.618 Level Color_input"]="1.618 Level Color",e.exports["1.65 Level Color_input"]="1.65 Level Color",e.exports["2.618 Level Color_input"]="2.618 Level Color",e.exports["2.65 Level Color_input"]="2.65 Level Color",e.exports["3.618 Level Color_input"]="3.618 Level Color",e.exports["3.65 Level Color_input"]="3.65 Level Color",e.exports["4.236 Level Color_input"]="4.236 Level Color",e.exports["-0.236 Level Color_input"]="-0.236 Level Color",e.exports["-0.382 Level Color_input"]="-0.382 Level Color",e.exports["-0.618 Level Color_input"]="-0.618 Level Color",e.exports["-0.65 Level Color_input"]="-0.65 Level Color",e.exports.ADX_input="ADX",e.exports["ADX Smoothing_input"]="ADX Smoothing",e.exports["DI Length_input"]="DI Length",e.exports.Smoothing_input="Smoothing",e.exports.ATR_input="ATR",e.exports.Growing_input="Growing",e.exports.Falling_input="Falling",e.exports["Color 0_input"]="Color 0",e.exports["Color 1_input"]="Color 1",e.exports.StdDev_input="StdDev",e.exports.Basis_input="Basis",e.exports.Median_input="Median",e.exports["Bollinger Bands %B_input"]="Bollinger Bands %B",e.exports.Overbought_input="Overbought",e.exports.Oversold_input="Oversold",e.exports["Bollinger Bands Width_input"]="Bollinger Bands Width",e.exports["RSI Length_input"]="RSI Length",e.exports["UpDown Length_input"]="UpDown Length",e.exports["ROC Length_input"]=["ROC Hossz"],e.exports.MF_input="MF",e.exports.resolution_input="resolution",e.exports["Fast Length_input"]="Fast Length",e.exports["Slow Length_input"]="Slow Length",e.exports["Chaikin Oscillator_input"]="Chaikin Oscillator",e.exports.P_input="P",e.exports.X_input="X",e.exports.Q_input="Q",e.exports.p_input="p",e.exports.x_input="x",
e.exports.q_input="q",e.exports.Price_input="Price",e.exports["Chande MO_input"]="Chande MO",e.exports["Zero Line_input"]="Zero Line",e.exports["Color 2_input"]="Color 2",e.exports["Color 3_input"]="Color 3",e.exports["Color 4_input"]="Color 4",e.exports["Color 5_input"]="Color 5",e.exports["Color 6_input"]="Color 6",e.exports["Color 7_input"]="Color 7",e.exports["Color 8_input"]="Color 8",e.exports.CHOP_input="CHOP",e.exports["Upper Band_input"]="Upper Band",e.exports["Lower Band_input"]="Lower Band",e.exports["Smoothing Line_input"]="Smoothing Line",e.exports["Smoothing Length_input"]="Smoothing Length",e.exports["WMA Length_input"]=["WMA hosszúság"],e.exports["Long RoC Length_input"]="Long RoC Length",e.exports["Short RoC Length_input"]="Short RoC Length",e.exports.sym_input="sym",e.exports.Symbol_input="Symbol",e.exports.Correlation_input="Correlation",e.exports.Period_input="Period",e.exports.Centered_input="Centered",e.exports["Detrended Price Oscillator_input"]="Detrended Price Oscillator",e.exports.isCentered_input="isCentered",e.exports.DPO_input="DPO",e.exports["ADX smoothing_input"]="ADX smoothing",e.exports["+DI_input"]="+DI",e.exports["-DI_input"]="-DI",e.exports.DEMA_input="DEMA",e.exports["Multi timeframe_input"]="Multi timeframe",e.exports.Timeframe_input="Timeframe",e.exports["Wait for timeframe closes_input"]="Wait for timeframe closes",e.exports.Divisor_input="Divisor",e.exports.EOM_input="EOM",e.exports["Elder's Force Index_input"]="Elder's Force Index",e.exports.Percent_input="Percent",e.exports.Exponential_input="Exponential",e.exports.Average_input="Average",e.exports["Upper Percentage_input"]="Upper Percentage",e.exports["Lower Percentage_input"]="Lower Percentage",e.exports.Fisher_input="Fisher",e.exports.Trigger_input="Trigger",e.exports.Level_input="Level",e.exports["Trader EMA 1 length_input"]="Trader EMA 1 length",e.exports["Trader EMA 2 length_input"]="Trader EMA 2 length",e.exports["Trader EMA 3 length_input"]="Trader EMA 3 length",e.exports["Trader EMA 4 length_input"]="Trader EMA 4 length",e.exports["Trader EMA 5 length_input"]="Trader EMA 5 length",e.exports["Trader EMA 6 length_input"]="Trader EMA 6 length",e.exports["Investor EMA 1 length_input"]="Investor EMA 1 length",e.exports["Investor EMA 2 length_input"]="Investor EMA 2 length",e.exports["Investor EMA 3 length_input"]="Investor EMA 3 length",e.exports["Investor EMA 4 length_input"]="Investor EMA 4 length",e.exports["Investor EMA 5 length_input"]="Investor EMA 5 length",e.exports["Investor EMA 6 length_input"]="Investor EMA 6 length",e.exports.HV_input="HV",e.exports["Conversion Line Periods_input"]="Conversion Line Periods",e.exports["Base Line Periods_input"]="Base Line Periods",e.exports["Lagging Span_input"]="Lagging Span",e.exports["Conversion Line_input"]="Conversion Line",e.exports["Base Line_input"]="Base Line",e.exports["Leading Span A_input"]=["Lead 1"],e.exports["Leading Span Periods_input"]=["Lagging Span 2 Periods"],e.exports["Leading Shift Periods_input"]="Leading Shift Periods",
e.exports["Plots Background_input"]="Plots Background",e.exports["yay Color 0_input"]="yay Color 0",e.exports["yay Color 1_input"]="yay Color 1",e.exports.Multiplier_input="Multiplier",e.exports["Bands style_input"]="Bands style",e.exports.Middle_input="Middle",e.exports.useTrueRange_input="useTrueRange",e.exports.ROCLen1_input="ROCLen1",e.exports.ROCLen2_input="ROCLen2",e.exports.ROCLen3_input="ROCLen3",e.exports.ROCLen4_input="ROCLen4",e.exports.SMALen1_input="SMALen1",e.exports.SMALen2_input="SMALen2",e.exports.SMALen3_input="SMALen3",e.exports.SMALen4_input="SMALen4",e.exports.SigLen_input="SigLen",e.exports.KST_input="KST",e.exports.Sig_input="Sig",e.exports.roclen1_input="roclen1",e.exports.roclen2_input="roclen2",e.exports.roclen3_input="roclen3",e.exports.roclen4_input="roclen4",e.exports.smalen1_input="smalen1",e.exports.smalen2_input="smalen2",e.exports.smalen3_input="smalen3",e.exports.smalen4_input="smalen4",e.exports.siglen_input="siglen",e.exports["Upper Deviation_input"]="Upper Deviation",e.exports["Lower Deviation_input"]="Lower Deviation",e.exports["Use Upper Deviation_input"]="Use Upper Deviation",e.exports["Use Lower Deviation_input"]="Use Lower Deviation",e.exports.Count_input="Count",e.exports.Crosses_input="Crosses",e.exports.MOM_input="MOM",e.exports.MA_input="MA",e.exports["Length EMA_input"]=["EMA Hossz"],e.exports["Length MA_input"]=["MA Hossz"],e.exports["Fast length_input"]="Fast length",e.exports["Slow length_input"]="Slow length",e.exports["Signal smoothing_input"]="Signal smoothing",e.exports["Simple ma(oscillator)_input"]="Simple ma(oscillator)",e.exports["Simple ma(signal line)_input"]="Simple ma(signal line)",e.exports.Histogram_input="Histogram",e.exports.MACD_input="MACD",e.exports.fastLength_input="fastLength",e.exports.slowLength_input="slowLength",e.exports.signalLength_input="signalLength",e.exports.NV_input="NV",e.exports.OnBalanceVolume_input="OnBalanceVolume",e.exports.Start_input=["Kezdés"],e.exports.Increment_input="Increment",e.exports["Max value_input"]="Max value",e.exports.ParabolicSAR_input="ParabolicSAR",e.exports.start_input="start",e.exports.increment_input="increment",e.exports.maximum_input="maximum",e.exports["Short length_input"]="Short length",e.exports["Long length_input"]="Long length",e.exports.OSC_input="OSC",e.exports.shortlen_input="shortlen",e.exports.longlen_input="longlen",e.exports.PVT_input="PVT",e.exports.ROC_input="ROC",e.exports.RSI_input="RSI",e.exports.RVGI_input="RVGI",e.exports.RVI_input="RVI",e.exports["Long period_input"]="Long period",e.exports["Short period_input"]="Short period",e.exports["Signal line period_input"]="Signal line period",e.exports.SMI_input="SMI",e.exports["SMI Ergodic Oscillator_input"]="SMI Ergodic Oscillator",e.exports.Indicator_input="Indicator",e.exports.Oscillator_input="Oscillator",e.exports.K_input="K",e.exports.D_input="D",e.exports.smoothK_input="smoothK",e.exports.smoothD_input="smoothD",e.exports["%K_input"]="%K",e.exports["%D_input"]="%D",e.exports["Stochastic Length_input"]="Stochastic Length",
e.exports["RSI Source_input"]="RSI Source",e.exports.lengthRSI_input="lengthRSI",e.exports.lengthStoch_input="lengthStoch",e.exports.TRIX_input="TRIX",e.exports.TEMA_input="TEMA",e.exports["Long Length_input"]="Long Length",e.exports["Short Length_input"]="Short Length",e.exports["Signal Length_input"]="Signal Length",e.exports.Length1_input="Length1",e.exports.Length2_input="Length2",e.exports.Length3_input=["Hossz3"],e.exports.length7_input="length7",e.exports.length14_input="length14",e.exports.length28_input="length28",e.exports.VWMA_input="VWMA",e.exports.len_input="len",e.exports["VI +_input"]="VI +",e.exports["VI -_input"]="VI -",e.exports["%R_input"]="%R",e.exports["Jaw Length_input"]="Jaw Length",e.exports["Teeth Length_input"]="Teeth Length",e.exports["Lips Length_input"]="Lips Length",e.exports.Jaw_input="Jaw",e.exports.Teeth_input="Teeth",e.exports.Lips_input="Lips",e.exports["Jaw Offset_input"]="Jaw Offset",e.exports["Teeth Offset_input"]="Teeth Offset",e.exports["Lips Offset_input"]="Lips Offset",e.exports["Down fractals_input"]="Down fractals",e.exports["Up fractals_input"]="Up fractals",e.exports.Periods_input="Periods",e.exports.Shapes_input="Shapes",e.exports["show MA_input"]="show MA",e.exports["MA Length_input"]=["MA hosszúság"],e.exports["Color based on previous close_input"]=["Szín az előző záróár alapján"],e.exports["Rows Layout_input"]="Rows Layout",e.exports["Row Size_input"]="Row Size",e.exports.Volume_input="Volume",e.exports["Value Area volume_input"]="Value Area volume",e.exports["Extend Right_input"]="Extend Right",e.exports["Extend POC Right_input"]="Extend POC Right",e.exports["Extend VAH Right_input"]="Extend VAH Right",e.exports["Extend VAL Right_input"]="Extend VAL Right",e.exports["Value Area Volume_input"]="Value Area Volume",e.exports.Placement_input="Placement",e.exports.POC_input="POC",e.exports["Developing Poc_input"]="Developing Poc",e.exports["Up Volume_input"]="Up Volume",e.exports["Down Volume_input"]="Down Volume",e.exports["Value Area_input"]="Value Area",e.exports["Histogram Box_input"]="Histogram Box",e.exports["Value Area Up_input"]="Value Area Up",e.exports["Value Area Down_input"]="Value Area Down",e.exports["Number Of Rows_input"]="Number Of Rows",e.exports["Ticks Per Row_input"]="Ticks Per Row",e.exports["Up/Down_input"]="Up/Down",e.exports.Total_input="Total",e.exports.Delta_input="Delta",e.exports.Bar_input="Bar",e.exports.Day_input="Day",e.exports["Deviation (%)_input"]="Deviation (%)",e.exports.Depth_input="Depth",e.exports["Extend to last bar_input"]="Extend to last bar",e.exports.Simple_input="Simple",e.exports.Weighted_input="Weighted",e.exports["Wilder's Smoothing_input"]="Wilder's Smoothing",e.exports["1st Period_input"]="1st Period",e.exports["2nd Period_input"]="2nd Period",e.exports["3rd Period_input"]="3rd Period",e.exports["4th Period_input"]="4th Period",e.exports["5th Period_input"]="5th Period",e.exports["6th Period_input"]="6th Period",e.exports["Rate of Change Lookback_input"]="Rate of Change Lookback",
e.exports["Instrument 1_input"]="Instrument 1",e.exports["Instrument 2_input"]="Instrument 2",e.exports["Rolling Period_input"]="Rolling Period",e.exports["Standard Errors_input"]="Standard Errors",e.exports["Averaging Periods_input"]="Averaging Periods",e.exports["Days Per Year_input"]="Days Per Year",e.exports["Market Closed Percentage_input"]="Market Closed Percentage",e.exports["ATR Mult_input"]="ATR Mult",e.exports.VWAP_input="VWAP",e.exports["Anchor Period_input"]="Anchor Period",e.exports.Session_input="Session",e.exports.Week_input="Week",e.exports.Month_input="Month",e.exports.Year_input="Year",e.exports.Decade_input="Decade",e.exports.Century_input="Century",e.exports.Sessions_input="Sessions",e.exports["Each (pre-market, market, post-market)_input"]="Each (pre-market, market, post-market)",e.exports["Pre-market only_input"]="Pre-market only",e.exports["Market only_input"]="Market only",e.exports["Post-market only_input"]="Post-market only",e.exports["Main chart symbol_input"]="Main chart symbol",e.exports["Another symbol_input"]="Another symbol",e.exports["Nothing selected_combobox_input"]="Nothing selected",e.exports["All items_combobox_input"]="All items",e.exports.Cancel_input="Cancel",e.exports.Open_input=["Nyitás"]},54138:e=>{e.exports=["Invert Scale"]},47807:e=>{e.exports="Indexed to 100"},34727:e=>{e.exports="Logarithmic"},19238:e=>{e.exports=["No Overlapping Labels"]},70361:e=>{e.exports="Percent"},72116:e=>{e.exports="Regular"},33021:e=>{e.exports="ETH"},75610:e=>{e.exports="Electronic trading hours"},97442:e=>{e.exports=["Bővített kereskedési órák"]},32929:e=>{e.exports="POST"},56137:e=>{e.exports="PRE"},98801:e=>{e.exports="Postmarket"},56935:e=>{e.exports="Premarket"},63798:e=>{e.exports="RTH"},24380:e=>{e.exports="Regular trading hours"},27991:e=>{e.exports=["Május"]},68716:e=>{e.exports=Object.create(null),e.exports.Technicals_study=["Technikaiak"],e.exports["Average Day Range_study"]="Average Day Range",e.exports["Bull Bear Power_study"]="Bull Bear Power",e.exports["Capital expenditures_study"]=["Tőkeberuházások"],e.exports["Cash to debt ratio_study"]="Cash to debt ratio",e.exports["Debt to EBITDA ratio_study"]="Debt to EBITDA ratio",e.exports["Directional Movement Index_study"]="Directional Movement Index",e.exports.DMI_study="DMI",e.exports["Dividend payout ratio %_study"]="Dividend payout ratio %",e.exports["Equity to assets ratio_study"]="Equity to assets ratio",e.exports["Enterprise value to EBIT ratio_study"]="Enterprise value to EBIT ratio",e.exports["Enterprise value to EBITDA ratio_study"]="Enterprise value to EBITDA ratio",e.exports["Enterprise value to revenue ratio_study"]="Enterprise value to revenue ratio",e.exports["Goodwill, net_study"]="Goodwill, net",e.exports["Ichimoku Cloud_study"]=["Ichimoku Felhő"],e.exports.Ichimoku_study="Ichimoku",e.exports["Moving Average Convergence Divergence_study"]="Moving Average Convergence Divergence",e.exports["Operating income_study"]="Operating income",e.exports["Price to book ratio_study"]="Price to book ratio",
e.exports["Price to cash flow ratio_study"]="Price to cash flow ratio",e.exports["Price to earnings ratio_study"]="Price to earnings ratio",e.exports["Price to free cash flow ratio_study"]="Price to free cash flow ratio",e.exports["Price to sales ratio_study"]="Price to sales ratio",e.exports["Float shares outstanding_study"]="Float shares outstanding",e.exports.Stoch_study="Stoch",e.exports["Total common shares outstanding_study"]="Total common shares outstanding",e.exports["Volume Weighted Average Price_study"]="Volume Weighted Average Price",e.exports["Volume Weighted Moving Average_study"]="Volume Weighted Moving Average",e.exports["Williams Percent Range_study"]="Williams Percent Range",e.exports.Doji_study="Doji",e.exports["Spinning Top Black_study"]=["Fekete Forgó Top"],e.exports["Spinning Top White_study"]=["Fehér Forgó Top"],e.exports["Accounts payable_study"]="Accounts payable",e.exports["Accounts receivables, gross_study"]="Accounts receivables, gross",e.exports["Accounts receivable - trade, net_study"]="Accounts receivable - trade, net",e.exports.Accruals_study="Accruals",e.exports["Accrued payroll_study"]="Accrued payroll",e.exports["Accumulated depreciation, total_study"]="Accumulated depreciation, total",e.exports["Additional paid-in capital/Capital surplus_study"]="Additional paid-in capital/Capital surplus",e.exports["After tax other income/expense_study"]="After tax other income/expense",e.exports["Altman Z-score_study"]="Altman Z-score",e.exports.Amortization_study="Amortization",e.exports["Amortization of intangibles_study"]="Amortization of intangibles",e.exports["Amortization of deferred charges_study"]="Amortization of deferred charges",e.exports["Asset turnover_study"]="Asset turnover",e.exports["Average basic shares outstanding_study"]="Average basic shares outstanding",e.exports["Bad debt / Doubtful accounts_study"]="Bad debt / Doubtful accounts",e.exports["Basic EPS_study"]="Basic EPS",e.exports["Basic earnings per share (Basic EPS)_study"]="Basic earnings per share (Basic EPS)",e.exports["Beneish M-score_study"]="Beneish M-score",e.exports["Book value per share_study"]="Book value per share",e.exports["Buyback yield %_study"]="Buyback yield %",e.exports["Capital and operating lease obligations_study"]="Capital and operating lease obligations",e.exports["Capital expenditures - fixed assets_study"]="Capital expenditures - fixed assets",e.exports["Capital expenditures - other assets_study"]="Capital expenditures - other assets",e.exports["Capitalized lease obligations_study"]="Capitalized lease obligations",e.exports["Cash and short term investments_study"]="Cash and short term investments",e.exports["Cash conversion cycle_study"]="Cash conversion cycle",e.exports["Cash & equivalents_study"]="Cash & equivalents",e.exports["Cash from financing activities_study"]="Cash from financing activities",e.exports["Cash from investing activities_study"]="Cash from investing activities",e.exports["Cash from operating activities_study"]="Cash from operating activities",
e.exports["Change in accounts payable_study"]="Change in accounts payable",e.exports["Change in accounts receivable_study"]="Change in accounts receivable",e.exports["Change in accrued expenses_study"]="Change in accrued expenses",e.exports["Change in inventories_study"]="Change in inventories",e.exports["Change in other assets/liabilities_study"]="Change in other assets/liabilities",e.exports["Change in taxes payable_study"]="Change in taxes payable",e.exports["Changes in working capital_study"]="Changes in working capital",e.exports["COGS to revenue ratio_study"]="COGS to revenue ratio",e.exports["Common dividends paid_study"]="Common dividends paid",e.exports["Common equity, total_study"]="Common equity, total",e.exports["Common stock par/Carrying value_study"]="Common stock par/Carrying value",e.exports["Cost of goods_study"]="Cost of goods",e.exports["Cost of goods sold_study"]="Cost of goods sold",e.exports["Current portion of LT debt and capital leases_study"]="Current portion of LT debt and capital leases",e.exports["Current ratio_study"]="Current ratio",e.exports["Days inventory_study"]="Days inventory",e.exports["Days payable_study"]="Days payable",e.exports["Days sales outstanding_study"]="Days sales outstanding",e.exports["Debt to assets ratio_study"]="Debt to assets ratio",e.exports["Debt to equity ratio_study"]="Debt to equity ratio",e.exports["Debt to revenue ratio_study"]="Debt to revenue ratio",e.exports["Deferred income, current_study"]="Deferred income, current",e.exports["Deferred income, non-current_study"]="Deferred income, non-current",e.exports["Deferred tax assets_study"]="Deferred tax assets",e.exports["Deferred taxes (cash flow)_study"]="Deferred taxes (cash flow)",e.exports["Deferred tax liabilities_study"]="Deferred tax liabilities",e.exports.Depreciation_study="Depreciation",e.exports["Deprecation and amortization_study"]="Deprecation and amortization",e.exports["Depreciation & amortization (cash flow)_study"]="Depreciation & amortization (cash flow)",e.exports["Depreciation/depletion_study"]="Depreciation/depletion",e.exports["Diluted EPS_study"]=["Higított EPS"],e.exports["Diluted earnings per share (Diluted EPS)_study"]="Diluted earnings per share (Diluted EPS)",e.exports["Diluted net income available to common stockholders_study"]="Diluted net income available to common stockholders",e.exports["Diluted shares outstanding_study"]="Diluted shares outstanding",e.exports["Dilution adjustment_study"]="Dilution adjustment",e.exports["Discontinued operations_study"]="Discontinued operations",e.exports["Dividends payable_study"]="Dividends payable",e.exports["Dividends per share - common stock primary issue_study"]="Dividends per share - common stock primary issue",e.exports["Dividend yield %_study"]="Dividend yield %",e.exports["Earnings yield_study"]="Earnings yield",e.exports.EBIT_study="EBIT",e.exports.EBITDA_study="EBITDA",e.exports["EBITDA margin %_study"]="EBITDA margin %",e.exports["Effective interest rate on debt %_study"]="Effective interest rate on debt %",
e.exports["Enterprise value_study"]="Enterprise value",e.exports["EPS basic one year growth_study"]="EPS basic one year growth",e.exports["EPS diluted one year growth_study"]="EPS diluted one year growth",e.exports["EPS estimates_study"]="EPS estimates",e.exports["Equity in earnings_study"]="Equity in earnings",e.exports["Financing activities – other sources_study"]="Financing activities – other sources",e.exports["Financing activities – other uses_study"]="Financing activities – other uses",e.exports["Free cash flow_study"]=["Szabad Cash Flow"],e.exports["Free cash flow margin %_study"]="Free cash flow margin %",e.exports["Fulmer H factor_study"]="Fulmer H factor",e.exports["Funds from operations_study"]="Funds from operations",e.exports["Goodwill to assets ratio_study"]="Goodwill to assets ratio",e.exports["Graham's number_study"]="Graham's number",e.exports["Gross margin %_study"]="Gross margin %",e.exports["Gross profit_study"]=["Bruttó Profit"],e.exports["Gross profit to assets ratio_study"]="Gross profit to assets ratio",e.exports["Gross property/plant/equipment_study"]="Gross property/plant/equipment",e.exports.Impairments_study="Impairments",e.exports["Income Tax Credits_study"]="Income Tax Credits",e.exports["Income tax, current_study"]="Income tax, current",e.exports["Income tax, current - domestic_study"]="Income tax, current - domestic",e.exports["Income Tax, current - foreign_study"]="Income Tax, current - foreign",e.exports["Income tax, deferred_study"]="Income tax, deferred",e.exports["Income tax, deferred - domestic_study"]="Income tax, deferred - domestic",e.exports["Income tax, deferred - foreign_study"]="Income tax, deferred - foreign",e.exports["Income tax payable_study"]="Income tax payable",e.exports["Interest capitalized_study"]="Interest capitalized",e.exports["Interest coverage_study"]="Interest coverage",e.exports["Interest expense, net of interest capitalized_study"]="Interest expense, net of interest capitalized",e.exports["Interest expense on debt_study"]="Interest expense on debt",e.exports["Inventories - finished goods_study"]="Inventories - finished goods",e.exports["Inventories - progress payments & other_study"]="Inventories - progress payments & other",e.exports["Inventories - raw materials_study"]="Inventories - raw materials",e.exports["Inventories - work in progress_study"]="Inventories - work in progress",e.exports["Inventory to revenue ratio_study"]="Inventory to revenue ratio",e.exports["Inventory turnover_study"]="Inventory turnover",e.exports["Investing activities – other sources_study"]="Investing activities – other sources",e.exports["Investing activities – other uses_study"]="Investing activities – other uses",e.exports["Investments in unconsolidated subsidiaries_study"]="Investments in unconsolidated subsidiaries",e.exports["Issuance of long term debt_study"]="Issuance of long term debt",e.exports["Issuance/retirement of debt, net_study"]="Issuance/retirement of debt, net",e.exports["Issuance/retirement of long term debt_study"]="Issuance/retirement of long term debt",
e.exports["Issuance/retirement of other debt_study"]="Issuance/retirement of other debt",e.exports["Issuance/retirement of short term debt_study"]="Issuance/retirement of short term debt",e.exports["Issuance/retirement of stock, net_study"]="Issuance/retirement of stock, net",e.exports["KZ index_study"]="KZ index",e.exports["Legal claim expense_study"]="Legal claim expense",e.exports["Long term debt_study"]="Long term debt",e.exports["Long term debt excl. lease liabilities_study"]="Long term debt excl. lease liabilities",e.exports["Long term debt to total assets ratio_study"]="Long term debt to total assets ratio",e.exports["Long term debt to total equity ratio_study"]="Long term debt to total equity ratio",e.exports["Long term investments_study"]="Long term investments",e.exports["Market capitalization_study"]="Market capitalization",e.exports["Minority interest_study"]=["Kisebbségi Részesedés"],e.exports["Miscellaneous non-operating expense_study"]="Miscellaneous non-operating expense",e.exports["Net current asset value per share_study"]="Net current asset value per share",e.exports["Net debt_study"]="Net debt",e.exports["Net income_study"]=["Nettó Jövedelem"],e.exports["Net income before discontinued operations_study"]="Net income before discontinued operations",e.exports["Net income (cash flow)_study"]="Net income (cash flow)",e.exports["Net income per employee_study"]="Net income per employee",e.exports["Net intangible assets_study"]="Net intangible assets",e.exports["Net margin %_study"]="Net margin %",e.exports["Net property/plant/equipment_study"]="Net property/plant/equipment",e.exports["Non-cash items_study"]="Non-cash items",e.exports["Non-controlling/minority interest_study"]="Non-controlling/minority interest",e.exports["Non-operating income, excl. interest expenses_study"]="Non-operating income, excl. interest expenses",e.exports["Non-operating income, total_study"]="Non-operating income, total",e.exports["Non-operating interest income_study"]="Non-operating interest income",e.exports["Note receivable - long term_study"]="Note receivable - long term",e.exports["Notes payable_study"]="Notes payable",e.exports["Number of employees_study"]="Number of employees",e.exports["Number of shareholders_study"]="Number of shareholders",e.exports["Operating earnings yield %_study"]="Operating earnings yield %",e.exports["Operating expenses (excl. COGS)_study"]="Operating expenses (excl. COGS)",e.exports["Operating lease liabilities_study"]="Operating lease liabilities",e.exports["Operating margin %_study"]="Operating margin %",e.exports["Other COGS_study"]="Other COGS",e.exports["Other common equity_study"]="Other common equity",e.exports["Other current assets, total_study"]="Other current assets, total",e.exports["Other current liabilities_study"]="Other current liabilities",e.exports["Other cost of goods sold_study"]="Other cost of goods sold",e.exports["Other exceptional charges_study"]="Other exceptional charges",e.exports["Other financing cash flow items, total_study"]="Other financing cash flow items, total",
e.exports["Other intangibles, net_study"]="Other intangibles, net",e.exports["Other investing cash flow items, total_study"]="Other investing cash flow items, total",e.exports["Other investments_study"]="Other investments",e.exports["Other liabilities, total_study"]="Other liabilities, total",e.exports["Other long term assets, total_study"]="Other long term assets, total",e.exports["Other non-current liabilities, total_study"]="Other non-current liabilities, total",e.exports["Other operating expenses, total_study"]="Other operating expenses, total",e.exports["Other receivables_study"]="Other receivables",e.exports["Other short term debt_study"]="Other short term debt",e.exports["Paid in capital_study"]="Paid in capital",e.exports["PEG ratio_study"]="PEG ratio",e.exports["Piotroski F-score_study"]="Piotroski F-score",e.exports["Preferred dividends_study"]="Preferred dividends",e.exports["Preferred dividends paid_study"]="Preferred dividends paid",e.exports["Preferred stock, carrying value_study"]="Preferred stock, carrying value",e.exports["Prepaid expenses_study"]="Prepaid expenses",e.exports["Pretax equity in earnings_study"]="Pretax equity in earnings",e.exports["Pretax income_study"]="Pretax income",e.exports["Price earnings ratio forward_study"]="Price earnings ratio forward",e.exports["Price sales ratio forward_study"]="Price sales ratio forward",e.exports["Price to tangible book ratio_study"]="Price to tangible book ratio",e.exports["Provision for risks & charge_study"]="Provision for risks & charge",e.exports["Purchase/acquisition of business_study"]="Purchase/acquisition of business",e.exports["Purchase of investments_study"]="Purchase of investments",e.exports["Purchase/sale of business, net_study"]="Purchase/sale of business, net",e.exports["Purchase/sale of investments, net_study"]="Purchase/sale of investments, net",e.exports["Quality ratio_study"]="Quality ratio",e.exports["Quick ratio_study"]="Quick ratio",e.exports["Reduction of long term debt_study"]="Reduction of long term debt",e.exports["Repurchase of common & preferred stock_study"]="Repurchase of common & preferred stock",e.exports["Research & development_study"]="Research & development",e.exports["Research & development to revenue ratio_study"]="Research & development to revenue ratio",e.exports["Restructuring charge_study"]="Restructuring charge",e.exports["Retained earnings_study"]="Retained earnings",e.exports["Return on assets %_study"]="Return on assets %",e.exports["Return on equity %_study"]="Return on equity %",e.exports["Return on equity adjusted to book value %_study"]="Return on equity adjusted to book value %",e.exports["Return on invested capital %_study"]="Return on invested capital %",e.exports["Return on tangible assets %_study"]="Return on tangible assets %",e.exports["Return on tangible equity %_study"]="Return on tangible equity %",e.exports["Revenue estimates_study"]="Revenue estimates",e.exports["Revenue one year growth_study"]="Revenue one year growth",e.exports["Revenue per employee_study"]="Revenue per employee",
e.exports["Sale/maturity of investments_study"]="Sale/maturity of investments",e.exports["Sale of common & preferred stock_study"]="Sale of common & preferred stock",e.exports["Sale of fixed assets & businesses_study"]="Sale of fixed assets & businesses",e.exports["Selling/general/admin expenses, other_study"]="Selling/general/admin expenses, other",e.exports["Selling/general/admin expenses, total_study"]="Selling/general/admin expenses, total",e.exports["Shareholders' equity_study"]="Shareholders' equity",e.exports["Shares buyback ratio %_study"]="Shares buyback ratio %",e.exports["Short term debt_study"]="Short term debt",e.exports["Short term debt excl. current portion of LT debt_study"]="Short term debt excl. current portion of LT debt",e.exports["Short term investments_study"]="Short term investments",e.exports["Sloan ratio %_study"]="Sloan ratio %",e.exports["Springate score_study"]="Springate score",e.exports["Sustainable growth rate_study"]="Sustainable growth rate",e.exports["Tangible book value per share_study"]="Tangible book value per share",e.exports["Tangible common equity ratio_study"]="Tangible common equity ratio",e.exports.Taxes_study="Taxes",e.exports["Tobin's Q (approximate)_study"]="Tobin's Q (approximate)",e.exports["Total assets_study"]=["Mérlegfőösszeg"],e.exports["Total cash dividends paid_study"]="Total cash dividends paid",e.exports["Total current assets_study"]=["Forgóeszközök Összesen"],e.exports["Total current liabilities_study"]=["Folyó Kötelezettségek Összesen"],e.exports["Total debt_study"]=["Teljes Hitelállomány"],e.exports["Total equity_study"]="Total equity",e.exports["Total inventory_study"]="Total inventory",e.exports["Total liabilities_study"]=["Összes Kötelezettség"],e.exports["Total liabilities & shareholders' equities_study"]="Total liabilities & shareholders' equities",e.exports["Total non-current assets_study"]="Total non-current assets",e.exports["Total non-current liabilities_study"]="Total non-current liabilities",e.exports["Total operating expenses_study"]=["Működési Költségek Összesen"],e.exports["Total receivables, net_study"]="Total receivables, net",e.exports["Total revenue_study"]=["Összes Bevétel"],e.exports["Treasury stock - common_study"]="Treasury stock - common",e.exports["Unrealized gain/loss_study"]="Unrealized gain/loss",e.exports["Unusual income/expense_study"]="Unusual income/expense",e.exports["Zmijewski score_study"]="Zmijewski score",e.exports["Valuation ratios_study"]="Valuation ratios",e.exports["Profitability ratios_study"]="Profitability ratios",e.exports["Liquidity ratios_study"]="Liquidity ratios",e.exports["Solvency ratios_study"]="Solvency ratios",e.exports["Key stats_study"]="Key stats",e.exports["Accumulation/Distribution_study"]=["Akkumuláció/Disztribúció"],e.exports["Accumulative Swing Index_study"]=["Akkumulatív Swing Index"],e.exports["Advance/Decline_study"]="Advance/Decline",e.exports["All Chart Patterns_study"]="All Chart Patterns",e.exports["Arnaud Legoux Moving Average_study"]=["Arnaud Legoux Mozgóátlag"],
e.exports.Aroon_study="Aroon",e.exports.ASI_study="ASI",e.exports["Average Directional Index_study"]=["Átlagos Irányított Index"],e.exports["Average True Range_study"]=["Átlagos Valós Tartomány"],e.exports["Awesome Oscillator_study"]=["Awesome Oszcillátor"],e.exports["Balance of Power_study"]=["Erőegyensúly"],e.exports["Bollinger Bands %B_study"]=["Bollinger Szalagok %B"],e.exports["Bollinger Bands Width_study"]=["Bollinger Szalag Szélesség"],e.exports["Bollinger Bands_study"]=["Bollinger Szalagok"],e.exports["Chaikin Money Flow_study"]=["Chaikin Pénzáramlás"],e.exports["Chaikin Oscillator_study"]=["Chaikin Oszcillátor"],e.exports["Chande Kroll Stop_study"]="Chande Kroll Stop",e.exports["Chande Momentum Oscillator_study"]=["Chande Momentum Oszcillátor"],e.exports["Chop Zone_study"]=["Oldalazó Zóna"],e.exports["Choppiness Index_study"]=["Szaggatottság Index"],e.exports["Commodity Channel Index_study"]=["Árucsatorna Index"],e.exports["Connors RSI_study"]="Connors RSI",e.exports["Coppock Curve_study"]=["Coppock Görbe"],e.exports["Correlation Coefficient_study"]=["Korrelációs Koefficiens"],e.exports.CRSI_study="CRSI",e.exports["Detrended Price Oscillator_study"]=["Trendmentes Ár Oszcillátor"],e.exports["Directional Movement_study"]=["Irányított Mozgás"],e.exports["Donchian Channels_study"]=["Donchian Csatornák"],e.exports["Double EMA_study"]=["Dupla EMA"],e.exports["Ease Of Movement_study"]=["Mozgás Könnyedség"],e.exports["Elder Force Index_study"]=["Nemes Erő Index"],e.exports["EMA Cross_study"]="EMA Cross",e.exports.Envelopes_study="Envelopes",e.exports["Fisher Transform_study"]=["Fisher Transzformáció"],e.exports["Fixed Range_study"]="Fixed Range",e.exports["Fixed Range Volume Profile_study"]="Fixed Range Volume Profile",e.exports["Guppy Multiple Moving Average_study"]="Guppy Multiple Moving Average",e.exports["Historical Volatility_study"]=["Histórikus Volatilitás"],e.exports["Hull Moving Average_study"]=["Hull Mozgóátlag"],e.exports["Keltner Channels_study"]=["Keltner Csatornák"],e.exports["Klinger Oscillator_study"]=["Klinger Oszcillátor"],e.exports["Know Sure Thing_study"]=["Biztosra Tudd Dolog"],e.exports["Least Squares Moving Average_study"]=["Least Squares Mozgóátlag"],e.exports["Linear Regression Curve_study"]="Linear Regression Curve",e.exports["MA Cross_study"]=["MA Kereszt"],e.exports["MA with EMA Cross_study"]="MA with EMA Cross",e.exports["MA/EMA Cross_study"]="MA/EMA Cross",e.exports.MACD_study="MACD",e.exports["Mass Index_study"]=["Tömeg Index"],e.exports["McGinley Dynamic_study"]=["McGinley Dinamika"],e.exports.Median_study=["Medián"],e.exports.Momentum_study="Momentum",e.exports["Money Flow_study"]=["Pénzáramlás"],e.exports["Moving Average Channel_study"]="Moving Average Channel",e.exports["Moving Average Exponential_study"]=["Mozgóátlag Exponenciális"],e.exports["Moving Average Weighted_study"]=["Mozgóátlag Súlyozott"],e.exports["Moving Average Simple_study"]="Moving Average Simple",e.exports["Net Volume_study"]=["Nettó Volumen"],e.exports["On Balance Volume_study"]=["Egyensúly Volumen"],
e.exports["Parabolic SAR_study"]=["Parabolikus SAR"],e.exports["Pivot Points Standard_study"]=["Pivotális Pontok Standard"],e.exports["Periodic Volume Profile_study"]="Periodic Volume Profile",e.exports["Price Channel_study"]="Price Channel",e.exports["Price Oscillator_study"]=["Price Oszcillátor"],e.exports["Price Volume Trend_study"]=["Árvolumen Trend"],e.exports["Rate Of Change_study"]=["Változás Üteme"],e.exports["Relative Strength Index_study"]=["Relatív Erő Index"],e.exports["Relative Vigor Index_study"]=["Relatív Életerő Index"],e.exports["Relative Volatility Index_study"]="Relative Volatility Index",e.exports["Relative Volume at Time_study"]="Relative Volume at Time",e.exports["Session Volume_study"]="Session Volume",e.exports["Session Volume HD_study"]=["Session Volume"],e.exports["Session Volume Profile_study"]="Session Volume Profile",e.exports["Session Volume Profile HD_study"]="Session Volume Profile HD",e.exports["SMI Ergodic Indicator/Oscillator_study"]="SMI Ergodic Indicator/Oscillator",e.exports["Smoothed Moving Average_study"]=["Simított Mozgóátlag"],e.exports["Stochastic Momentum Index_study"]="Stochastic Momentum Index",e.exports["Stochastic RSI_study"]=["Sztochasztikus RSI"],e.exports.Stochastic_study=["Sztochasztikus"],e.exports["Time Weighted Average Price_study"]="Time Weighted Average Price",e.exports["Triple EMA_study"]="Triple EMA",e.exports.TRIX_study="TRIX",e.exports["True Strength Indicator_study"]=["True Strength Indikátor"],e.exports["Ultimate Oscillator_study"]=["Végső Oszcillátor"],e.exports["Visible Range_study"]="Visible Range",e.exports["Visible Range Volume Profile_study"]="Visible Range Volume Profile",e.exports["Volume Oscillator_study"]=["Volumen Oszcillátor"],e.exports.Volume_study=["Volumen"],e.exports.Vol_study="Vol",e.exports["Vortex Indicator_study"]=["Vortex Indikátor"],e.exports.VWAP_study="VWAP",e.exports.VWMA_study="VWMA",e.exports["Williams %R_study"]="Williams %R",e.exports["Williams Alligator_study"]=["Williams Alligátor"],e.exports["Williams Fractal_study"]=["Williams Fraktál"],e.exports["Zig Zag_study"]=["Cikk Cakk"],e.exports["24-hour Volume_study"]="24-hour Volume",e.exports["Ease of Movement_study"]=["Mozgás Könnyedség"],e.exports["Elders Force Index_study"]="Elders Force Index",e.exports.Envelope_study=["Boríték"],e.exports.Gaps_study="Gaps",e.exports["Linear Regression Channel_study"]="Linear Regression Channel",e.exports["Moving Average Ribbon_study"]="Moving Average Ribbon",e.exports["Multi-Time Period Charts_study"]=["Több Időszakos Chartok"],e.exports["Open Interest_study"]="Open Interest",e.exports["Rob Booker - Intraday Pivot Points_study"]="Rob Booker - Intraday Pivot Points",e.exports["Rob Booker - Knoxville Divergence_study"]="Rob Booker - Knoxville Divergence",e.exports["Rob Booker - Missed Pivot Points_study"]="Rob Booker - Missed Pivot Points",e.exports["Rob Booker - Reversal_study"]="Rob Booker - Reversal",e.exports["Rob Booker - Ziv Ghost Pivots_study"]="Rob Booker - Ziv Ghost Pivots",e.exports.Supertrend_study="Supertrend",
e.exports["Technical Ratings_study"]="Technical Ratings",e.exports["True Strength Index_study"]="True Strength Index",e.exports["Up/Down Volume_study"]="Up/Down Volume",e.exports["Visible Average Price_study"]="Visible Average Price",e.exports["Williams Fractals_study"]="Williams Fractals",e.exports["Keltner Channels Strategy_study"]="Keltner Channels Strategy",e.exports["Rob Booker - ADX Breakout_study"]="Rob Booker - ADX Breakout",e.exports["Supertrend Strategy_study"]="Supertrend Strategy",e.exports["Technical Ratings Strategy_study"]="Technical Ratings Strategy",e.exports["Auto Anchored Volume Profile_study"]="Auto Anchored Volume Profile",e.exports["Auto Fib Extension_study"]="Auto Fib Extension",e.exports["Auto Fib Retracement_study"]="Auto Fib Retracement",e.exports["Auto Pitchfork_study"]="Auto Pitchfork",e.exports["Bearish Flag Chart Pattern_study"]="Bearish Flag Chart Pattern",e.exports["Bullish Flag Chart Pattern_study"]="Bullish Flag Chart Pattern",e.exports["Bearish Pennant Chart Pattern_study"]="Bearish Pennant Chart Pattern",e.exports["Bullish Pennant Chart Pattern_study"]="Bullish Pennant Chart Pattern",e.exports["Double Bottom Chart Pattern_study"]="Double Bottom Chart Pattern",e.exports["Double Top Chart Pattern_study"]="Double Top Chart Pattern",e.exports["Elliott Wave Chart Pattern_study"]="Elliott Wave Chart Pattern",e.exports["Falling Wedge Chart Pattern_study"]="Falling Wedge Chart Pattern",e.exports["Head And Shoulders Chart Pattern_study"]="Head And Shoulders Chart Pattern",e.exports["Inverse Head And Shoulders Chart Pattern_study"]="Inverse Head And Shoulders Chart Pattern",e.exports["Rectangle Chart Pattern_study"]="Rectangle Chart Pattern",e.exports["Rising Wedge Chart Pattern_study"]="Rising Wedge Chart Pattern",e.exports["Triangle Chart Pattern_study"]="Triangle Chart Pattern",e.exports["Triple Bottom Chart Pattern_study"]="Triple Bottom Chart Pattern",e.exports["Triple Top Chart Pattern_study"]="Triple Top Chart Pattern",e.exports["VWAP Auto Anchored_study"]="VWAP Auto Anchored",e.exports["*All Candlestick Patterns*_study"]="*All Candlestick Patterns*",e.exports["Abandoned Baby - Bearish_study"]="Abandoned Baby - Bearish",e.exports["Abandoned Baby - Bullish_study"]="Abandoned Baby - Bullish",e.exports["Dark Cloud Cover - Bearish_study"]="Dark Cloud Cover - Bearish",e.exports["Doji Star - Bearish_study"]="Doji Star - Bearish",e.exports["Doji Star - Bullish_study"]="Doji Star - Bullish",e.exports["Downside Tasuki Gap - Bearish_study"]="Downside Tasuki Gap - Bearish",e.exports["Dragonfly Doji - Bullish_study"]="Dragonfly Doji - Bullish",e.exports["Engulfing - Bearish_study"]="Engulfing - Bearish",e.exports["Engulfing - Bullish_study"]="Engulfing - Bullish",e.exports["Evening Doji Star - Bearish_study"]="Evening Doji Star - Bearish",e.exports["Evening Star - Bearish_study"]="Evening Star - Bearish",e.exports["Falling Three Methods - Bearish_study"]="Falling Three Methods - Bearish",e.exports["Falling Window - Bearish_study"]="Falling Window - Bearish",
e.exports["Gravestone Doji - Bearish_study"]="Gravestone Doji - Bearish",e.exports["Hammer - Bullish_study"]="Hammer - Bullish",e.exports["Hanging Man - Bearish_study"]="Hanging Man - Bearish",e.exports["Harami - Bearish_study"]="Harami - Bearish",e.exports["Harami - Bullish_study"]="Harami - Bullish",e.exports["Harami Cross - Bearish_study"]="Harami Cross - Bearish",e.exports["Harami Cross - Bullish_study"]="Harami Cross - Bullish",e.exports["Inverted Hammer - Bullish_study"]="Inverted Hammer - Bullish",e.exports["Kicking - Bearish_study"]="Kicking - Bearish",e.exports["Kicking - Bullish_study"]="Kicking - Bullish",e.exports["Long Lower Shadow - Bullish_study"]="Long Lower Shadow - Bullish",e.exports["Long Upper Shadow - Bearish_study"]="Long Upper Shadow - Bearish",e.exports["Marubozu Black - Bearish_study"]="Marubozu Black - Bearish",e.exports["Marubozu White - Bullish_study"]="Marubozu White - Bullish",e.exports["Morning Doji Star - Bullish_study"]="Morning Doji Star - Bullish",e.exports["Morning Star - Bullish_study"]="Morning Star - Bullish",e.exports["On Neck - Bearish_study"]="On Neck - Bearish",e.exports["Piercing - Bullish_study"]="Piercing - Bullish",e.exports["Rising Three Methods - Bullish_study"]="Rising Three Methods - Bullish",e.exports["Rising Window - Bullish_study"]="Rising Window - Bullish",e.exports["Shooting Star - Bearish_study"]="Shooting Star - Bearish",e.exports["Three Black Crows - Bearish_study"]="Three Black Crows - Bearish",e.exports["Three White Soldiers - Bullish_study"]="Three White Soldiers - Bullish",e.exports["Tri-Star - Bearish_study"]="Tri-Star - Bearish",e.exports["Tri-Star - Bullish_study"]="Tri-Star - Bullish",e.exports["Tweezer Top - Bearish_study"]="Tweezer Top - Bearish",e.exports["Upside Tasuki Gap - Bullish_study"]="Upside Tasuki Gap - Bullish",e.exports.SuperTrend_study="SuperTrend",e.exports["Average Price_study"]="Average Price",e.exports["Typical Price_study"]="Typical Price",e.exports["Median Price_study"]="Median Price",e.exports["Money Flow Index_study"]="Money Flow Index",e.exports["Moving Average Double_study"]="Moving Average Double",e.exports["Moving Average Triple_study"]="Moving Average Triple",e.exports["Moving Average Adaptive_study"]="Moving Average Adaptive",e.exports["Moving Average Hamming_study"]="Moving Average Hamming",e.exports["Moving Average Modified_study"]="Moving Average Modified",e.exports["Moving Average Multiple_study"]="Moving Average Multiple",e.exports["Linear Regression Slope_study"]="Linear Regression Slope",e.exports["Standard Error_study"]="Standard Error",e.exports["Standard Error Bands_study"]="Standard Error Bands",e.exports["Correlation - Log_study"]="Correlation - Log",e.exports["Standard Deviation_study"]="Standard Deviation",e.exports["Chaikin Volatility_study"]="Chaikin Volatility",e.exports["Volatility Close-to-Close_study"]="Volatility Close-to-Close",e.exports["Volatility Zero Trend Close-to-Close_study"]="Volatility Zero Trend Close-to-Close",e.exports["Volatility O-H-L-C_study"]="Volatility O-H-L-C",
e.exports["Volatility Index_study"]="Volatility Index",e.exports["Trend Strength Index_study"]="Trend Strength Index",e.exports["Majority Rule_study"]="Majority Rule",e.exports["Advance Decline Line_study"]=["Növekedés Zuhanás Vonal"],e.exports["Advance Decline Ratio_study"]=["Növekedés Zuhanás Arány"],e.exports["Advance/Decline Ratio (Bars)_study"]=["Növekedés/Zuhanás Arány (Bárok)"],e.exports["BarUpDn Strategy_study"]="BarUpDn Strategy",e.exports["Bollinger Bands Strategy directed_study"]="Bollinger Bands Strategy directed",e.exports["Bollinger Bands Strategy_study"]=["Bollinger Szalagok Stratégia"],e.exports.ChannelBreakOutStrategy_study="ChannelBreakOutStrategy",e.exports.Compare_study=["Összehasonlít"],e.exports["Conditional Expressions_study"]=["Feltételes Kifejezések"],e.exports.ConnorsRSI_study="ConnorsRSI",e.exports["Consecutive Up/Down Strategy_study"]="Consecutive Up/Down Strategy",e.exports["Cumulative Volume Index_study"]=["Kumulatív Volumenindex"],e.exports["Divergence Indicator_study"]=["Divergencia Indikátor"],e.exports["Greedy Strategy_study"]=["Greedy Stratégia"],e.exports["InSide Bar Strategy_study"]=["InSide Bar Stratégia"],e.exports["Keltner Channel Strategy_study"]=["Keltner Channel Stratégia"],e.exports["Linear Regression_study"]=["Lineáris Regresszió"],e.exports["MACD Strategy_study"]=["MACD Stratégia"],e.exports["Momentum Strategy_study"]=["Momentum Stratégia"],e.exports["Moon Phases_study"]=["Holdfázisok"],e.exports["Moving Average Convergence/Divergence_study"]=["Mozgóátlag Konvergencia/Divergencia"],e.exports["MovingAvg Cross_study"]="MovingAvg Cross",e.exports["MovingAvg2Line Cross_study"]="MovingAvg2Line Cross",e.exports["OutSide Bar Strategy_study"]=["OutSide Bar Stratégia"],e.exports.Overlay_study="Overlay",e.exports["Parabolic SAR Strategy_study"]=["Parabolic SAR Stratégia"],e.exports["Pivot Extension Strategy_study"]=["Pivot Extension Stratégia"],e.exports["Pivot Points High Low_study"]=["Pivotális Pontok Max Min"],e.exports["Pivot Reversal Strategy_study"]=["Pivot Reversal Stratégia"],e.exports["Price Channel Strategy_study"]=["Price Channel Stratégia"],e.exports["RSI Strategy_study"]=["RSI Stratégia"],e.exports["SMI Ergodic Indicator_study"]=["SMI Ergodikus Indikátor"],e.exports["SMI Ergodic Oscillator_study"]=["SMI Ergodikus Oszcillátor"],e.exports["Stochastic Slow Strategy_study"]=["Stochastic Slow Stratégia"],e.exports["Volatility Stop_study"]=["Volatilitás Stop"],e.exports["Volty Expan Close Strategy_study"]=["Volty Expan Close Stratégia"],e.exports["Woodies CCI_study"]="Woodies CCI",e.exports["Anchored Volume Profile_study"]="Anchored Volume Profile"},59791:e=>{e.exports="Anchored Volume Profile"},40434:e=>{e.exports="Fixed Range Volume Profile"},32819:e=>{e.exports="Vol"},66051:e=>{e.exports=["Kicsi"]},86054:e=>{e.exports=["Perc"]},20936:e=>{e.exports="Text"},98478:e=>{e.exports="Couldn't copy"},34004:e=>{e.exports="Couldn't cut"},96260:e=>{e.exports="Couldn't paste"},94370:e=>{e.exports=["Countdown To Bar Close"]},15168:e=>{e.exports="Colombo"},36018:e=>{
e.exports=["Oszlopok"]},19372:e=>{e.exports=["Komment"]},20229:e=>{e.exports=["Összehasonlítás vagy Szimbólum Hozzáadása"]},46689:e=>{e.exports=["Inputok Megerősítése"]},43432:e=>{e.exports="Copenhagen"},35216:e=>{e.exports=["Másolás"]},87898:e=>{e.exports=["Chart Elrendezés Másolása"]},28851:e=>{e.exports="Copy price"},94099:e=>{e.exports="Cairo"},64149:e=>{e.exports=["Kiemelő"]},63528:e=>{e.exports=["Gyertyák"]},46837:e=>{e.exports="Caracas"},53705:e=>{e.exports="Casablanca"},49329:e=>{e.exports=["Változás"]},28089:e=>{e.exports=["Szimbólum Változtatás"]},99374:e=>{e.exports=["Intervallum Váltás"]},35696:e=>{e.exports="Change interval. Press number or comma"},71705:e=>{e.exports="Change symbol. Start typing symbol name"},86715:e=>{e.exports="Chart #{index}"},14412:e=>{e.exports=["Chart Tulajdonságok"]},26619:e=>{e.exports="Chart by TradingView"},69916:e=>{e.exports="Chart for {symbol}, {interval}"},12011:e=>{e.exports="Chart image copied to clipboard {emoji}"},79393:e=>{e.exports="Chart image embed code copied to clipboard {emoji}"},59884:e=>{e.exports=["Chatham-szigetek"]},28244:e=>{e.exports="Chicago"},49648:e=>{e.exports="Chongqing"},90068:e=>{e.exports=["Kör"]},32234:e=>{e.exports=["Klikkelj a pont megadásához"]},52977:e=>{e.exports=["Klón"]},31691:e=>{e.exports=["Zárás"]},52302:e=>{e.exports="Create limit order"},29908:e=>{e.exports=["Kereszt"]},60997:e=>{e.exports="Cross Line"},81520:e=>{e.exports=["Devizák"]},98486:e=>{e.exports="Current interval and above"},73106:e=>{e.exports="Current interval and below"},85964:e=>{e.exports="Current interval only"},17206:e=>{e.exports=["Görbe"]},95176:e=>{e.exports=["Ciklus"]},87761:e=>{e.exports=["Ciklikus Vonalak"]},27891:e=>{e.exports=["Rejtjel Minta"]},56996:e=>{e.exports="A layout with that name already exists"},30192:e=>{e.exports="A layout with that name already exists. Do you want to overwrite it?"},32852:e=>{e.exports=["ABCD Minta"]},88010:e=>{e.exports="Amsterdam"},37422:e=>{e.exports=["Kereskedési Felállás Elemzése"]},99873:e=>{e.exports="Anchorage"},66828:e=>{e.exports=["Horgony Megjegyzés"]},94782:e=>{e.exports=["Horgony Szöveg"]},61704:e=>{e.exports="Anchored VWAP"},45743:e=>{e.exports=["Szimbólum Hozzáadása"]},64615:e=>{e.exports="Add alert on {title}"},7005:e=>{e.exports="Add alert on {title} at {price}"},3612:e=>{e.exports="Add financial metric for {instrumentName}"},92206:e=>{e.exports="Add indicator/strategy on {studyTitle}"},34810:e=>{e.exports=["Add Text Note for {symbol}"]},75669:e=>{e.exports="Add this financial metric to entire layout"},64288:e=>{e.exports="Add this indicator to entire layout"},77920:e=>{e.exports="Add this strategy to entire layout"},34059:e=>{e.exports="Add this symbol to entire layout"},17365:e=>{e.exports="Adelaide"},9408:e=>{e.exports=["Mindig Láthatatlan"]},71997:e=>{e.exports=["Mindig Látható"]},97305:e=>{e.exports=["Összes Indikátor és Rajzeszköz"]},59192:e=>{e.exports="All intervals"},14452:e=>{e.exports=["Almati"]},5716:e=>{e.exports=["Elliot Hullám Alkalmazása"]},19263:e=>{e.exports=["Fő Elliot Hullám Alkalmazása"]},
15818:e=>{e.exports=["Kis Elliot Hullám Alkalmazása"]},50352:e=>{e.exports=["Közbülső Elliot Hullám Alkalmazása"]},66631:e=>{e.exports=["Manuális Döntési Pont Alkalmazása"]},15682:e=>{e.exports=["Manuális Kockázat/Nyereség Alkalmazása"]},15644:e=>{e.exports=["WPT Le Hullám Alkalmazása"]},5897:e=>{e.exports=["WPT Fel Hullám Alkalmazása"]},13345:e=>{e.exports=["Alapértelmezett Beállítás"]},95910:e=>{e.exports="Apply these indicators to entire layout"},42762:e=>{e.exports=["Ápr"]},45104:e=>{e.exports=["Ív"]},42097:e=>{e.exports=["Terület"]},96237:e=>{e.exports=["Nyíl"]},48732:e=>{e.exports="Arrow Down"},82473:e=>{e.exports="Arrow Marker"},8738:e=>{e.exports=["Nyíl Lefelé"]},35062:e=>{e.exports=["Nyíl Balra"]},92163:e=>{e.exports=["Nyíl Jobbra"]},33196:e=>{e.exports=["Nyíl Felfelé"]},10650:e=>{e.exports="Arrow Up"},59340:e=>{e.exports=["Asgábád"]},13468:e=>{e.exports="At close"},21983:e=>{e.exports=["Athén"]},86951:e=>{e.exports="Auto"},50834:e=>{e.exports=["Auto (Fits Data To Screen)"]},38465:e=>{e.exports="Aug"},8975:e=>{e.exports="Average close price label"},87899:e=>{e.exports="Average close price line"},22554:e=>{e.exports="Avg"},54173:e=>{e.exports=["Bogotá"]},53260:e=>{e.exports="Bahrain"},40664:e=>{e.exports=["Ballon"]},32376:e=>{e.exports="Bangkok"},19149:e=>{e.exports="Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"},38660:e=>{e.exports="Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"},16812:e=>{e.exports=["Bárok"]},98838:e=>{e.exports=["Bár Minta"]},17712:e=>{e.exports=["Alapvonal"]},54861:e=>{e.exports="Belgrade"},26825:e=>{e.exports="Berlin"},30251:e=>{e.exports=["Ecset"]},90204:e=>{e.exports="Brussels"},5262:e=>{e.exports="Bratislava"},59901:e=>{e.exports=["Előterjesztés"]},26354:e=>{e.exports=["Előrehozás"]},11741:e=>{e.exports="Brisbane"},37728:e=>{e.exports="Bucharest"},87143:e=>{e.exports="Budapest"},82446:e=>{e.exports="Buenos Aires"},82128:e=>{e.exports=["TradingView Által"]},75190:e=>{e.exports=["Ugrás dátumhoz"]},38342:e=>{e.exports="Go to {lineToolName}"},75139:e=>{e.exports="Got it"},81180:e=>{e.exports=["Gann Doboz"]},68102:e=>{e.exports=["Gann Legyező"]},66321:e=>{e.exports=["Gann Négyszög"]},87107:e=>{e.exports="Gann Square Fixed"},7914:e=>{e.exports=["Ghost Hírfolyam"]},18367:e=>{e.exports=["Nagy Szuperciklus"]},97065:e=>{e.exports=["Biztos, hogy törölni akarod ezt a tanulmánysablont: {name}?"]},59368:e=>{e.exports=["Dupla Görbe"]},35273:e=>{e.exports="Double-click any edge to reset layout grid"},5828:e=>{e.exports="Double-click to finish Path"},63898:e=>{e.exports="Double-click to finish Polyline"},42660:e=>{e.exports=["Hullám 1 vagy A Le"]},44788:e=>{e.exports=["Hullám 2 vagy B Le"]},71263:e=>{e.exports=["Hullám 3 Le"]},70573:e=>{e.exports=["Hullám 4 Le"]},59560:e=>{e.exports=["Hullám 5 Le"]},70437:e=>{e.exports=["Hullám C Le"]},93345:e=>{e.exports="Data Provided by"},76912:e=>{e.exports=["Dátum"]},60222:e=>{e.exports=["Időintervallum"]},79859:e=>{e.exports=["Dátum és Árfolyamtartomány"]},92203:e=>{e.exports="Dec"},
69479:e=>{e.exports=["Fokozat"]},57701:e=>{e.exports="Denver"},24477:e=>{e.exports="Dhaka"},73720:e=>{e.exports="Diamond"},3556:e=>{e.exports="Disjoint Channel"},62764:e=>{e.exports="Displacement"},22903:e=>{e.exports=["Rajzok Eszköztár"]},8338:e=>{e.exports="Draw Horizontal Line at"},22429:e=>{e.exports=["Dubaj"]},9497:e=>{e.exports="Dublin"},85223:e=>{e.exports="Emoji"},24435:e=>{e.exports=["Add meg az új chart elrendezés nevét"]},91215:e=>{e.exports=["Elliot Korrekciós Hullám (ABC)"]},80983:e=>{e.exports=["Elliott Dupla Kombinációs Hullám (WXY)"]},74118:e=>{e.exports=["Elliott Impulzushullám (12345)"]},95840:e=>{e.exports=["Elliott Háromszög Hullám (ABCDE)"]},66637:e=>{e.exports=["Elliott Tripla Kombinációs Hullám (WXYXZ)"]},69418:e=>{e.exports=["Ellipszis"]},2578:e=>{e.exports="Extended Line"},77295:e=>{e.exports=["Tőzsde"]},2899:e=>{e.exports=["Existing Pane Above"]},53387:e=>{e.exports=["Existing Pane Below"]},36972:e=>{e.exports=["Előrejelzés"]},17994:e=>{e.exports="Failed to save library"},87375:e=>{e.exports="Failed to save script"},35050:e=>{e.exports="Feb"},82719:e=>{e.exports=["Fib Csatorna"]},64192:e=>{e.exports=["Fib Körök"]},63835:e=>{e.exports="Fib Retracement"},18072:e=>{e.exports=["Fib Speed Ellenállás Ívek"]},20877:e=>{e.exports=["Fib Speed Ellenállás Fan"]},76783:e=>{e.exports=["Fib Spirál"]},89037:e=>{e.exports=["Fib Időzóna"]},72489:e=>{e.exports=["Fib Ék"]},21524:e=>{e.exports="Flag"},55678:e=>{e.exports=["Zászló Jel"]},29230:e=>{e.exports=["Lapos Felső/Alsó"]},92754:e=>{e.exports=["Flippelt"]},42015:e=>{e.exports=["Érvénytelen törtrész."]},47542:e=>{e.exports="Fundamental studies are no longer available on charts"},16245:e=>{e.exports=["Kalkutta"]},3155:e=>{e.exports="Kathmandu"},92901:e=>{e.exports="Kagi"},2693:e=>{e.exports="Karachi"},72374:e=>{e.exports="Kuwait"},34911:e=>{e.exports="HLC area"},87338:e=>{e.exports="Ho Chi Minh"},61582:e=>{e.exports=["Áttetsző Gyertyák"]},32918:e=>{e.exports="Hong Kong"},61351:e=>{e.exports="Honolulu"},60049:e=>{e.exports=["Vízszintes Vonal"]},76604:e=>{e.exports=["Vízszintes Sugár"]},42616:e=>{e.exports="Head and Shoulders"},40530:e=>{e.exports="Heikin Ashi"},99820:e=>{e.exports="Helsinki"},31971:e=>{e.exports=["Elrejt"]},33911:e=>{e.exports="Hide all"},95551:e=>{e.exports="Hide all drawings"},44312:e=>{e.exports="Hide all drawings and indicators"},67927:e=>{e.exports="Hide all drawings, indicators, positions & orders"},86306:e=>{e.exports="Hide all indicators"},70803:e=>{e.exports="Hide all positions & orders"},13277:e=>{e.exports="Hide drawings"},8251:e=>{e.exports=["Események Elrejtése a Chartról"]},44177:e=>{e.exports="Hide indicators"},2441:e=>{e.exports=["Jelölések Elrejtése a Bárokon"]},90540:e=>{e.exports="Hide positions & orders"},30777:e=>{e.exports=["Max"]},31994:e=>{e.exports="High-low"},60259:e=>{e.exports="High and low price labels"},21803:e=>{e.exports="High and low price lines"},31895:e=>{e.exports="Highlighter"},69085:e=>{e.exports='Histogram is too large, please increase "Row Size" input.'},8122:e=>{
e.exports="Histogram is too large, please reduce 'Row Size' input."},23450:e=>{e.exports="Image"},93213:e=>{e.exports=["Az időköz nem alkalmazható"]},71778:e=>{e.exports=["Közbülső"]},14177:e=>{e.exports=["Érvénytelen Szimbólum"]},32619:e=>{e.exports="Invalid symbol"},53239:e=>{e.exports=["Invert Scale"]},20062:e=>{e.exports="Indexed to 100"},81584:e=>{e.exports="Indicators value labels"},31485:e=>{e.exports="Indicators name labels"},21585:e=>{e.exports="Indicators, Metrics and Strategies. Press slash"},27677:e=>{e.exports="Info Line"},98767:e=>{e.exports=["Indikátor Beillesztés"]},9114:e=>{e.exports=["Belső"]},12354:e=>{e.exports=["Belső Villa"]},26579:e=>{e.exports=["Ikon"]},37885:e=>{e.exports=["Isztambul"]},87469:e=>{e.exports="Johannesburg"},52707:e=>{e.exports="Jakarta"},95425:e=>{e.exports="Jan"},42890:e=>{e.exports="Jerusalem"},6215:e=>{e.exports=["Júl"]},15224:e=>{e.exports=["Jún"]},36253:e=>{e.exports="Juneau"},15241:e=>{e.exports="On the left"},29404:e=>{e.exports="On the right"},850:e=>{e.exports=["Hoppá!"]},675:e=>{e.exports="Object Tree"},73546:e=>{e.exports=["Okt"]},39280:e=>{e.exports=["Nyitó"]},25595:e=>{e.exports=["Eredeti"]},82906:e=>{e.exports="Oslo"},8136:e=>{e.exports=["Min"]},14702:e=>{e.exports="Load layout. Press period"},42284:e=>{e.exports=["Zárás"]},1441:e=>{e.exports=["Zárás/Feloldás"]},82232:e=>{e.exports="Lock vertical cursor line by time"},18219:e=>{e.exports=["Lock Price To Bar Ratio"]},12285:e=>{e.exports="Logarithmic"},50286:e=>{e.exports="London"},44604:e=>{e.exports=["Long Pozíció"]},87604:e=>{e.exports="Los Angeles"},18528:e=>{e.exports="Label Down"},13046:e=>{e.exports="Label Up"},94420:e=>{e.exports=["Címkék"]},89155:e=>{e.exports="Lagos"},37611:e=>{e.exports="Last day change"},25846:e=>{e.exports="Lima"},1277:e=>{e.exports=["Vonal"]},38397:e=>{e.exports=["Jelölésekkel"]},63492:e=>{e.exports=["Vonaltörés"]},83182:e=>{e.exports="Lines"},78104:e=>{e.exports="Link to the chart image copied to clipboard {emoji}"},50091:e=>{e.exports="Lisbon"},64352:e=>{e.exports="Luxembourg"},11156:e=>{e.exports="MTPredictor"},67861:e=>{e.exports="Move the point to position the anchor then tap to place"},45828:e=>{e.exports="Move to"},44302:e=>{e.exports="Move scale to left"},94338:e=>{e.exports="Move scale to right"},66276:e=>{e.exports=["Módosított Schiff"]},18559:e=>{e.exports=["Módosított Schiff Villa"]},18665:e=>{e.exports=["Moszkva"]},58038:e=>{e.exports="Madrid"},34190:e=>{e.exports="Malta"},90271:e=>{e.exports="Manila"},51369:e=>{e.exports=["Már"]},85095:e=>{e.exports=["Mexikóváros"]},75633:e=>{e.exports="Merge all scales into one"},95093:e=>{e.exports="Mixed"},10931:e=>{e.exports=["Mikro"]},58397:e=>{e.exports=["Évezred"]},85884:e=>{e.exports=["Menüett"]},9632:e=>{e.exports="Minuscule"},63158:e=>{e.exports=["Tükrözött"]},42769:e=>{e.exports="Muscat"},43088:e=>{e.exports="N/A"},95222:e=>{e.exports="No data here"},3485:e=>{e.exports=["No Scale (Fullscreen)"]},8886:e=>{e.exports="No sync"},16971:e=>{e.exports="No volume data"},75549:e=>{e.exports=["Megjegyzés"]},71230:e=>{e.exports="Nov"},
99203:e=>{e.exports="Norfolk Island"},79023:e=>{e.exports="Nairobi"},91203:e=>{e.exports="New York"},24143:e=>{e.exports=["Új-Zéland"]},40887:e=>{e.exports="New pane above"},96712:e=>{e.exports="New pane below"},33566:e=>{e.exports="Nicosia"},56670:e=>{e.exports=["Valami hiba történt"]},64968:e=>{e.exports="Something went wrong. Please try again later."},10520:e=>{e.exports=["Új Chart Elrendezés Mentése"]},9908:e=>{e.exports=["Mentés Másként"]},68553:e=>{e.exports="San Salvador"},65412:e=>{e.exports="Santiago"},13538:e=>{e.exports=["São Paulo"]},37207:e=>{e.exports=["Csak az Árskála Chart"]},51464:e=>{e.exports="Schiff"},98114:e=>{e.exports=["Schiff Villa"]},1535:e=>{e.exports="Script may be not updated if you leave the page."},89517:e=>{e.exports=["Beállítások"]},43247:e=>{e.exports=["A második törtrész érvénytelen."]},19796:e=>{e.exports=["Visszaküldés"]},23221:e=>{e.exports=["Hátrébb Küldés"]},5961:e=>{e.exports=["Szöul"]},57902:e=>{e.exports=["Szep"]},25866:e=>{e.exports=["Munkamenet"]},59827:e=>{e.exports=["Munkamenet Szünetek"]},69240:e=>{e.exports=["Sanghaj"]},37819:e=>{e.exports=["Short Pozíció"]},81428:e=>{e.exports=["Mutat"]},98116:e=>{e.exports="Show all drawings"},39046:e=>{e.exports="Show all drawings and indicators"},38293:e=>{e.exports="Show all drawings, indicators, positions & orders"},49982:e=>{e.exports="Show all indicators"},48284:e=>{e.exports=["Show All Ideas"]},62632:e=>{e.exports="Show all positions & orders"},24620:e=>{e.exports="Show continuous contract switch"},84813:e=>{e.exports="Show contract expiration"},66263:e=>{e.exports=["Osztalékok Mutatása"]},46771:e=>{e.exports=["Nyereség Mutatása"]},87933:e=>{e.exports=["Show Ideas of Followed Users"]},72973:e=>{e.exports="Show latest news and Minds"},58669:e=>{e.exports=["Show My Ideas Only"]},30816:e=>{e.exports=["Felosztások Mutatása"]},68161:e=>{e.exports="Signpost"},56683:e=>{e.exports=["Szingapúr"]},69502:e=>{e.exports=["Szinuszvonal"]},44904:e=>{e.exports=["Négyzet"]},70213:e=>{e.exports="Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."},32733:e=>{e.exports=["Stílus"]},65323:e=>{e.exports="Stack on the left"},14113:e=>{e.exports="Stack on the right"},29787:e=>{e.exports="Start using keyboard navigation mode. Press {shortcut}"},93161:e=>{e.exports=["Rajzmódban Marad"]},79511:e=>{e.exports=["Lépcső"]},84573:e=>{e.exports="Sticker"},48767:e=>{e.exports="Stockholm"},29662:e=>{e.exports=["Szubmikro"]},9753:e=>{e.exports=["Szubévezred"]},71722:e=>{e.exports=["Szubminüett"]},91889:e=>{e.exports=["Szuperciklus"]},33820:e=>{e.exports=["Szuperévezred"]},11020:e=>{e.exports="Sydney"},89659:e=>{e.exports="Symbol Error"},90932:e=>{e.exports=["Symbol Name Label"]},65986:e=>{e.exports=["Szimbólum Infó"]},52054:e=>{e.exports=["Symbol Last Value Label"]},33606:e=>{e.exports="Sync globally"},18008:e=>{e.exports=["Sync To All Charts"]},99969:e=>{e.exports=["Pont & Ábra"]},53047:e=>{e.exports=["Sokszögvonal"]},34402:e=>{e.exports="Path"},70394:e=>{e.exports=["Párhuzamos Csatorna"]},95995:e=>{e.exports=["Párizs"]},29682:e=>{
e.exports="Paste"},51102:e=>{e.exports="Percent"},35590:e=>{e.exports="Perth"},19093:e=>{e.exports="Phoenix"},22293:e=>{e.exports="Pitchfan"},43852:e=>{e.exports=["Villa"]},37680:e=>{e.exports="Pin to new left scale"},43707:e=>{e.exports="Pin to new right scale"},91130:e=>{e.exports="Pin to left scale"},61201:e=>{e.exports="Pin to left scale (hidden)"},764:e=>{e.exports="Pin to right scale"},20207:e=>{e.exports="Pin to right scale (hidden)"},66156:e=>{e.exports="Pin to scale (now left)"},54727:e=>{e.exports="Pin to scale (now no scale)"},76598:e=>{e.exports="Pin to scale (now right)"},39065:e=>{e.exports="Pin to scale (now {label})"},97324:e=>{e.exports="Pin to scale {label}"},56948:e=>{e.exports="Pin to scale {label} (hidden)"},32156:e=>{e.exports="Pinned to left scale"},8128:e=>{e.exports="Pinned to left scale (hidden)"},3822:e=>{e.exports="Pinned to right scale"},44538:e=>{e.exports="Pinned to right scale (hidden)"},65810:e=>{e.exports="Pinned to scale {label}"},14125:e=>{e.exports="Pinned to scale {label} (hidden)"},97378:e=>{e.exports="Plus button"},46669:e=>{e.exports="Please give us a clipboard writing permission in your browser or press {keystroke}"},46298:e=>{e.exports="Prague"},35963:e=>{e.exports="Press and hold {key} while zooming to maintain the chart position"},95921:e=>{e.exports=["Árcímke"]},28625:e=>{e.exports="Price Note"},2032:e=>{e.exports=["Ártartomány"]},32061:e=>{e.exports=["Érvénytelen árformátum."]},91492:e=>{e.exports=["Árvonal"]},48404:e=>{e.exports=["Elsődleges"]},87086:e=>{e.exports=["Vetület"]},10160:e=>{e.exports="Published on {customer}, {date}"},19056:e=>{e.exports="Qatar"},4868:e=>{e.exports="Quick search. Press {shortcut}"},9998:e=>{e.exports=["Elforgatott Téglalap"]},74214:e=>{e.exports="Rome"},50470:e=>{e.exports=["Sugár"]},90357:e=>{e.exports=["Tartomány"]},26833:e=>{e.exports="Reykjavik"},328:e=>{e.exports=["Téglalap"]},41615:e=>{e.exports=["Újra"]},35001:e=>{e.exports=["Regresszió Trend"]},34596:e=>{e.exports=["Eltávolítás"]},1434:e=>{e.exports="Remove drawings"},13951:e=>{e.exports=["Indikátorok Eltávolítása"]},4142:e=>{e.exports=["Chart Elrendezés Átnevezése"]},20801:e=>{e.exports="Renko"},34301:e=>{e.exports="Reset chart view"},18001:e=>{e.exports="Reset points"},17258:e=>{e.exports=["Reset Price Scale"]},25333:e=>{e.exports=["Reset Time Scale"]},52588:e=>{e.exports="Riyadh"},5871:e=>{e.exports="Riga"},33603:e=>{e.exports=["Figyelmeztetés"]},48474:e=>{e.exports=["Varsó"]},74327:e=>{e.exports="Toggle auto scale"},84112:e=>{e.exports="Toggle log scale"},20466:e=>{e.exports="Tokelau"},94284:e=>{e.exports=["Tokió"]},83836:e=>{e.exports="Toronto"},38788:e=>{e.exports=["Tajpej"]},39108:e=>{e.exports="Tallinn"},37229:e=>{e.exports=["Szöveg"]},16267:e=>{e.exports=["Teherán"]},19611:e=>{e.exports=["Sablon"]},29198:e=>{e.exports="The data vendor doesn't provide volume data for this symbol."},8162:e=>{e.exports="The publication preview could not be loaded. Please disable your browser extensions and try again."},65943:e=>{
e.exports=["Ezt az indikátort nem lehet alkalmazni egy másik indikátorra"]},81214:e=>{e.exports="This script contains an error. Please contact its author."},74986:e=>{e.exports="This script is invite-only. To request access, please contact its author."},58018:e=>{e.exports=["The symbol available only on {linkStart}TradingView{linkEnd}."]},98538:e=>{e.exports=["Három Hajtás Minta"]},30973:e=>{e.exports="Ticks"},31976:e=>{e.exports=["Idő"]},64375:e=>{e.exports=["Időzóna"]},95005:e=>{e.exports=["Ciklusidők"]},87085:e=>{e.exports=["Kereskedés"]},48890:e=>{e.exports="TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"},94770:e=>{e.exports=["Trendszög"]},23104:e=>{e.exports=["Trendvonal"]},15501:e=>{e.exports=["Trendalapú Fib Kiterjesztés"]},31196:e=>{e.exports=["Trendalapú Fib Idő"]},29245:e=>{e.exports=["Háromszög"]},83356:e=>{e.exports="Triangle Down"},12390:e=>{e.exports=["Háromszög Minta"]},28340:e=>{e.exports="Triangle Up"},93855:e=>{e.exports="Tunis"},50406:e=>{e.exports="UTC"},81320:e=>{e.exports=["Visszavonás"]},25933:e=>{e.exports="Units"},28523:e=>{e.exports=["Ismeretlen hiba"]},15101:e=>{e.exports=["Feloldás"]},34150:e=>{e.exports=["Hullám 4 Fel"]},83927:e=>{e.exports=["Hullám 5 Fel"]},58976:e=>{e.exports=["Hullám 1 vagy A Fel"]},11661:e=>{e.exports=["Hullám 2 vagy B Fel"]},53958:e=>{e.exports=["Hullám 3 Fel"]},66560:e=>{e.exports=["Hullám C Fel"]},18426:e=>{e.exports="Volume Profile Fixed Range"},61022:e=>{e.exports="Volume Profile indicator available only on our upgraded plans."},82772:e=>{e.exports="Volume data is not provided in BIST MIXED data plan."},78560:e=>{e.exports="Volume footprint"},15771:e=>{e.exports="Vancouver"},56211:e=>{e.exports=["Függőleges Vonal"]},32166:e=>{e.exports="Vienna"},75354:e=>{e.exports="Vilnius"},21852:e=>{e.exports=["Láthatóság"]},27557:e=>{e.exports="Visibility on intervals"},89960:e=>{e.exports=["Az Egér Föléhúzásakor Látható"]},22198:e=>{e.exports=["Vizuális Elrendezés"]},7050:e=>{e.exports="X Cross"},66527:e=>{e.exports=["XABCD Minta"]},17126:e=>{e.exports="You cannot see this pivot timeframe on this resolution"},69293:e=>{e.exports="Yangon"},84301:e=>{e.exports=["Zürich"]},76020:e=>{e.exports="change Elliott degree"},83935:e=>{e.exports="change no overlapping labels"},39402:e=>{e.exports="change average close price label visibility"},98866:e=>{e.exports="change average close price line visibility"},5100:e=>{e.exports="change bid and ask labels visibility"},32311:e=>{e.exports="change bid and ask lines visibility"},22641:e=>{e.exports="change currency"},30501:e=>{e.exports="change chart layout to {title}"},7017:e=>{e.exports="change continuous contract switch visibility"},58108:e=>{e.exports="change countdown to bar close visibility"},7151:e=>{e.exports="change date range"},84944:e=>{e.exports="change dividends visibility"},79574:e=>{e.exports="change events visibility on chart"},88217:e=>{e.exports="change earnings visibility"},28288:e=>{
e.exports="change futures contract expiration visibility"},66805:e=>{e.exports="change high and low price labels visibility"},92556:e=>{e.exports="change high and low price lines visibility"},87027:e=>{e.exports="change indicators name labels visibility"},14922:e=>{e.exports="change indicators value labels visibility"},19839:e=>{e.exports="change latest news and Minds visibility"},23783:e=>{e.exports="change linking group"},87510:e=>{e.exports="change pane height"},50190:e=>{e.exports="change plus button visibility"},49889:e=>{e.exports="change pre/post market price label visibility"},16750:e=>{e.exports="change pre/post market price line visibility"},59883:e=>{e.exports="change previous close price line visibility"},67761:e=>{e.exports="change price line visibility"},69510:e=>{e.exports="change price to bar ratio"},32303:e=>{e.exports=["Felbontás Módosítása"]},526:e=>{e.exports=["Szimbólum módosítása"]},9402:e=>{e.exports="change symbol labels visibility"},53150:e=>{e.exports="change symbol last value visibility"},12707:e=>{e.exports="change symbol previous close value visibility"},65303:e=>{e.exports="change session"},15403:e=>{e.exports="change session breaks visibility"},53438:e=>{e.exports="change series style"},74488:e=>{e.exports="change splits visibility"},20505:e=>{e.exports="change timezone"},39028:e=>{e.exports="change unit"},21511:e=>{e.exports="change visibility"},16698:e=>{e.exports="change visibility at current interval"},78422:e=>{e.exports="change visibility at current interval and above"},49529:e=>{e.exports="change visibility at current interval and below"},66927:e=>{e.exports="change visibility at all intervals"},74428:e=>{e.exports="change {title} style"},72032:e=>{e.exports="change {pointIndex} point"},65911:e=>{e.exports=["TradingView chartok"]},5179:e=>{e.exports="clone line tools"},3195:e=>{e.exports="create line tools group"},92659:e=>{e.exports="create line tools group from selection"},81791:e=>{e.exports="create {tool}"},63649:e=>{e.exports="cut sources"},78755:e=>{e.exports="cut {title}"},99113:e=>{e.exports="add line tool {lineTool} to group {name}"},40242:e=>{e.exports="add line tool(s) to group {group}"},22856:e=>{e.exports="add this financial metric to entire layout"},82388:e=>{e.exports="add this indicator to entire layout"},94292:e=>{e.exports="add this strategy to entire layout"},27982:e=>{e.exports="add this symbol to entire layout"},66568:e=>{e.exports="apply chart theme"},64034:e=>{e.exports="apply all chart properties"},49037:e=>{e.exports="apply drawing template"},96996:e=>{e.exports="apply factory defaults to selected sources"},44547:e=>{e.exports="apply indicators to entire layout"},26065:e=>{e.exports="apply study template {template}"},58570:e=>{e.exports="apply toolbars theme"},27195:e=>{e.exports="bring group {title} forward"},78246:e=>{e.exports="bring {title} to front"},56763:e=>{e.exports="bring {title} forward"},5607:e=>{e.exports="by TradingView"},90621:e=>{e.exports="date range lock"},12962:e=>{e.exports="erase level line"},63391:e=>{
e.exports="exclude line tools from group {group}"},59942:e=>{e.exports="flip bars pattern"},70301:e=>{e.exports="hide {title}"},54781:e=>{e.exports=["Minden Rajzeszköz Elrejtése"]},44974:e=>{e.exports=["Jelölések Elrejtése a Bárokon"]},28916:e=>{e.exports="interval lock"},94245:e=>{e.exports=["Invert Scale"]},90743:e=>{e.exports="insert {title}"},53146:e=>{e.exports="insert {title} after {targetTitle}"},74055:e=>{e.exports="insert {title} after {target}"},11231:e=>{e.exports="insert {title} before {target}"},67176:e=>{e.exports="insert {title} before {targetTitle}"},54597:e=>{e.exports="load default drawing template"},30295:e=>{e.exports=["töltés..."]},50193:e=>{e.exports="lock {title}"},4963:e=>{e.exports="lock group {group}"},68163:e=>{e.exports="lock objects"},47107:e=>{e.exports="move"},11303:e=>{e.exports="move {title} to new left scale"},45544:e=>{e.exports="move {title} to new right scale"},81898:e=>{e.exports="move all scales to left"},22863:e=>{e.exports="move all scales to right"},45356:e=>{e.exports="move drawing(s)"},15086:e=>{e.exports="move left"},61711:e=>{e.exports="move right"},4184:e=>{e.exports="move scale"},74642:e=>{e.exports="make {title} no scale (Full screen)"},45223:e=>{e.exports="make group {group} invisible"},87927:e=>{e.exports="make group {group} visible"},62153:e=>{e.exports="merge down"},70746:e=>{e.exports="merge to pane"},66143:e=>{e.exports="merge up"},81870:e=>{e.exports="mirror bars pattern"},16542:e=>{e.exports="n/a"},47222:e=>{e.exports="scale price"},99042:e=>{e.exports=["Csak az Árskála Chart"]},35962:e=>{e.exports="scale time"},68193:e=>{e.exports="scroll"},70009:e=>{e.exports="scroll time"},69485:e=>{e.exports="set price scale selection strategy to {title}"},16259:e=>{e.exports="send {title} backward"},66781:e=>{e.exports="send {title} to back"},4998:e=>{e.exports="send group {title} backward"},64704:e=>{e.exports="share line tools globally"},77554:e=>{e.exports="share line tools in layout"},13622:e=>{e.exports="show all ideas"},26267:e=>{e.exports="show ideas of followed users"},40061:e=>{e.exports="show my ideas only"},52010:e=>{e.exports="stay in drawing mode"},98784:e=>{e.exports="stop syncing drawing"},57011:e=>{e.exports="stop syncing line tool(s)"},92831:e=>{e.exports="symbol lock"},60635:e=>{e.exports="sync time"},99769:e=>{e.exports="powered by"},68111:e=>{e.exports=["támogatta a TradingView"]},96916:e=>{e.exports="paste drawing"},80611:e=>{e.exports="paste indicator"},41601:e=>{e.exports="paste {title}"},84018:e=>{e.exports="pin to left scale"},22615:e=>{e.exports="pin to right scale"},56015:e=>{e.exports="pin to scale {label}"},33348:e=>{e.exports="rearrange panes"},15516:e=>{e.exports="remove all studies"},80171:e=>{e.exports="remove all studies and drawing tools"},59211:e=>{e.exports="remove deselected empty line tools"},44656:e=>{e.exports="remove drawings"},70653:e=>{e.exports="remove drawings group"},66414:e=>{e.exports="remove line data sources"},47637:e=>{e.exports="remove pane"},39859:e=>{e.exports="remove {title}"},78811:e=>{
e.exports="removing line tools group {name}"},16338:e=>{e.exports="rename group {group} to {newName}"},30910:e=>{e.exports="reset layout sizes"},21948:e=>{e.exports="reset scales"},55064:e=>{e.exports=["Reset Time Scale"]},13034:e=>{e.exports="resize layout"},9608:e=>{e.exports="restore defaults"},30107:e=>{e.exports="restore study defaults"},63060:e=>{e.exports="toggle auto scale"},74724:e=>{e.exports="toggle collapsed pane state"},98860:e=>{e.exports="toggle indexed to 100 scale"},21203:e=>{e.exports="toggle lock scale"},60166:e=>{e.exports="toggle log scale"},68642:e=>{e.exports=["Toggle Percentage Scale"]},33714:e=>{e.exports="toggle regular scale"},47122:e=>{e.exports="track time"},28068:e=>{e.exports="turn line tools sharing off"},66824:e=>{e.exports="unlock objects"},51114:e=>{e.exports="unlock group {group}"},92421:e=>{e.exports="unlock {title}"},20057:e=>{e.exports="unmerge to new bottom pane"},52540:e=>{e.exports="unmerge up"},86949:e=>{e.exports="unmerge down"},47228:e=>{e.exports="{chartStyle} chart type isn't currently available for tick-based intervals."},33355:e=>{e.exports=["{count} oszlop"]},87826:e=>{e.exports="{p_start}Tick-based intervals are not supported for this symbol. You will be automatically switched to D interval.{p_end}"},88841:e=>{e.exports=["{symbol} TradingView pénzügyek"]},38641:e=>{e.exports="{userName} published on {customer}, {date}"},59833:e=>{e.exports="zoom"},19813:e=>{e.exports="zoom in"},9645:e=>{e.exports="zoom out"},30572:e=>{e.exports=["nap"]},52254:e=>{e.exports=["óra"]},99062:e=>{e.exports=["months"]},69143:e=>{e.exports=["perc"]},71787:e=>{e.exports=["seconds"]},82797:e=>{e.exports=["ranges"]},47966:e=>{e.exports=["weeks"]},99136:e=>{e.exports=["ticks"]},18562:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]=["APPLE INC"],e.exports["#AUDCAD-symbol-description"]=["ausztrál dollár/kanadai dollár"],e.exports["#AUDCHF-symbol-description"]=["ausztrál dollár / svájci frank"],e.exports["#AUDJPY-symbol-description"]=["ausztrál dollár / japán jen"],e.exports["#AUDNZD-symbol-description"]=["ausztrál dollár / új-zélandi dollár"],e.exports["#AUDRUB-symbol-description"]=["ausztrál dollár / orosz rúbel"],e.exports["#AUDUSD-symbol-description"]=["ausztrál dollár / amerikai dollár"],e.exports["#BRLJPY-symbol-description"]=["brazil dollár / japán jen"],e.exports["#BTCCAD-symbol-description"]=["bitcoin / kanadai dollár"],e.exports["#BTCCNY-symbol-description"]=["bitcoin / kínai jüan"],e.exports["#BTCEUR-symbol-description"]=["bitcoin / euró"],e.exports["#BTCKRW-symbol-description"]=["bitcoin / dél-koreai won"],e.exports["#BTCRUR-symbol-description"]=["bitcoin / rubel"],e.exports["#BTCUSD-symbol-description"]=["bitcoin / amerikai dollár"],e.exports["#BVSP-symbol-description"]="Brazil Bovespa Index",e.exports["#CADJPY-symbol-description"]=["kanadai dollár / japán jen"],e.exports["#CHFJPY-symbol-description"]=["svájci frank/japán jen"],e.exports["#COPPER-symbol-description"]=["Réz"],e.exports["#ES1-symbol-description"]=["#ES1-symbol-description"],
e.exports["#ESP35-symbol-description"]=["#ESP35-symbol-description"],e.exports["#EUBUND-symbol-description"]=["eurókötvények"],e.exports["#EURAUD-symbol-description"]=["euró / ausztrál dollár"],e.exports["#EURBRL-symbol-description"]=["euró / brazil reál"],e.exports["#EURCAD-symbol-description"]=["euró / kanadai dollár"],e.exports["#EURCHF-symbol-description"]=["Euró Fx/svájci frank"],e.exports["#EURGBP-symbol-description"]=["Euró Fx/brit font"],e.exports["#EURJPY-symbol-description"]=["Euró Fx/japán jen"],e.exports["#EURNZD-symbol-description"]=["euró / új-zélandi dollár"],e.exports["#EURRUB-symbol-description"]=["EURÓ / OROSZ RUBEL"],e.exports["#EURRUB_TOM-symbol-description"]=["EUR/RUB TOM"],e.exports["#EURSEK-symbol-description"]=["#EURSEK-symbol-description"],e.exports["#EURTRY-symbol-description"]=["Euró Fx/új török líra"],e.exports["#EURUSD-symbol-description"]=["euró / amerikai dollár"],e.exports["#EUSTX50-symbol-description"]=["Euro Stoxx 50 index európai jegyzett részvények"],e.exports["#FRA40-symbol-description"]=["#FRA40-symbol-description"],e.exports["#GB10-symbol-description"]=["brit államkötvények 10 éves"],e.exports["#GBPAUD-symbol-description"]=["brit font / ausztrál dollár"],e.exports["#GBPCAD-symbol-description"]=["brit font / kanadai dollár"],e.exports["#GBPCHF-symbol-description"]=["brit font/svájci frank"],e.exports["#GBPEUR-symbol-description"]=["FONT STERLING / EURÓ"],e.exports["#GBPJPY-symbol-description"]=["brit font//japán jen"],e.exports["#GBPNZD-symbol-description"]=["brit font / új-zélandi dollár"],e.exports["#GBPRUB-symbol-description"]=["font sterling / orosz rubel"],e.exports["#GBPUSD-symbol-description"]=["brit font / amerikai dollár"],e.exports["#GER30-symbol-description"]=["DAX index német jegyzett részvények"],e.exports["#GOOGL-symbol-description"]=["GOOGLE INC"],e.exports["#ITA40-symbol-description"]=["FTSE MIB index olasz jegyzett részvények"],e.exports["#JPN225-symbol-description"]=["#JPN225-symbol-description"],e.exports["#JPYKRW-symbol-description"]=["JEN / WON"],e.exports["#JPYRUB-symbol-description"]=["JEN / OROSZ RUBEL"],e.exports["#KA1-symbol-description"]=["#KA1-symbol-description"],e.exports["#KG1-symbol-description"]=["#KG1-symbol-description"],e.exports["#KT1-symbol-description"]=["#KT1-symbol-description"],e.exports["#LKOH-symbol-description"]="LUKOIL",e.exports["#LTCBTC-symbol-description"]=["litecoin / bitcoin"],e.exports["#MGNT-symbol-description"]=["MAGNIT"],e.exports["#MICEX-symbol-description"]=["MICEX INDEX"],e.exports["#MNOD_ME.EQRP-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#MSFT-symbol-description"]=["MICROSOFT CORP"],e.exports["#NAS100-symbol-description"]=["NASDAQ 100 amerikai jegyzett részvények"],e.exports["#NGAS-symbol-description"]=["Földgáz (Henry Hub)"],e.exports["#NKY-symbol-description"]="Nikkei 225 Index",e.exports["#NZDJPY-symbol-description"]=["új-zélandi dollár / japán jen"],e.exports["#NZDUSD-symbol-description"]=["új-zélandi dollár / amerikai dollár"],
e.exports["#RB1-symbol-description"]=["#KT1-symbol-description"],e.exports["#RTS-symbol-description"]=["Orosz RTS Index"],e.exports["#SBER-symbol-description"]="SBERBANK",e.exports["#SPX500-symbol-description"]=["S&P 500 index amerikai jegyzett részvények"],e.exports["#TWTR-symbol-description"]=["TWITTER INC"],e.exports["#UK100-symbol-description"]=["#UK100-symbol-description"],e.exports["#USDBRL-symbol-description"]=["amerikai dollár / brazil reál"],e.exports["#USDCAD-symbol-description"]=["amerikai dollár / kanadai dollár"],e.exports["#USDCHF-symbol-description"]=["amerikai dollár / svájci frank"],e.exports["#USDCNY-symbol-description"]=["amerikai dollár / jüan renminbi"],e.exports["#USDDKK-symbol-description"]=["amerikai follár / dán korona"],e.exports["#USDHKD-symbol-description"]=["amerikai dollár / hong kongi dollár"],e.exports["#USDIDR-symbol-description"]=["amerikai dollár / rúpia"],e.exports["#USDINR-symbol-description"]=["amerikai dollár / indiai rúpia"],e.exports["#USDJPY-symbol-description"]=["amerikai dollár / japán jen"],e.exports["#USDKRW-symbol-description"]=["amerikai dollár / dél-koreai won"],e.exports["#USDMXN-symbol-description"]=["amerikai dollár / mexikói peso"],e.exports["#USDPHP-symbol-description"]=["amerikai dollár /fülöp-szigeteki peso"],e.exports["#USDRUB-symbol-description"]=["amerikai dollár / orosz rúbel"],e.exports["#USDRUB_TOM-symbol-description"]=["amerikai dollár / orosz rúbel TOM"],e.exports["#USDSEK-symbol-description"]=["amerikai dollár / svéd korona"],e.exports["#USDSGD-symbol-description"]=["amerikai dollár / szingapúri dollár"],e.exports["#USDTRY-symbol-description"]=["amerikai dollár / török líra"],e.exports["#VTBR-symbol-description"]="VTB",e.exports["#XAGUSD-symbol-description"]=["ezüst / amerikai dollár"],e.exports["#XAUUSD-symbol-description"]=["arany / amerikai dollár"],e.exports["#XPDUSD-symbol-description"]=["#XPDUSD-symbol-description"],e.exports["#XPTUSD-symbol-description"]=["platina / amerikai dollár"],e.exports["#ZS1-symbol-description"]=["#ZS1-symbol-description"],e.exports["#ZW1-symbol-description"]=["#ZW1-symbol-description"],e.exports["#BTCGBP-symbol-description"]=["bitcoin / brit font"],e.exports["#MICEXINDEXCF-symbol-description"]=["MICEX Index"],e.exports["#BTCAUD-symbol-description"]=["bitcoin / ausztrál dollár"],e.exports["#BTCJPY-symbol-description"]=["bitcoin / japán jen"],e.exports["#BTCBRL-symbol-description"]=["bitcoin / brazil reál"],e.exports["#PT10-symbol-description"]=["Portugál Államkötvények 10 éves"],e.exports["#TXSX-symbol-description"]="TSX 60 Index",e.exports["#VIXC-symbol-description"]=["TSX 60 VIX"],e.exports["#USDPLN-symbol-description"]=["amerikai dollár / lengyel zloty"],e.exports["#EURPLN-symbol-description"]=["EUR/PLN"],e.exports["#BTCPLN-symbol-description"]=["bitcoin / lengyel zloty"],e.exports["#CAC40-symbol-description"]=["CAC 40"],e.exports["#XBTCAD-symbol-description"]=["bitcoin / kanadai dollár"],e.exports["#ITI2!-symbol-description"]="Iron Ore Futures",e.exports["#ITIF2018-symbol-description"]=["Vasérc Határidősők"],
e.exports["#ITIF2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIF2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIG2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIG2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIG2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIH2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIH2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIH2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIJ2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIJ2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIJ2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIK2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIK2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIK2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIM2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIM2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIM2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIM2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIN2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIN2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIN2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIN2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIQ2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIQ2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIQ2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIQ2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIU2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIU2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIU2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIU2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIV2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIV2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIV2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIV2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIX2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIX2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIX2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIX2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIZ2017-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIZ2018-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIZ2019-symbol-description"]=["Vasérc Határidősők"],e.exports["#ITIZ2020-symbol-description"]=["Vasérc Határidősők"],e.exports["#AMEX:GXF-symbol-description"]=["Globális x FTSE Északi Régió ETF"],e.exports["#ASX:XAF-symbol-description"]=["S&P/ASX Összes Ausztrál 50"],e.exports["#ASX:XAT-symbol-description"]=["S&P/ASX Összes Ausztrál 200"],e.exports["#BIST:XU100-symbol-description"]=["BIST 100"],e.exports["#GPW:WIG20-symbol-description"]=["WIG20"],
e.exports["#INDEX:JKSE-symbol-description"]=["Dzsakarta Kompozit Index"],e.exports["#INDEX:KLSE-symbol-description"]=["Bursa Malajzia KLCI Index"],e.exports["#INDEX:NZD-symbol-description"]="NZX 50 Index",e.exports["#INDEX:STI-symbol-description"]="STI Index",e.exports["#INDEX:XLY0-symbol-description"]=["Sanghaj Kompozit Index"],e.exports["#MOEX:MICEXINDEXCF-symbol-description"]=["MOEX Oroszország Index"],e.exports["#NYMEX:KT1!-symbol-description"]=["Kávé Határidősők"],e.exports["#OANDA:NATGASUSD-symbol-description"]=["Földgáz"],e.exports["#OANDA:USDPLN-symbol-description"]=["amerikai dollár / lengyel zloty"],e.exports["#TSX:TX60-symbol-description"]="S&P/TSX 60 Index",e.exports["#TSX:VBU-symbol-description"]=["Vanguard US Aggregált BND INDX ETF(CAD-HEG)UN"],e.exports["#TSX:VIXC-symbol-description"]=["S&P/TSX 60 VIX"],e.exports["#TVC:CAC40-symbol-description"]=["CAC 40"],e.exports["#TVC:ES10-symbol-description"]=["Spanyol Államkötvények 10 éves"],e.exports["#TVC:EUBUND-symbol-description"]=["Eurókötvény"],e.exports["#TVC:GB02-symbol-description"]=["UK Államkötvények 2 éves"],e.exports["#TVC:GB10-symbol-description"]=["UK Államkötvények 10 éves"],e.exports["#TVC:GOLD-symbol-description"]=["ARANY (US$/OZ)"],e.exports["#TVC:ID03-symbol-description"]=["Indonéz Államkötvények 3 éves"],e.exports["#TVC:ID10-symbol-description"]=["Indonéz Államkötvények 10 éves"],e.exports["#TVC:PALLADIUM-symbol-description"]=["PALLÁDIUM (US$/OZ)"],e.exports["#TVC:PT10-symbol-description"]=["Portugál Államkötvények 10 éves"],e.exports["#TVC:SILVER-symbol-description"]=["EZÜST (US$/OZ)"],e.exports["#TSX:TSX-symbol-description"]=["S&P/TSX Kompozit"],e.exports["#OANDA:CH20CHF-symbol-description"]="Swiss 20 Index",e.exports["#TVC:SHCOMP-symbol-description"]=["Shanghaj Kompozit"],e.exports["#NZX:ALLC-symbol-description"]=["S&P/NZX ÖSSZES Index ( Tőkeindex)"],e.exports["#AMEX:SHYG-symbol-description"]="Shares 0-5 YEAR High Yield Corporate Bond ETF",e.exports["#TVC:AU10-symbol-description"]="Australia Government Bonds 10 YR",e.exports["#TVC:CN10-symbol-description"]="China Government Bonds 10 YR",e.exports["#TVC:KR10-symbol-description"]="Korea Government Bonds 10 YR",e.exports["#NYMEX:RB1!-symbol-description"]="RBOB Gasoline Futures",e.exports["#NYMEX:HO1!-symbol-description"]="NY Harbor ULSD Futures",e.exports["#NYMEX:AEZ1!-symbol-description"]="NY Ethanol Futures",e.exports["#OANDA:XCUUSD-symbol-description"]="CFDs on Copper (US$ / lb)",e.exports["#COMEX:ZA1!-symbol-description"]="Zinc Futures",e.exports["#CBOT:ZW1!-symbol-description"]="Wheat Futures",e.exports["#NYMEX:KA1!-symbol-description"]="Sugar #11 Futures",e.exports["#CBOT:QBC1!-symbol-description"]="Corn Futures",e.exports["#CME:E61!-symbol-description"]="Euro Futures",e.exports["#CME:B61!-symbol-description"]="British Pound Futures",e.exports["#CME:QJY1!-symbol-description"]="Japanese Yen Futures",e.exports["#CME:A61!-symbol-description"]=["Ausztrál dollár határidős"],e.exports["#CME:D61!-symbol-description"]=["Kanadai dollár határidős"],
e.exports["#CME:SP1!-symbol-description"]="S&P 500 Futures",e.exports["#CME_MINI:NQ1!-symbol-description"]="NASDAQ 100 E-mini Futures",e.exports["#CBOT_MINI:YM1!-symbol-description"]="E-mini Dow Jones ($5) Futures",e.exports["#CME:NY1!-symbol-description"]="NIKKEI 225 Futures",e.exports["#EUREX:DY1!-symbol-description"]="DAX Index",e.exports["#CME:IF1!-symbol-description"]="IBOVESPA Index Futures-US$",e.exports["#CBOT:TY1!-symbol-description"]="10 Year T-Note Futures",e.exports["#CBOT:FV1!-symbol-description"]="5 Year T-Note Futures",e.exports["#CBOT:ZE1!-symbol-description"]="Treasury Notes - 3 Year Futures",e.exports["#CBOT:TU1!-symbol-description"]="2 Year T-Note Futures",e.exports["#CBOT:FF1!-symbol-description"]="30-Day FED Funds Interest Rate Futures",e.exports["#CBOT:US1!-symbol-description"]="T-Bond Futures",e.exports["#TVC:EXY-symbol-description"]="Euro Currency Index",e.exports["#TVC:JXY-symbol-description"]="Japanese Yen Currency Index",e.exports["#TVC:BXY-symbol-description"]="British Pound Currency Index",e.exports["#TVC:AXY-symbol-description"]=["Ausztrál Dollár Devizaindex"],e.exports["#TVC:CXY-symbol-description"]=["Kanadai Dollár Devizaindex"],e.exports["#FRED:GDP-symbol-description"]="Gross Domestic Product, 1 Decimal",e.exports["#FRED:UNRATE-symbol-description"]="Civilian Unemployment Rate",e.exports["#FRED:POP-symbol-description"]="Total Population: All Ages Including Armed Forces Overseas",e.exports["#ETHUSD-symbol-description"]=["ethereum / amerikai dollár"],e.exports["#BMFBOVESPA:IBOV-symbol-description"]="IBovespa Index",e.exports["#BMFBOVESPA:IBRA-symbol-description"]="IBrasil Index",e.exports["#BMFBOVESPA:IBXL-symbol-description"]="IBRX 50 Index",e.exports["#COMEX:HG1!-symbol-description"]="Copper Futures",e.exports["#INDEX:HSCE-symbol-description"]="Hang Seng China Enterprises Index",e.exports["#NYMEX:CL1!-symbol-description"]="Light Crude Oil Futures",e.exports["#OTC:IHRMF-symbol-description"]="Ishares MSCI Japan SHS",e.exports["#TVC:DAX-symbol-description"]="DAX Index",e.exports["#TVC:DE10-symbol-description"]="German Government Bonds 10 YR",e.exports["#TVC:DJI-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#TVC:DXY-symbol-description"]=["Amerikai Dollár Devizaindex"],e.exports["#TVC:FR10-symbol-description"]="France Government Bonds 10 YR",e.exports["#TVC:HSI-symbol-description"]="Hang Seng Index",e.exports["#TVC:IBEX35-symbol-description"]="IBEX 35 Index",e.exports["#FX:AUS200-symbol-description"]="S&P/ASX Index",e.exports["#AMEX:SHY-symbol-description"]="Ishares 1-3 Year Treasury Bond ETF",e.exports["#ASX:XJO-symbol-description"]="S&P/ASX 200 Index",e.exports["#BSE:SENSEX-symbol-description"]="S&P BSE Sensex Index",e.exports["#INDEX:MIB-symbol-description"]="MIB Index",e.exports["#INDEX:MOY0-symbol-description"]="Euro Stoxx 50 Index",e.exports["#MOEX:RTSI-symbol-description"]="RTS Index",e.exports["#NSE:NIFTY-symbol-description"]="Nifty 50 Index",e.exports["#NYMEX:NG1!-symbol-description"]="Natural Gas Futures",
e.exports["#NYMEX:ZC1!-symbol-description"]="Corn Futures",e.exports["#TVC:IN10-symbol-description"]="India Government Bonds 10 YR",e.exports["#TVC:IT10-symbol-description"]="Italy Government Bonds 10 YR",e.exports["#TVC:JP10-symbol-description"]="Japan Government Bonds 10 YR",e.exports["#TVC:NDX-symbol-description"]=["NASDAQ 100 Index"],e.exports["#TVC:NI225-symbol-description"]="Nikkei 225 Index",e.exports["#TVC:SPX-symbol-description"]="S&P 500 Index",e.exports["#TVC:SX5E-symbol-description"]="Euro Stoxx 50 Index",e.exports["#TVC:TR10-symbol-description"]="Turkey Government Bonds 10 YR",e.exports["#TVC:UKOIL-symbol-description"]="CFDs on Brent Crude Oil",e.exports["#TVC:UKX-symbol-description"]="UK 100 Index",e.exports["#TVC:US02-symbol-description"]="US Government Bonds 2 YR",e.exports["#TVC:US05-symbol-description"]="US Government Bonds 5 YR",e.exports["#TVC:US10-symbol-description"]="US Government Bonds 10 YR",e.exports["#TVC:USOIL-symbol-description"]="CFDs on WTI Crude Oil",e.exports["#NYMEX:ITI1!-symbol-description"]="Iron Ore Futures",e.exports["#NASDAQ:SHY-symbol-description"]="Ishares 1-3 Year Treasury Bond ETF",e.exports["#AMEX:ALD-symbol-description"]="WisdomTree Asia Local Debt ETF",e.exports["#NASDAQ:AMD-symbol-description"]="Advanced Micro Devices Inc",e.exports["#NYSE:BABA-symbol-description"]=["ALIBABA GROUP HLDG LTD"],e.exports["#ICEEUR:CB-symbol-description"]=["Nyersolaj Brent"],e.exports["#ICEEUR:CB1!-symbol-description"]=["Brent Nyersolaj"],e.exports["#ICEUSA:CC-symbol-description"]=["Kakaó"],e.exports["#NYMEX:CL-symbol-description"]=["Nyersolaj WTI"],e.exports["#ICEUSA:CT-symbol-description"]=["Gyapot #2"],e.exports["#NASDAQ:CTRV-symbol-description"]=["CONTRAVIR PHARMACEUTICALS INC"],e.exports["#CME:DL-symbol-description"]=["Tej III. osztály"],e.exports["#NYSE:F-symbol-description"]="FORD MTR CO DEL",e.exports["#MOEX:GAZP-symbol-description"]="GAZPROM",e.exports["#COMEX:GC-symbol-description"]=["Arany"],e.exports["#CME:GF-symbol-description"]=["Feeder Szarvasmarha"],e.exports["#CME:HE-symbol-description"]=["Lean Sertés"],e.exports["#NASDAQ:IEF-symbol-description"]="Ishares 7-10 Year Treasury Bond ETF",e.exports["#NASDAQ:IEI-symbol-description"]="Ishares 3-7 Year Treasury Bond ETF",e.exports["#NYMEX:KA1-symbol-description"]="Sugar #11 Futures",e.exports["#ICEUSA:KC-symbol-description"]=["Kávé"],e.exports["#NYMEX:KG1-symbol-description"]="Cotton Futures",e.exports["#FWB:KT1-symbol-description"]="Key Tronic Corр.",e.exports["#CME:LE-symbol-description"]=["Élő Szarvasmarha"],e.exports["#ICEEUR:LO-symbol-description"]=["ICE Fűtőolaj"],e.exports["#CME:LS-symbol-description"]=["Fűrészárú"],e.exports["#MOEX:MGNT-symbol-description"]="MAGNIT",e.exports["#LSIN:MNOD-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#NYMEX:NG-symbol-description"]=["Földgáz"],e.exports["#ICEUSA:OJ-symbol-description"]=["Narancslé"],e.exports["#NYMEX:PA-symbol-description"]=["Palládium"],e.exports["#NYSE:PBR-symbol-description"]="PETROLEO BRASILEIRO SA PETROBR",
e.exports["#NYMEX:PL-symbol-description"]=["Platina"],e.exports["#COMEX_MINI:QC-symbol-description"]=["E-Mini Réz"],e.exports["#NYMEX:RB-symbol-description"]=["Benzin RBOB"],e.exports["#NYMEX:RB1-symbol-description"]=["#KT1-symbol-description"],e.exports["#MOEX:SBER-symbol-description"]="SBERBANK",e.exports["#AMEX:SCHO-symbol-description"]="Schwab Short-Term U.S. Treasury ETF",e.exports["#COMEX:SI-symbol-description"]=["Ezüst"],e.exports["#NASDAQ:TLT-symbol-description"]="Ishares 20+ Year Treasury Bond ETF",e.exports["#TVC:VIX-symbol-description"]="Volatility S&P 500 Index",e.exports["#MOEX:VTBR-symbol-description"]="VTB",e.exports["#COMEX:ZA-symbol-description"]=["Cink"],e.exports["#CBOT:ZC-symbol-description"]=["Kukorica"],e.exports["#CBOT:ZK-symbol-description"]=["Etanol Határidős"],e.exports["#CBOT:ZL-symbol-description"]=["Szójabab Olaj"],e.exports["#CBOT:ZO-symbol-description"]=["Zab"],e.exports["#CBOT:ZR-symbol-description"]=["Hántolatlan Rizs"],e.exports["#CBOT:ZS-symbol-description"]=["Szójabab"],e.exports["#CBOT:ZS1-symbol-description"]="Soybean Futures",e.exports["#CBOT:ZW-symbol-description"]=["Búza"],e.exports["#CBOT:ZW1-symbol-description"]="Wheat Futures - ECBT",e.exports["#NASDAQ:ITI-symbol-description"]=["Iteris Inc."],e.exports["#NYMEX:ITI2!-symbol-description"]="Iron Ore Futures",e.exports["#CADUSD-symbol-description"]=["kanadai dollár / amerikai dollár"],e.exports["#CHFUSD-symbol-description"]=["svájci frank / amerikai dollár"],e.exports["#GPW:ACG-symbol-description"]="Acautogaz",e.exports["#JPYUSD-symbol-description"]=["japán jen / amerikai dollár"],e.exports["#USDAUD-symbol-description"]=["amerikai dollár / ausztrál dollár"],e.exports["#USDEUR-symbol-description"]=["amerikai dollár / euró"],e.exports["#USDGBP-symbol-description"]=["amerikai dollár / brit font"],e.exports["#USDNZD-symbol-description"]=["amerikai dollár / új-zélandi dollár"],e.exports["#UKOIL-symbol-description"]="CFDs on Crude Oil (Brent)",e.exports["#USOIL-symbol-description"]="CFDs on Crude Oil (WTI)",e.exports["#US30-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#BCHUSD-symbol-description"]=["bitcoin cash / amerikai dollár"],e.exports["#ETCUSD-symbol-description"]=["ethereum classic / amerikai dollár"],e.exports["#GOOG-symbol-description"]="Alphabet Inc (Google) Class C",e.exports["#LTCUSD-symbol-description"]=["litecoin / amerikai dollár"],e.exports["#XRPUSD-symbol-description"]=["ripple / amerikai dollár"],e.exports["#SP:SPX-symbol-description"]="S&P 500 Index",e.exports["#ETCBTC-symbol-description"]="Ethereum Classic / Bitcoin",e.exports["#ETHBTC-symbol-description"]="Ethereum / Bitcoin",e.exports["#XRPBTC-symbol-description"]=["Ripple / Bitcoin"],e.exports["#TVC:US30-symbol-description"]="US Government Bonds 30 YR",e.exports["#COMEX:SI1!-symbol-description"]="Silver Futures",e.exports["#BTGUSD-symbol-description"]=["bitcoin gold / amerikai dollár"],e.exports["#IOTUSD-symbol-description"]=["IOTA / amerikai dollár"],e.exports["#CME:BTC1!-symbol-description"]="Bitcoin CME Futures",
e.exports["#COMEX:GC1!-symbol-description"]="Gold Futures",e.exports["#CORNUSD-symbol-description"]="CFDs on Corn",e.exports["#COTUSD-symbol-description"]="CFDs on Cotton",e.exports["#DJ:DJA-symbol-description"]="Dow Jones Composite Average Index",e.exports["#DJ:DJI-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#ETHEUR-symbol-description"]="Ethereum / Euro",e.exports["#ETHGBP-symbol-description"]="Ethereum / British Pound",e.exports["#ETHJPY-symbol-description"]="Ethereum / Japanese Yen",e.exports["#EURNOK-symbol-description"]="Euro / Norwegian Krone",e.exports["#GBPPLN-symbol-description"]="British Pound / Polish Zloty",e.exports["#MOEX:BR1!-symbol-description"]="Brent Oil Futures",e.exports["#NYMEX:KG1!-symbol-description"]="Cotton Futures",e.exports["#NYMEX:PL1!-symbol-description"]="Platinum Futures",e.exports["#SOYBNUSD-symbol-description"]="CFDs on Soybeans",e.exports["#SUGARUSD-symbol-description"]="CFDs on Sugar",e.exports["#TVC:IXIC-symbol-description"]=["NASDAQ Composite Index"],e.exports["#TVC:RU-symbol-description"]="Russell 1000 Index",e.exports["#USDZAR-symbol-description"]=["amerikai dollár / dél-afrikai rand"],e.exports["#WHEATUSD-symbol-description"]="CFDs on Wheat",e.exports["#XRPEUR-symbol-description"]=["Ripple / Euro"],e.exports["#CBOT:S1!-symbol-description"]="Soybean Futures",e.exports["#SP:MID-symbol-description"]="S&P 400 Index",e.exports["#TSX:XCUUSD-symbol-description"]="CFDs on Copper",e.exports["#TVC:NYA-symbol-description"]="NYSE Composite Index",e.exports["#TVC:PLATINUM-symbol-description"]="CFDs on Platinum (US$ / OZ)",e.exports["#TVC:SSMI-symbol-description"]="Swiss Market Index",e.exports["#TVC:SXY-symbol-description"]="Swiss Franc Currency Index",e.exports["#MOEX:RI1!-symbol-description"]="RTS Index Futures",e.exports["#MOEX:MX1!-symbol-description"]="MICEX Index Futures",e.exports["#CBOE:BG1!-symbol-description"]="Bitcoin CBOE Futures",e.exports["#TVC:MY10-symbol-description"]="Malaysia Government Bonds 10 YR",e.exports["#CME:S61!-symbol-description"]="Swiss Franc Futures",e.exports["#TVC:DEU30-symbol-description"]="DAX Index",e.exports["#BCHEUR-symbol-description"]="Bitcoin Cash / Euro",e.exports["#TVC:ZXY-symbol-description"]=["Új-zélandi Dollár Devizaindex"],e.exports["#MIL:FTSEMIB-symbol-description"]="FTSE MIB Index",e.exports["#XETR:DAX-symbol-description"]="DAX Index",e.exports["#MOEX:IMOEX-symbol-description"]="MOEX Russia Index",e.exports["#FX:US30-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#MOEX:RUAL-symbol-description"]="United Company RUSAL PLC",e.exports["#MOEX:MX2!-symbol-description"]="MICEX Index Futures",e.exports["#NEOUSD-symbol-description"]=["NEO / amerikai dollár"],e.exports["#XMRUSD-symbol-description"]=["monero / amerikai dollár"],e.exports["#ZECUSD-symbol-description"]=["Zcash / amerikai dollár"],e.exports["#TVC:CAC-symbol-description"]=["CAC 40"],e.exports["#NASDAQ:ZS-symbol-description"]="Zscaler Inc",e.exports["#TVC:GB10Y-symbol-description"]=["UK Államkötvények 10 éves"],
e.exports["#TVC:AU10Y-symbol-description"]="Australia Government Bonds 10 YR Yield",e.exports["#TVC:CN10Y-symbol-description"]="China Government Bonds 10 YR Yield",e.exports["#TVC:DE10Y-symbol-description"]="German Government Bonds 10 YR Yield",e.exports["#TVC:ES10Y-symbol-description"]=["Spanyol Államkötvények 10 éves"],e.exports["#TVC:FR10Y-symbol-description"]="France Government Bonds 10 YR Yield",e.exports["#TVC:IN10Y-symbol-description"]=["Indiai államkötvények 10 éves"],e.exports["#TVC:IT10Y-symbol-description"]=["Olasz államkötvények 10 éves"],e.exports["#TVC:JP10Y-symbol-description"]=["japán államkötvények 10 éves"],e.exports["#TVC:KR10Y-symbol-description"]="Korea Government Bonds 10 YR Yield",e.exports["#TVC:MY10Y-symbol-description"]="Malaysia Government Bonds 10 YR Yield",e.exports["#TVC:PT10Y-symbol-description"]=["Portugál Államkötvények 10 éves"],e.exports["#TVC:TR10Y-symbol-description"]=["Turkey Government Bonds 10 YR"],e.exports["#TVC:US02Y-symbol-description"]=["US államkötvények 2 éves"],e.exports["#TVC:US05Y-symbol-description"]=["US államkötvények 5 éves"],e.exports["#TVC:US10Y-symbol-description"]=["US államkötvények 10 éves"],e.exports["#INDEX:TWII-symbol-description"]="Taiwan Weighted Index",e.exports["#CME:J61!-symbol-description"]="Japanese Yen Futures",e.exports["#CME_MINI:J71!-symbol-description"]="Japanese Yen E-mini Futures",e.exports["#CME_MINI:WM1!-symbol-description"]="E-micro Japanese Yen / U.S. Dollar Futures",e.exports["#CME:M61!-symbol-description"]="Mexican Peso Futures",e.exports["#CME:T61!-symbol-description"]="South African Rand Futures",e.exports["#CME:SK1!-symbol-description"]="Swedish Krona Futures",e.exports["#CME:QT1!-symbol-description"]="Chinese Renminbi / U.S. Dollar Futures",e.exports["#COMEX:AUP1!-symbol-description"]="Aluminum MW U.S. Transaction Premium Platts (25MT) Futures",e.exports["#CME:L61!-symbol-description"]="Brazilian Real Futures",e.exports["#CME:WP1!-symbol-description"]="Polish Zloty Futures",e.exports["#CME:N61!-symbol-description"]="New Zealand Dollar Futures",e.exports["#CME_MINI:MG1!-symbol-description"]="E-micro Australian Dollar / U.S. Dollar Futures",e.exports["#CME_MINI:WN1!-symbol-description"]="E-micro Swiss Franc / U.S. Dollar Futures",e.exports["#CME_MINI:MF1!-symbol-description"]="E-micro Euro / U.S. Dollar Futures",e.exports["#CME_MINI:E71!-symbol-description"]="Euro E-mini Futures",e.exports["#CBOT:ZK1!-symbol-description"]="Denatured Fuel Ethanol Futures",e.exports["#CME_MINI:MB1!-symbol-description"]="E-micro British Pound / U.S. Dollar Futures",e.exports["#NYMEX_MINI:QU1!-symbol-description"]="E-mini Gasoline Futures",e.exports["#NYMEX_MINI:QX1!-symbol-description"]="E-mini Heating Oil Futures",e.exports["#COMEX_MINI:QC1!-symbol-description"]="E-mini Copper Futures",e.exports["#NYMEX_MINI:QG1!-symbol-description"]="E-mini Natural Gas Futures",e.exports["#CME:E41!-symbol-description"]="U.S. Dollar / Turkish Lira Futures",e.exports["#COMEX_MINI:QI1!-symbol-description"]="Silver (Mini) Futures",
e.exports["#CME:DL1!-symbol-description"]="Milk, Class III Futures",e.exports["#NYMEX:UX1!-symbol-description"]="Uranium Futures",e.exports["#CBOT:BO1!-symbol-description"]="Soybean Oil Futures",e.exports["#CME:HE1!-symbol-description"]="Lean Hogs Futures",e.exports["#NYMEX:IAC1!-symbol-description"]="Newcastle Coal Futures",e.exports["#NYMEX_MINI:QM1!-symbol-description"]="E-mini Light Crude Oil Futures",e.exports["#NYMEX:JMJ1!-symbol-description"]="Mini Brent Financial Futures",e.exports["#COMEX:AEP1!-symbol-description"]="Aluminium European Premium Futures",e.exports["#CBOT:ZQ1!-symbol-description"]="30 Day Federal Funds Interest Rate Futures",e.exports["#CME:LE1!-symbol-description"]="Live Cattle Futures",e.exports["#CME:UP1!-symbol-description"]="Swiss Franc / Japanese Yen Futures",e.exports["#CBOT:ZN1!-symbol-description"]="10 Year T-Note Futures",e.exports["#CBOT:ZB1!-symbol-description"]="T-Bond Futures",e.exports["#CME:GF1!-symbol-description"]="Feeder Cattle Futures",e.exports["#CBOT:UD1!-symbol-description"]="Ultra T-Bond Futures",e.exports["#CME:I91!-symbol-description"]="CME Housing Futures — Washington DC",e.exports["#CBOT:ZO1!-symbol-description"]="Oat Futures",e.exports["#CBOT:ZM1!-symbol-description"]="Soybean Meal Futures",e.exports["#CBOT_MINI:XN1!-symbol-description"]="Corn Mini Futures",e.exports["#CBOT:ZC1!-symbol-description"]="Corn Futures",e.exports["#CME:LS1!-symbol-description"]="Lumber Futures",e.exports["#CBOT_MINI:XW1!-symbol-description"]="Wheat Mini Futures",e.exports["#CBOT_MINI:XK1!-symbol-description"]="Soybean Mini Futures",e.exports["#CBOT:ZS1!-symbol-description"]="Soybean Futures",e.exports["#NYMEX:PA1!-symbol-description"]="Palladium Futures",e.exports["#CME:FTU1!-symbol-description"]="E-mini FTSE 100 Index USD Futures",e.exports["#CBOT:ZR1!-symbol-description"]="Rice Futures",e.exports["#COMEX_MINI:GR1!-symbol-description"]="Gold (E-micro) Futures",e.exports["#COMEX_MINI:QO1!-symbol-description"]="Gold (Mini) Futures",e.exports["#CME_MINI:RL1!-symbol-description"]="E-mini Russell 1000 Futures",e.exports["#CME_MINI:EW1!-symbol-description"]="S&P 400 Midcap E-mini Futures",e.exports["#COMEX:LD1!-symbol-description"]="Lead Futures",e.exports["#CME_MINI:ES1!-symbol-description"]="S&P 500 E-mini Futures",e.exports["#TVC:SA40-symbol-description"]="South Africa Top 40 Index",e.exports["#BMV:ME-symbol-description"]="IPC Mexico Index",e.exports["#BCBA:IMV-symbol-description"]="MERVAL Index",e.exports["#HSI:HSI-symbol-description"]="Hang Seng Index",e.exports["#BVL:SPBLPGPT-symbol-description"]="S&P / BVL Peru General Index (PEN)",e.exports["#EGX:EGX30-symbol-description"]="EGX 30 Price Return Index",e.exports["#BVC:IGBC-symbol-description"]="Indice General de la Bolsa de Valores de Colombia",e.exports["#TWSE:TAIEX-symbol-description"]="Taiwan Capitalization Weighted Stock Index",e.exports["#QSE:GNRI-symbol-description"]="QE Index",e.exports["#BME:IBC-symbol-description"]="IBEX 35 Index",e.exports["#NZX:NZ50G-symbol-description"]="S&P / NZX 50 Index Gross",
e.exports["#SIX:SMI-symbol-description"]="Swiss Market Index",e.exports["#SZSE:399001-symbol-description"]="SZSE Component Index",e.exports["#TADAWUL:TASI-symbol-description"]="Tadawul All Shares Index",e.exports["#IDX:COMPOSITE-symbol-description"]="IDX Composite Index",e.exports["#EURONEXT:PX1-symbol-description"]="CAC 40 Index",e.exports["#OMXHEX:OMXH25-symbol-description"]="OMX Helsinki 25 Index",e.exports["#EURONEXT:BEL20-symbol-description"]="BEL 20 Index",e.exports["#TVC:STI-symbol-description"]="Straits Times Index",e.exports["#DFM:DFMGI-symbol-description"]="DFM Index",e.exports["#TVC:KOSPI-symbol-description"]="Korea Composite Stock Price Index",e.exports["#FTSEMYX:FBMKLCI-symbol-description"]="FTSE Bursa Malaysia KLCI Index",e.exports["#TASE:TA35-symbol-description"]="TA-35 Index",e.exports["#OMXSTO:OMXS30-symbol-description"]="OMX Stockholm 30 Index",e.exports["#OMXICE:OMXI8-symbol-description"]="OMX Iceland 8 Index",e.exports["#NSENG:NSE30-symbol-description"]="NSE 30 Index",e.exports["#BAHRAIN:BSEX-symbol-description"]="Bahrain All Share Index",e.exports["#OMXTSE:OMXTGI-symbol-description"]="OMX Tallinn GI",e.exports["#OMXCOP:OMXC25-symbol-description"]="OMX Copenhagen 25 Index",e.exports["#OMXRSE:OMXRGI-symbol-description"]="OMX Riga GI",e.exports["#BELEX:BELEX15-symbol-description"]="BELEX 15 Index",e.exports["#OMXVSE:OMXVGI-symbol-description"]="OMX Vilnius GI",e.exports["#EURONEXT:AEX-symbol-description"]="AEX Index",e.exports["#CBOE:VIX-symbol-description"]="Volatility S&P 500 Index",e.exports["#NASDAQ:XAU-symbol-description"]="PHLX Gold and Silver Sector Index",e.exports["#DJ:DJUSCL-symbol-description"]="Dow Jones U.S. Coal Index",e.exports["#DJ:DJCIKC-symbol-description"]="Dow Jones Commodity Index Coffee",e.exports["#DJ:DJCIEN-symbol-description"]="Dow Jones Commodity Index Energy",e.exports["#NASDAQ:OSX-symbol-description"]="PHLX Oil Service Sector Index",e.exports["#DJ:DJCISB-symbol-description"]="Dow Jones Commodity Index Sugar",e.exports["#DJ:DJCICC-symbol-description"]="Dow Jones Commodity Index Cocoa",e.exports["#DJ:DJCIGR-symbol-description"]="Dow Jones Commodity Index Grains",e.exports["#DJ:DJCIAGC-symbol-description"]="Dow Jones Commodity Index Agriculture Capped Component",e.exports["#DJ:DJCISI-symbol-description"]="Dow Jones Commodity Index Silver",e.exports["#DJ:DJCIIK-symbol-description"]="Dow Jones Commodity Index Nickel",e.exports["#NASDAQ:HGX-symbol-description"]="PHLX Housing Sector Index",e.exports["#DJ:DJCIGC-symbol-description"]="Dow Jones Commodity Index Gold",e.exports["#SP:SPGSCI-symbol-description"]="S&P Goldman Sachs Commodity Index",e.exports["#NASDAQ:UTY-symbol-description"]="PHLX Utility Sector Index",e.exports["#DJ:DJU-symbol-description"]="Dow Jones Utility Average Index",e.exports["#SP:SVX-symbol-description"]="S&P 500 Value Index",e.exports["#SP:OEX-symbol-description"]="S&P 100 Index",e.exports["#CBOE:OEX-symbol-description"]="S&P 100 Index",e.exports["#NASDAQ:SOX-symbol-description"]="Philadelphia Semiconductor Index",
e.exports["#RUSSELL:RUI-symbol-description"]="Russell 1000 Index",e.exports["#RUSSELL:RUA-symbol-description"]="Russell 3000 Index",e.exports["#RUSSELL:RUT-symbol-description"]="Russell 2000 Index",e.exports["#NYSE:XMI-symbol-description"]="NYSE ARCA Major Market Index",e.exports["#NYSE:XAX-symbol-description"]="AMEX Composite Index",e.exports["#NASDAQ:NDX-symbol-description"]="Nasdaq 100 Index",e.exports["#NASDAQ:IXIC-symbol-description"]="Nasdaq Composite Index",e.exports["#DJ:DJT-symbol-description"]="Dow Jones Transportation Average Index",e.exports["#NYSE:NYA-symbol-description"]="NYSE Composite Index",e.exports["#NYMEX:CJ1!-symbol-description"]="Cocoa Futures",e.exports["#USDILS-symbol-description"]="U.S. Dollar / Israeli Shekel",e.exports["#TSXV:F-symbol-description"]="Fiore Gold Inc",e.exports["#SIX:F-symbol-description"]="Ford Motor Company",e.exports["#BMV:F-symbol-description"]="Ford Motor Company",e.exports["#TWII-symbol-description"]="Taiwan Weighted Index",e.exports["#TVC:PL10Y-symbol-description"]="Poland Government Bonds 10 YR Yield",e.exports["#TVC:PL05Y-symbol-description"]="Poland Government Bonds 5 YR Yield",e.exports["#SET:GC-symbol-description"]="Global Connections Public Company",e.exports["#TSX:GC-symbol-description"]="Great Canadian Gaming Corporation",e.exports["#TVC:FTMIB-symbol-description"]="Milano Italia Borsa Index",e.exports["#OANDA:SPX500USD-symbol-description"]="S&P 500 Index",e.exports["#BMV:CT-symbol-description"]="China SX20 RT",e.exports["#TSXV:CT-symbol-description"]="Centenera Mining Corporation",e.exports["#BYBIT:ETHUSD-symbol-description"]="ETHUSD Perpetual Contract",e.exports["#BYBIT:XRPUSD-symbol-description"]="XRPUSD Perpetual Contract",e.exports["#BYBIT:BTCUSD-symbol-description"]="BTCUSD Perpetual Contract",e.exports["#BITMEX:ETHUSD-symbol-description"]="ETHUSD Perpetual Futures Contract",e.exports["#DERIBIT:BTCUSD-symbol-description"]="BTCUSD Perpetual Futures Contract",e.exports["#DERIBIT:ETHUSD-symbol-description"]="ETHUSD Perpetual Futures Contract",e.exports["#USDHUF-symbol-description"]="U.S. Dollar / Hungarian Forint",e.exports["#USDTHB-symbol-description"]="U.S. Dollar / Thai Baht",e.exports["#FOREXCOM:US2000-symbol-description"]="US Small Cap 2000",e.exports["#TSXV:PBR-symbol-description"]="Para Resources Inc",e.exports["#NYSE:SI-symbol-description"]="Silvergate Capital Corporation",e.exports["#NASDAQ:LE-symbol-description"]="Lands' End Inc",e.exports["#CME:CB1!-symbol-description"]="Butter Futures-Cash (Continuous: Current contract in front)",e.exports["#LSE:SCHO-symbol-description"]="Scholium Group Plc Ord 1P",e.exports["#NEO:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#NYSE:HE-symbol-description"]="Hawaiian Electric Industries",e.exports["#OMXCOP:SCHO-symbol-description"]="Schouw & Co A/S",e.exports["#TSX:HE-symbol-description"]="Hanwei Energy Services Corp.",e.exports["#BSE:ITI-symbol-description"]="ITI Ltd",e.exports["#NSE:ITI-symbol-description"]="Indian Telephone Industries Limited",
e.exports["#TSX:LS-symbol-description"]="Middlefield Healthcare & Life Sciences Dividend Fund",e.exports["#BITMEX:XBT-symbol-description"]="Bitcoin / U.S. Dollar Index",e.exports["#CME_MINI:RTY1!-symbol-description"]="E-Mini Russell 2000 Index Futures",e.exports["#CRYPTOCAP:TOTAL-symbol-description"]="Crypto Total Market Cap, $",e.exports["#ICEUS:DX1!-symbol-description"]="U.S. Dollar Index Futures",e.exports["#NYMEX:TT1!-symbol-description"]="Cotton Futures",e.exports["#PHEMEX:BTCUSD-symbol-description"]="BTC Perpetual Futures Contract",e.exports["#PHEMEX:ETHUSD-symbol-description"]="ETH Perpetual Futures Contract",e.exports["#PHEMEX:XRPUSD-symbol-description"]="XRP Perpetual Futures Contract",e.exports["#PHEMEX:LTCUSD-symbol-description"]="LTC Perpetual Futures Contract",e.exports["#BITCOKE:BCHUSD-symbol-description"]="BCH Quanto Swap",e.exports["#BITCOKE:BTCUSD-symbol-description"]="BTC Quanto Swap",e.exports["#BITCOKE:ETHUSD-symbol-description"]="ETH Quanto Swap",e.exports["#BITCOKE:LTCUSD-symbol-description"]="LTC Quanto Swap",e.exports["#TVC:CA10-symbol-description"]="Canadian Government Bonds, 10 YR",e.exports["#TVC:CA10Y-symbol-description"]="Canadian Government Bonds 10 YR Yield",e.exports["#TVC:ID10Y-symbol-description"]="Indonesia Government Bonds 10 YR Yield",e.exports["#TVC:NL10-symbol-description"]="Netherlands Government Bonds, 10 YR",e.exports["#TVC:NL10Y-symbol-description"]="Netherlands Government Bonds 10 YR Yield",e.exports["#TVC:NZ10-symbol-description"]="New Zealand Government Bonds, 10 YR",e.exports["#TVC:NZ10Y-symbol-description"]="New Zealand Government Bonds 10 YR Yield",e.exports["#SOLUSD-symbol-description"]="Solana / U.S. Dollar",e.exports["#LUNAUSD-symbol-description"]="Luna / U.S. Dollar",e.exports["#UNIUSD-symbol-description"]="Uniswap / U.S. Dollar",e.exports["#LTCBRL-symbol-description"]="Litecoin / Brazilian Real",e.exports["#ETCEUR-symbol-description"]="Ethereum Classic / Euro",e.exports["#ETHKRW-symbol-description"]="Ethereum / South Korean Won",e.exports["#BTCRUB-symbol-description"]="Bitcoin / Russian Ruble",e.exports["#BTCTHB-symbol-description"]="Bitcoin / Thai Baht",e.exports["#ETHTHB-symbol-description"]="Ethereum / Thai Baht",e.exports["#TVC:EU10YY-symbol-description"]="Euro Government Bonds 10 YR Yield",e.exports["#NASDAQ:LCID-symbol-description"]="Lucid Group, Inc.",e.exports["#TADAWUL:2370-symbol-description"]="Middle East Specialized Cables Co."}}]);